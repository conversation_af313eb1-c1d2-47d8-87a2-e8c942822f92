@echo off
REM 🚀 SARA - Deploy Super Simples para Windows

echo 🚀 SARA - Deploy Automático
echo ==========================

REM Verificar se Docker está instalado
docker --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker não encontrado!
    echo Instale o Docker Desktop primeiro
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker Compose não encontrado!
    echo Instale o Docker Desktop primeiro
    pause
    exit /b 1
)

REM Verificar se arquivo .env existe
if not exist ".env" (
    if exist ".env.simple" (
        echo 📋 Copiando configuração de exemplo...
        copy .env.simple .env
        echo.
        echo ⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!
        echo.
        echo Variáveis obrigatórias:
        echo - JWT_SECRET (mude para algo seguro)
        echo - CLOUDINARY_* (suas credenciais)
        echo - GIT_TOKEN (token do GitHub para auto-deploy)
        echo.
        pause
    ) else (
        echo ❌ Arquivo .env não encontrado!
        echo Crie um arquivo .env com as configurações necessárias
        pause
        exit /b 1
    )
)

REM Criar diretórios necessários
echo 📁 Criando diretórios...
if not exist "data" mkdir data
if not exist "data\database" mkdir data\database
if not exist "data\logs" mkdir data\logs
if not exist "data\backups" mkdir data\backups

REM Parar containers existentes
echo 🛑 Parando containers existentes...
docker-compose down 2>nul

REM Build e start
echo 🏗️  Buildando e iniciando SARA...
docker-compose up -d --build

REM Aguardar inicialização
echo ⏳ Aguardando inicialização...
timeout /t 30 /nobreak >nul

REM Verificar status
echo 📊 Verificando status...
curl -f http://localhost/health >nul 2>&1
if errorlevel 1 (
    echo ❌ Falha na inicialização!
    echo 📋 Verificar logs:
    docker-compose logs sara
    pause
) else (
    echo.
    echo 🎉 SARA está rodando!
    echo ==========================
    echo 🌐 Frontend: http://localhost
    echo 🔌 Backend:  http://localhost:3001
    echo 👤 Login:    <EMAIL>
    echo 🔑 Senha:    123456
    echo.
    echo 📋 Comandos úteis:
    echo docker-compose logs -f sara     # Ver logs
    echo docker-compose restart sara     # Reiniciar
    echo docker-compose down             # Parar
    echo.
    echo ⚠️  IMPORTANTE: Altere a senha padrão!
    echo.
    pause
)
