import React, { useState, useEffect } from 'react'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Settings,
  PiggyBank,
  Lightbulb,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Gauge,
  Zap,
  Award,
  Plus,
  FileText,
  Eye,
  Download,
  Upload,
  X
} from 'lucide-react'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  <PERSON><PERSON> as RechartsPieChart,
  Pie,
  Cell
} from 'recharts'
import GreenMeter from '../components/GreenMeter'
import EconomyTips from '../components/EconomyTips'
import ReportSetup from '../components/ReportSetup'
import EsverdeometroPanel from '../components/EsverdeometroPanel'
import ExpectedVsActualChart from '../components/ExpectedVsActualChart'
import GoalManager from '../components/GoalManager'
import api from '../services/api'
import toast from 'react-hot-toast'

function Reports({ selectedYear }) {
  const [reportConfig, setReportConfig] = useState(null)
  const [analysis, setAnalysis] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showSetup, setShowSetup] = useState(false)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYearState, setSelectedYearState] = useState(selectedYear || new Date().getFullYear())
  const [activeTab, setActiveTab] = useState('reports')

  useEffect(() => {
    checkReportConfig()
  }, [selectedYearState, selectedMonth])

  const checkReportConfig = async () => {
    try {
      setLoading(true)

      // Verificar se existe configuração para o mês/ano
      const configResponse = await api.get(`/reports/config/${selectedYearState}/${selectedMonth}`)

      if (configResponse.data.exists && configResponse.data.config.isCompleted) {
        setReportConfig(configResponse.data.config)
        // Buscar análise
        await fetchAnalysis()
      } else {
        setReportConfig(null)
        setAnalysis(null)
      }
    } catch (error) {
      console.error('Erro ao verificar configuração:', error)
      setReportConfig(null)
      setAnalysis(null)
    } finally {
      setLoading(false)
    }
  }

  const fetchAnalysis = async () => {
    try {
      const response = await api.get(`/reports/analysis/${selectedYearState}/${selectedMonth}`)
      setAnalysis(response.data)
    } catch (error) {
      console.error('Erro ao buscar análise:', error)
      if (error.response?.data?.needsSetup) {
        setReportConfig(null)
        setAnalysis(null)
      } else {
        toast.error('Erro ao carregar análise do relatório')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getOverallHealthColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getOverallHealthMessage = (score) => {
    if (score >= 80) return 'Excelente controle financeiro!'
    if (score >= 60) return 'Bom controle, mas pode melhorar'
    if (score >= 40) return 'Atenção necessária'
    return 'Situação crítica - ação urgente'
  }

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const handleSetupComplete = () => {
    setShowSetup(false)
    checkReportConfig()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Relatórios Inteligentes</h1>
              <p className="text-sm text-gray-600">Análise detalhada baseada em suas configurações personalizadas</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Seletor de Mês/Ano */}
            <div className="flex items-center gap-2">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {months.map((month, index) => (
                  <option key={index} value={index + 1}>{month}</option>
                ))}
              </select>

              <select
                value={selectedYearState}
                onChange={(e) => setSelectedYearState(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[2023, 2024, 2025].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Botões de Ação */}
            {reportConfig ? (
              <div className="flex gap-2">
                <button
                  onClick={() => setShowSetup(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <Settings className="h-4 w-4" />
                  Editar Configuração
                </button>
                <button
                  onClick={fetchAnalysis}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Eye className="h-4 w-4" />
                  Atualizar Análise
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowSetup(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Configurar Relatório
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Abas */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('reports')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'reports'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Relatórios
            </div>
          </button>
          <button
            onClick={() => setActiveTab('goals')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'goals'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Target className="h-4 w-4" />
              Objetivos
            </div>
          </button>
          <button
            onClick={() => setActiveTab('import-export')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'import-export'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Download className="h-4 w-4" />
              Importar/Exportar
            </div>
          </button>
        </div>
      </div>

      {/* Conteúdo Principal */}
      {activeTab === 'reports' ? (
        !reportConfig ? (
        /* Tela de Configuração Necessária */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Target className="h-10 w-10 text-blue-600" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Configure seu Relatório Personalizado
          </h2>

          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Para gerar relatórios inteligentes e personalizados, você precisa configurar suas preferências
            para <strong>{months[selectedMonth - 1]} de {selectedYearState}</strong>.
            Este processo leva apenas alguns minutos e pode ser feito em 3 passos simples.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">1. Definir Limites</h3>
              <p className="text-sm text-gray-600">Configure as porcentagens ideais para despesas, poupança e lazer</p>
            </div>

            <div className="bg-green-50 rounded-lg p-6 border border-green-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">2. Mapear Categorias</h3>
              <p className="text-sm text-gray-600">Selecione quais categorias, tags e contatos pertencem a cada limite</p>
            </div>

            <div className="bg-purple-50 rounded-lg p-6 border border-purple-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">3. Valores Esperados</h3>
              <p className="text-sm text-gray-600">Defina valores esperados para análises mais precisas</p>
            </div>
          </div>

          <button
            onClick={() => setShowSetup(true)}
            className="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
          >
            <Plus className="h-5 w-5" />
            Começar Configuração
          </button>
        </div>
      ) : reportConfig ? (
        /* Relatório Configurado */
        <>
          {/* Gráfico Esperado vs Real */}
          <ExpectedVsActualChart
            year={selectedYearState}
            month={selectedMonth}
          />

          {/* Painel de Esverdeômetro */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <EsverdeometroPanel
              year={selectedYearState}
              month={selectedMonth}
            />
          </div>
        </>
      ) : (
        /* Loading da Análise */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando análise do relatório...</p>
        </div>
        )
      ) : activeTab === 'goals' ? (
        /* Aba de Objetivos */
        <GoalManager />
      ) : (
        /* Aba de Importação/Exportação */
        <ImportExportPanel
          selectedYear={selectedYearState}
          selectedMonth={selectedMonth}
        />
      )}



      {/* Modal de Setup */}
      <ReportSetup
        isOpen={showSetup}
        onClose={() => setShowSetup(false)}
        year={selectedYearState}
        month={selectedMonth}
        onComplete={handleSetupComplete}
      />
    </div>
  )
}

// Componente de Importação/Exportação
const ImportExportPanel = ({ selectedYear, selectedMonth }) => {
  const [operationType, setOperationType] = useState('export')
  const [exportYear, setExportYear] = useState(selectedYear)
  const [exportMonth, setExportMonth] = useState(selectedMonth)
  const [validationResults, setValidationResults] = useState(null)
  const [selectedBank, setSelectedBank] = useState('')
  const [banks, setBanks] = useState([])
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [contacts, setContacts] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])


  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const handleExport = async (format) => {
    try {
      const response = await api.post('/reports/export', {
        year: exportYear,
        month: exportMonth,
        format: format
      }, {
        responseType: 'blob'
      })

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      // Definir nome do arquivo baseado no formato
      const monthStr = exportMonth.toString().padStart(2, '0')
      const extension = format.toLowerCase()
      link.setAttribute('download', `relatorio-transacoes-${exportYear}-${monthStr}.${extension}`)

      // Fazer download
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      toast.success(`Relatório ${format} exportado com sucesso!`)
    } catch (error) {
      console.error('Erro ao exportar:', error)
      toast.error(error.response?.data?.error || 'Erro ao exportar relatório')
    }
  }

  const handleDownloadTemplate = async (format) => {
    try {
      const response = await api.get(`/reports/import-template/${format}`, {
        responseType: 'blob'
      })

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      // Definir nome do arquivo
      const extension = format.toLowerCase()
      link.setAttribute('download', `modelo-importacao-sara.${extension}`)

      // Fazer download
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      toast.success(`Modelo ${format} baixado com sucesso!`)
    } catch (error) {
      console.error('Erro ao baixar template:', error)
      toast.error('Erro ao baixar modelo de importação')
    }
  }

  const [selectedFile, setSelectedFile] = useState(null)
  const [showImportPreview, setShowImportPreview] = useState(false)
  const [importData, setImportData] = useState([])

  const handleFileSelect = (event) => {
    const file = event.target.files[0]
    if (file) {
      setSelectedFile(file)
      handleFilePreview(file)
    }
  }

  const handleFilePreview = async (file) => {
    try {
      const text = await file.text()
      let rows = []

      if (file.name.endsWith('.csv')) {
        // Parse CSV
        const lines = text.split('\n').filter(line => line.trim())
        const headers = lines[0].split(';').map(h => h.trim().replace(/"/g, ''))

        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(';').map(v => v.trim().replace(/"/g, ''))
          if (values.length >= headers.length) {
            const row = {}
            headers.forEach((header, index) => {
              row[header] = values[index] || ''
            })
            rows.push(row)
          }
        }
      } else {
        toast.error('Formato de arquivo não suportado. Use CSV.')
        return
      }

      // Validar dados e buscar entidades existentes
      await validateImportData(rows)
      setImportData(rows)
      setShowImportPreview(true)
    } catch (error) {
      console.error('Erro ao processar arquivo:', error)
      toast.error('Erro ao processar arquivo')
    }
  }

  const validateImportData = async (data) => {
    try {
      // Buscar dados existentes
      const [banksResponse, categoriesResponse, tagsResponse, contactsResponse, paymentMethodsResponse] = await Promise.all([
        api.get('/banks'),
        api.get('/categories'),
        api.get('/tags'),
        api.get('/contacts-tx'),
        api.get('/payment-methods')
      ])

      setBanks(banksResponse.data)
      setCategories(categoriesResponse.data)
      setTags(tagsResponse.data)
      setContacts(contactsResponse.data)
      setPaymentMethods(paymentMethodsResponse.data)

      // Validar cada linha
      const validation = {
        totalTransactions: data.length,
        validTransactions: 0,
        invalidTransactions: 0,
        totalImpact: 0,
        bankImpact: {},
        missingCategories: new Set(),
        missingTags: new Set(),
        missingContacts: new Set(),
        foundCategories: new Set(),
        foundTags: new Set(),
        foundContacts: new Set(),
        errors: []
      }

      data.forEach((row, index) => {
        const errors = []

        // Validar campos obrigatórios
        if (!row.Data) errors.push('Data é obrigatória')
        if (!row.Descrição) errors.push('Descrição é obrigatória')
        if (!row.Tipo) errors.push('Tipo é obrigatório')
        if (!row.Valor) errors.push('Valor é obrigatório')
        if (!row.Banco) errors.push('Banco é obrigatório')

        // Validar formato da data
        if (row.Data && !row.Data.match(/^\d{2}\/\d{2}\/\d{4}$/)) {
          errors.push('Data deve estar no formato DD/MM/AAAA')
        }

        // Validar tipo
        if (row.Tipo && !['INCOME', 'EXPENSE', 'INVESTMENT', 'LOAN'].includes(row.Tipo.toUpperCase())) {
          errors.push('Tipo deve ser INCOME, EXPENSE, INVESTMENT ou LOAN')
        }

        // Validar valor
        const amount = parseFloat(row.Valor?.replace(',', '.'))
        if (isNaN(amount)) {
          errors.push('Valor deve ser numérico')
        } else {
          // Calcular impacto no saldo
          const impact = row.Tipo?.toUpperCase() === 'INCOME' ? amount : -amount
          validation.totalImpact += impact

          if (row.Banco) {
            if (!validation.bankImpact[row.Banco]) {
              validation.bankImpact[row.Banco] = 0
            }
            validation.bankImpact[row.Banco] += impact
          }
        }

        // Verificar banco
        if (row.Banco) {
          const bankExists = banksResponse.data.find(b =>
            b.name.toLowerCase() === row.Banco.toLowerCase()
          )
          if (!bankExists) {
            errors.push('Banco não encontrado no sistema')
          }
        } else {
          errors.push('Banco é obrigatório')
        }

        // Verificar categoria
        if (row.Categoria) {
          const categoryExists = categoriesResponse.data.find(c =>
            c.name.toLowerCase() === row.Categoria.toLowerCase()
          )
          if (categoryExists) {
            validation.foundCategories.add(row.Categoria)
          } else {
            validation.missingCategories.add(row.Categoria)
          }
        }

        // Verificar contato
        if (row.Contato) {
          const contactExists = contactsResponse.data.find(c =>
            c.name.toLowerCase() === row.Contato.toLowerCase()
          )
          if (contactExists) {
            validation.foundContacts.add(row.Contato)
          } else {
            validation.missingContacts.add(row.Contato)
          }
        }

        // Verificar tags
        if (row.Tags) {
          const tagNames = row.Tags.split(',').map(t => t.trim())
          tagNames.forEach(tagName => {
            const tagExists = tagsResponse.data.find(t =>
              t.name.toLowerCase() === tagName.toLowerCase()
            )
            if (tagExists) {
              validation.foundTags.add(tagName)
            } else {
              validation.missingTags.add(tagName)
            }
          })
        }

        // Verificar forma de pagamento (opcional)
        if (row['Forma de Pagamento']) {
          const paymentMethodExists = paymentMethodsResponse.data.find(pm =>
            pm.name.toLowerCase() === row['Forma de Pagamento'].toLowerCase()
          )
          if (!paymentMethodExists) {
            errors.push('Forma de pagamento não encontrada no sistema')
          }
        }

        if (errors.length === 0) {
          validation.validTransactions++
        } else {
          validation.invalidTransactions++
          validation.errors.push({
            line: index + 2, // +2 porque começamos do índice 0 e temos header
            errors
          })
        }
      })

      setValidationResults(validation)
    } catch (error) {
      console.error('Erro ao validar dados:', error)
      toast.error('Erro ao validar dados de importação')
    }
  }

  const handleImport = () => {
    // Trigger file input
    document.getElementById('import-file-input').click()
  }

  const handleConfirmImport = async (dataToImport = null) => {
    try {
      const transactionsToImport = dataToImport || importData

      const response = await api.post('/transactions/import', {
        transactions: transactionsToImport,
        allowedCreations: dataToImport?.[0]?.allowedCreations || {
          categories: Array.from(validationResults?.missingCategories || []),
          tags: Array.from(validationResults?.missingTags || []),
          contacts: Array.from(validationResults?.missingContacts || [])
        },
        selectedBanksForImpact: dataToImport?.[0]?.selectedBanksForImpact || []
      })

      toast.success(`${response.data.imported} transações importadas com sucesso!`)
      if (response.data.errors && response.data.errors.length > 0) {
        console.warn('Erros durante importação:', response.data.errors)
        toast.warning(`${response.data.errors.length} transações com problemas`)
      }

      setShowImportPreview(false)
      setImportData([])
      setSelectedFile(null)
      setValidationResults(null)
    } catch (error) {
      console.error('Erro ao importar transações:', error)
      toast.error(error.response?.data?.error || 'Erro ao importar transações')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
            <Download className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Importar/Exportar Dados</h2>
            <p className="text-gray-600">Gerencie seus dados de relatórios e configurações</p>
          </div>
        </div>
      </div>

      {/* Seletor de Operação */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tipo de Operação</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button
            onClick={() => setOperationType('export')}
            className={`p-4 rounded-lg border-2 transition-all ${
              operationType === 'export'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-3">
              <Download className="h-6 w-6" />
              <div className="text-left">
                <h4 className="font-semibold">Exportar Dados</h4>
                <p className="text-sm text-gray-600">Baixar configurações e relatórios</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => setOperationType('import')}
            className={`p-4 rounded-lg border-2 transition-all ${
              operationType === 'import'
                ? 'border-green-500 bg-green-50 text-green-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-3">
              <Upload className="h-6 w-6" />
              <div className="text-left">
                <h4 className="font-semibold">Importar Dados</h4>
                <p className="text-sm text-gray-600">Carregar configurações salvas</p>
              </div>
            </div>
          </button>
        </div>

        {/* Ações */}
        {operationType === 'export' ? (
          <>
            {/* Seleção de Período - Apenas para Exportação */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h4 className="font-medium text-gray-900 mb-3">Selecionar Período</h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Mês</label>
                  <select
                    value={exportMonth}
                    onChange={(e) => setExportMonth(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {months.map((month, index) => (
                      <option key={index} value={index + 1}>{month}</option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Ano</label>
                  <select
                    value={exportYear}
                    onChange={(e) => setExportYear(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    {[2023, 2024, 2025].map(year => (
                      <option key={year} value={year}>{year}</option>
                    ))}
                  </select>
                </div>
              </div>
            </div>
          </>
        ) : null}

        {/* Opções de Exportação/Importação */}
        {operationType === 'export' ? (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Opções de Exportação</h4>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button
                onClick={() => handleExport('PDF')}
                className="flex items-center justify-center gap-2 p-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                <FileText className="h-4 w-4" />
                PDF
              </button>

              <button
                onClick={() => handleExport('XLSX')}
                className="flex items-center justify-center gap-2 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                XLSX
              </button>

              <button
                onClick={() => handleExport('XLS')}
                className="flex items-center justify-center gap-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                XLS
              </button>

              <button
                onClick={() => handleExport('CSV')}
                className="flex items-center justify-center gap-2 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                CSV
              </button>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <h5 className="font-medium text-blue-900 mb-2">📋 O que será exportado:</h5>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Configurações de porcentagens para {months[exportMonth - 1]} de {exportYear}</li>
                <li>• Mapeamentos de categorias, tags e contatos</li>
                <li>• Valores esperados configurados</li>
                <li>• Dados de análise e relatórios gerados</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <h4 className="font-medium text-gray-900">Baixar Modelos de Importação</h4>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100 mb-4">
              <h5 className="font-medium text-blue-900 mb-2">📋 Primeiro, baixe um modelo:</h5>
              <p className="text-sm text-blue-800 mb-3">
                Baixe um modelo com exemplos e preencha com seus dados antes de importar.
              </p>

              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => handleDownloadTemplate('CSV')}
                  className="flex items-center justify-center gap-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  CSV
                </button>
                <button
                  onClick={() => handleDownloadTemplate('XLSX')}
                  className="flex items-center justify-center gap-2 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  XLSX
                </button>
                <button
                  onClick={() => handleDownloadTemplate('XLS')}
                  className="flex items-center justify-center gap-2 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  XLS
                </button>
              </div>
            </div>

            <h4 className="font-medium text-gray-900">Importar Arquivo Preenchido</h4>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">Arraste um arquivo aqui ou clique para selecionar</p>

              <input
                id="import-file-input"
                type="file"
                accept=".csv,.xlsx,.xls"
                onChange={handleFileSelect}
                className="hidden"
              />

              <button
                onClick={handleImport}
                className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mx-auto"
              >
                <Upload className="h-5 w-5" />
                Selecionar Arquivo
              </button>

              {selectedFile && (
                <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <p className="text-sm text-blue-800">
                    📄 Arquivo selecionado: <strong>{selectedFile.name}</strong>
                  </p>
                  <p className="text-xs text-blue-600 mt-1">
                    Tamanho: {(selectedFile.size / 1024).toFixed(1)} KB
                  </p>
                </div>
              )}
            </div>

            <div className="bg-green-50 rounded-lg p-4 border border-green-100">
              <h5 className="font-medium text-green-900 mb-2">📁 Campos obrigatórios no arquivo:</h5>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• <strong>Data:</strong> Formato DD/MM/AAAA</li>
                <li>• <strong>Descrição:</strong> Nome da transação</li>
                <li>• <strong>Tipo:</strong> INCOME, EXPENSE, INVESTMENT ou LOAN</li>
                <li>• <strong>Valor:</strong> Valor numérico (use vírgula para decimais)</li>
                <li>• <strong>Categoria:</strong> Nome da categoria (será criada se não existir)</li>
                <li>• <strong>Banco:</strong> Nome do banco</li>
                <li>• <strong>Contato:</strong> Nome do contato (opcional)</li>
                <li>• <strong>Tags:</strong> Separadas por vírgula (opcional)</li>
              </ul>
            </div>

            <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-100">
              <h5 className="font-medium text-yellow-900 mb-2">⚠️ Importante:</h5>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• O sistema buscará categorias, contatos e tags pelo nome</li>
                <li>• Se não encontrar, criará automaticamente</li>
                <li>• Você poderá revisar os dados antes de salvar</li>
                <li>• Máximo de 1000 transações por importação</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Modal de Pré-visualização */}
      {showImportPreview && (
        <ImportPreviewModal
          isOpen={showImportPreview}
          onClose={() => setShowImportPreview(false)}
          importData={importData}
          validationResults={validationResults}
          selectedBank={selectedBank}
          setSelectedBank={setSelectedBank}
          banks={banks}
          onConfirmImport={handleConfirmImport}
          setImportData={setImportData}
          categories={categories}
          tags={tags}
          contacts={contacts}
          paymentMethods={paymentMethods}
        />
      )}
    </div>
  )
}

// Componente do Modal de Pré-visualização
const ImportPreviewModal = ({
  isOpen,
  onClose,
  importData,
  validationResults,
  selectedBank,
  setSelectedBank,
  banks,
  onConfirmImport,
  setImportData,
  categories,
  tags,
  contacts,
  paymentMethods
}) => {
  const [filteredData, setFilteredData] = useState([])
  const [filters, setFilters] = useState({
    descricao: '',
    data: '',
    tipo: '',
    categoria: '',
    tags: ''
  })
  const [editingRow, setEditingRow] = useState(null)
  const [editData, setEditData] = useState({})
  const [selectedRows, setSelectedRows] = useState(new Set())
  const [excludedRows, setExcludedRows] = useState(new Set())
  const [allowedCreations, setAllowedCreations] = useState({
    categories: new Set(),
    tags: new Set(),
    contacts: new Set()
  })
  const [selectedBanksForImpact, setSelectedBanksForImpact] = useState(new Set())
  const [bankValidation, setBankValidation] = useState({})

  if (!isOpen) return null

  // Inicializar dados filtrados
  useEffect(() => {
    if (importData.length > 0) {
      setFilteredData(importData)
      // Inicializar todas as criações como permitidas
      if (validationResults) {
        setAllowedCreations({
          categories: new Set(validationResults.missingCategories),
          tags: new Set(validationResults.missingTags),
          contacts: new Set(validationResults.missingContacts)
        })
      }

      // Selecionar automaticamente transações válidas (verde e amarelo)
      const validRows = new Set()
      importData.forEach((_, index) => {
        const hasErrors = validationResults?.errors?.some(error => error.line === index + 2)
        const bankValid = bankValidation[index]?.exists !== false

        if (!hasErrors && bankValid) {
          validRows.add(index)
        }
      })
      setSelectedRows(validRows)

      // Inicializar seleção de bancos para impacto
      const bankImpacts = calculateBankImpacts()
      const bankIds = new Set(Object.keys(bankImpacts))
      setSelectedBanksForImpact(bankIds)
    }
  }, [importData, validationResults, bankValidation])

  // Aplicar filtros
  useEffect(() => {
    let filtered = importData.filter(row => {
      return (
        (!filters.descricao || row.Descrição?.toLowerCase().includes(filters.descricao.toLowerCase())) &&
        (!filters.data || row.Data?.includes(filters.data)) &&
        (!filters.tipo || row.Tipo?.toLowerCase().includes(filters.tipo.toLowerCase())) &&
        (!filters.categoria || row.Categoria?.toLowerCase().includes(filters.categoria.toLowerCase())) &&
        (!filters.tags || row.Tags?.toLowerCase().includes(filters.tags.toLowerCase()))
      )
    })
    setFilteredData(filtered)
  }, [filters, importData])

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  // Validar bancos nas transações
  useEffect(() => {
    if (importData.length > 0 && banks.length > 0) {
      const bankValidationMap = {}
      importData.forEach((row, index) => {
        if (row.Banco) {
          const bankExists = banks.find(b =>
            b.name.toLowerCase() === row.Banco.toLowerCase()
          )
          bankValidationMap[index] = {
            exists: !!bankExists,
            bank: bankExists || null
          }
        } else {
          bankValidationMap[index] = {
            exists: false,
            bank: null
          }
        }
      })
      setBankValidation(bankValidationMap)
    }
  }, [importData, banks])

  // Determinar cor da linha
  const getRowColor = (row, index) => {
    if (excludedRows.has(index)) {
      return 'bg-gray-100 text-gray-500 opacity-60' // Excluída
    }

    const isSelected = selectedRows.has(index)
    const hasErrors = validationResults?.errors?.some(error => error.line === index + 2)
    const bankValid = bankValidation[index]?.exists !== false

    if (hasErrors || !bankValid) {
      return isSelected
        ? 'bg-red-100 border-red-300 border-l-4 border-l-red-500' // Inválida selecionada
        : 'bg-red-50 border-red-200 opacity-60' // Inválida não selecionada
    }

    const willCreateCategory = row.Categoria && validationResults?.missingCategories?.has(row.Categoria)
    const willCreateContact = row.Contato && validationResults?.missingContacts?.has(row.Contato)
    const willCreateTags = row.Tags && row.Tags.split(',').some(tag =>
      validationResults?.missingTags?.has(tag.trim())
    )

    if (willCreateCategory || willCreateContact || willCreateTags) {
      return isSelected
        ? 'bg-yellow-100 border-yellow-300 border-l-4 border-l-yellow-500' // Válida com criações selecionada
        : 'bg-yellow-50 border-yellow-200 opacity-60' // Válida com criações não selecionada
    }

    return isSelected
      ? 'bg-green-100 border-green-300 border-l-4 border-l-green-500' // Totalmente válida selecionada
      : 'bg-green-50 border-green-200 opacity-60' // Totalmente válida não selecionada
  }

  // Calcular impacto por banco
  const calculateBankImpacts = () => {
    const impacts = {}
    importData.forEach((row, index) => {
      if (excludedRows.has(index)) return

      const bankInfo = bankValidation[index]
      if (bankInfo?.exists && bankInfo.bank) {
        const amount = parseFloat(row.Valor?.replace(',', '.')) || 0
        const impact = row.Tipo?.toUpperCase() === 'INCOME' ? amount : -amount

        if (!impacts[bankInfo.bank.id]) {
          impacts[bankInfo.bank.id] = {
            bank: bankInfo.bank,
            impact: 0,
            transactions: 0
          }
        }
        impacts[bankInfo.bank.id].impact += impact
        impacts[bankInfo.bank.id].transactions += 1
      }
    })
    return impacts
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999] p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-green-50 p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-gray-900">Pré-visualização da Importação</h2>
              <p className="text-gray-600">Revise os dados antes de confirmar a importação</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {validationResults && (
            <>
              {/* Resumo da Validação */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                {/* Card Resumo */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-xl p-6 border border-blue-200 shadow-sm">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">📊</span>
                    </div>
                    <h3 className="font-bold text-blue-900">Resumo Geral</h3>
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-blue-700">Total:</span>
                      <span className="font-semibold text-blue-900">{importData.length}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-green-700">Selecionadas:</span>
                      <span className="font-semibold text-green-800">{importData.length - excludedRows.size}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-600">Excluídas:</span>
                      <span className="font-semibold text-gray-700">{excludedRows.size}</span>
                    </div>
                  </div>
                </div>

                {/* Card Impacto Financeiro */}
                <div className="bg-gradient-to-br from-green-50 to-emerald-100 rounded-xl p-6 border border-green-200 shadow-sm">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                        <span className="text-white text-lg">💰</span>
                      </div>
                      <h3 className="font-bold text-green-900">Impacto Financeiro</h3>
                    </div>
                    <button
                      onClick={() => {
                        const bankImpacts = calculateBankImpacts()
                        const allBankIds = new Set(Object.keys(bankImpacts))
                        if (selectedBanksForImpact.size === allBankIds.size) {
                          setSelectedBanksForImpact(new Set())
                        } else {
                          setSelectedBanksForImpact(allBankIds)
                        }
                      }}
                      className="text-green-700 hover:text-green-900 text-sm font-medium"
                    >
                      {selectedBanksForImpact.size === Object.keys(calculateBankImpacts()).length ? 'Desmarcar todos' : 'Selecionar todos'}
                    </button>
                  </div>

                  <div className="mb-4">
                    <p className="text-sm font-semibold text-green-700 mb-3 flex items-center gap-2">
                      <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      Selecionados ({selectedBanksForImpact.size} de {Object.keys(calculateBankImpacts()).length} bancos)
                    </p>
                    <div className="flex flex-wrap gap-2">
                      {Object.values(calculateBankImpacts()).map(({ bank, impact, transactions }) => (
                        <div
                          key={bank.id}
                          onClick={() => {
                            const newSet = new Set(selectedBanksForImpact)
                            if (selectedBanksForImpact.has(bank.id)) {
                              newSet.delete(bank.id)
                            } else {
                              newSet.add(bank.id)
                            }
                            setSelectedBanksForImpact(newSet)
                          }}
                          className={`px-3 py-2 rounded-lg text-sm font-medium border transition-colors cursor-pointer ${
                            selectedBanksForImpact.has(bank.id)
                              ? impact >= 0
                                ? 'bg-green-100 text-green-800 border-green-200 border-l-4 border-l-green-500'
                                : 'bg-red-100 text-red-800 border-red-200 border-l-4 border-l-red-500'
                              : 'bg-gray-100 text-gray-600 border-gray-200 opacity-60'
                          }`}
                        >
                          <div className="flex items-center gap-2">
                            {selectedBanksForImpact.has(bank.id) ? (
                              <span className="w-3 h-3 bg-green-500 text-white rounded-full flex items-center justify-center text-xs">✓</span>
                            ) : (
                              <span className="w-3 h-3 border border-gray-400 rounded-full"></span>
                            )}
                            <span className="font-bold">{bank.name}</span>
                          </div>
                          <div className="text-xs mt-1">
                            {formatCurrency(impact)} • {transactions} trans.
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Card Filtros Rápidos */}
                <div className="bg-gradient-to-br from-purple-50 to-violet-100 rounded-xl p-6 border border-purple-200 shadow-sm">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">🎯</span>
                    </div>
                    <h3 className="font-bold text-purple-900">Filtros Rápidos</h3>
                  </div>
                  <div className="space-y-3">
                    <button
                      onClick={() => {
                        const expenseRows = new Set()
                        importData.forEach((row, index) => {
                          if (row.Tipo?.toUpperCase() === 'EXPENSE') {
                            expenseRows.add(index)
                          }
                        })
                        setSelectedRows(expenseRows)
                      }}
                      className="w-full px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm font-medium"
                    >
                      💸 Apenas Despesas
                    </button>
                    <button
                      onClick={() => {
                        const incomeRows = new Set()
                        importData.forEach((row, index) => {
                          if (row.Tipo?.toUpperCase() === 'INCOME') {
                            incomeRows.add(index)
                          }
                        })
                        setSelectedRows(incomeRows)
                      }}
                      className="w-full px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium"
                    >
                      💰 Apenas Receitas
                    </button>
                    <button
                      onClick={() => {
                        const investmentRows = new Set()
                        importData.forEach((row, index) => {
                          if (row.Tipo?.toUpperCase() === 'INVESTMENT') {
                            investmentRows.add(index)
                          }
                        })
                        setSelectedRows(investmentRows)
                      }}
                      className="w-full px-3 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors text-sm font-medium"
                    >
                      📈 Investimentos/Poupanças
                    </button>
                    <button
                      onClick={() => {
                        setSelectedRows(new Set())
                        setExcludedRows(new Set())
                      }}
                      className="w-full px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium"
                    >
                      🗑️ Limpar Seleção
                    </button>
                  </div>
                </div>

                {/* Card Ações */}
                <div className="bg-gradient-to-br from-orange-50 to-amber-100 rounded-xl p-6 border border-orange-200 shadow-sm">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                      <span className="text-white text-lg">🔧</span>
                    </div>
                    <h3 className="font-bold text-orange-900">Ações</h3>
                  </div>
                  <div className="space-y-2">
                    <button
                      onClick={() => {
                        const newExcluded = new Set(excludedRows)
                        selectedRows.forEach(index => newExcluded.add(index))
                        setExcludedRows(newExcluded)
                        setSelectedRows(new Set())
                      }}
                      className="w-full px-3 py-2 bg-red-500 text-white rounded-lg hover:bg-red-600 transition-colors text-sm font-medium disabled:bg-gray-300 disabled:cursor-not-allowed"
                      disabled={selectedRows.size === 0}
                    >
                      Excluir Selecionadas
                    </button>
                    <button
                      onClick={() => {
                        const newExcluded = new Set(excludedRows)
                        selectedRows.forEach(index => newExcluded.delete(index))
                        setExcludedRows(newExcluded)
                        setSelectedRows(new Set())
                      }}
                      className="w-full px-3 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium disabled:bg-gray-300 disabled:cursor-not-allowed"
                      disabled={selectedRows.size === 0}
                    >
                      Incluir Selecionadas
                    </button>
                    <div className="border-t border-orange-200 pt-2 mt-3">
                      <button
                        onClick={() => {
                          const allIndexes = new Set()
                          importData.forEach((_, index) => allIndexes.add(index))
                          setExcludedRows(allIndexes)
                          setSelectedRows(new Set())
                        }}
                        className="w-full px-3 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors text-sm font-medium"
                      >
                        🗑️ Excluir Tudo
                      </button>
                      <button
                        onClick={() => {
                          setExcludedRows(new Set())
                          const allIndexes = new Set()
                          importData.forEach((_, index) => allIndexes.add(index))
                          setSelectedRows(allIndexes)
                        }}
                        className="w-full px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium mt-2"
                      >
                        ✅ Incluir Tudo
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Entidades Encontradas/Não Encontradas */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
                {/* Categorias */}
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-blue-500 to-blue-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <span className="text-white text-lg">📂</span>
                        </div>
                        <h4 className="font-bold text-white">Categorias</h4>
                      </div>
                      {validationResults.missingCategories.size > 0 && (
                        <button
                          onClick={() => {
                            const allSelected = Array.from(validationResults.missingCategories).every(cat =>
                              allowedCreations.categories.has(cat)
                            )
                            const newSet = new Set(allowedCreations.categories)
                            if (allSelected) {
                              validationResults.missingCategories.forEach(cat => newSet.delete(cat))
                            } else {
                              validationResults.missingCategories.forEach(cat => newSet.add(cat))
                            }
                            setAllowedCreations(prev => ({ ...prev, categories: newSet }))
                          }}
                          className="text-white/80 hover:text-white text-sm font-medium"
                        >
                          {Array.from(validationResults.missingCategories).every(cat =>
                            allowedCreations.categories.has(cat)
                          ) ? 'Limpar todas' : 'Selecionar todas'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="p-6">
                    {validationResults.foundCategories.size > 0 && (
                      <div className="mb-6">
                        <p className="text-sm font-semibold text-green-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Encontradas ({validationResults.foundCategories.size})
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.foundCategories).map(cat => (
                            <span key={cat} className="px-3 py-1.5 bg-green-100 text-green-800 rounded-full text-sm font-medium border border-green-200">
                              {cat}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {validationResults.missingCategories.size > 0 && (
                      <div>
                        <p className="text-sm font-semibold text-blue-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-blue-500 rounded-full"></span>
                          Selecionadas ({Array.from(validationResults.missingCategories).filter(cat => allowedCreations.categories.has(cat)).length})
                        </p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {Array.from(validationResults.missingCategories).filter(cat => allowedCreations.categories.has(cat)).map(cat => (
                            <span
                              key={cat}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.categories)
                                newSet.delete(cat)
                                setAllowedCreations(prev => ({ ...prev, categories: newSet }))
                              }}
                              className="px-3 py-1.5 bg-blue-100 text-blue-800 rounded-full text-sm font-medium border border-blue-200 cursor-pointer hover:bg-blue-200 transition-colors flex items-center gap-1"
                            >
                              {cat} <span className="text-blue-600">×</span>
                            </span>
                          ))}
                        </div>

                        <p className="text-sm font-semibold text-gray-600 mb-3">Disponíveis</p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.missingCategories).filter(cat => !allowedCreations.categories.has(cat)).map(cat => (
                            <span
                              key={cat}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.categories)
                                newSet.add(cat)
                                setAllowedCreations(prev => ({ ...prev, categories: newSet }))
                              }}
                              className="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-full text-sm font-medium border border-gray-200 cursor-pointer hover:bg-gray-200 transition-colors"
                            >
                              {cat}
                            </span>
                          ))}
                        </div>
                        {Array.from(validationResults.missingCategories).filter(cat => !allowedCreations.categories.has(cat)).length === 0 && (
                          <p className="text-sm text-gray-500 italic">Todas as categorias foram selecionadas</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Tags */}
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-purple-500 to-purple-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <span className="text-white text-lg">🏷️</span>
                        </div>
                        <h4 className="font-bold text-white">Tags</h4>
                      </div>
                      {validationResults.missingTags.size > 0 && (
                        <button
                          onClick={() => {
                            const allSelected = Array.from(validationResults.missingTags).every(tag =>
                              allowedCreations.tags.has(tag)
                            )
                            const newSet = new Set(allowedCreations.tags)
                            if (allSelected) {
                              validationResults.missingTags.forEach(tag => newSet.delete(tag))
                            } else {
                              validationResults.missingTags.forEach(tag => newSet.add(tag))
                            }
                            setAllowedCreations(prev => ({ ...prev, tags: newSet }))
                          }}
                          className="text-white/80 hover:text-white text-sm font-medium"
                        >
                          {Array.from(validationResults.missingTags).every(tag =>
                            allowedCreations.tags.has(tag)
                          ) ? 'Limpar todas' : 'Selecionar todas'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="p-6">
                    {validationResults.foundTags.size > 0 && (
                      <div className="mb-6">
                        <p className="text-sm font-semibold text-green-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Encontradas ({validationResults.foundTags.size})
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.foundTags).map(tag => (
                            <span key={tag} className="px-3 py-1.5 bg-green-100 text-green-800 rounded-full text-sm font-medium border border-green-200">
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {validationResults.missingTags.size > 0 && (
                      <div>
                        <p className="text-sm font-semibold text-purple-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-purple-500 rounded-full"></span>
                          Selecionadas ({Array.from(validationResults.missingTags).filter(tag => allowedCreations.tags.has(tag)).length})
                        </p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {Array.from(validationResults.missingTags).filter(tag => allowedCreations.tags.has(tag)).map(tag => (
                            <span
                              key={tag}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.tags)
                                newSet.delete(tag)
                                setAllowedCreations(prev => ({ ...prev, tags: newSet }))
                              }}
                              className="px-3 py-1.5 bg-purple-100 text-purple-800 rounded-full text-sm font-medium border border-purple-200 cursor-pointer hover:bg-purple-200 transition-colors flex items-center gap-1"
                            >
                              {tag} <span className="text-purple-600">×</span>
                            </span>
                          ))}
                        </div>

                        <p className="text-sm font-semibold text-gray-600 mb-3">Disponíveis</p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.missingTags).filter(tag => !allowedCreations.tags.has(tag)).map(tag => (
                            <span
                              key={tag}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.tags)
                                newSet.add(tag)
                                setAllowedCreations(prev => ({ ...prev, tags: newSet }))
                              }}
                              className="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-full text-sm font-medium border border-gray-200 cursor-pointer hover:bg-gray-200 transition-colors"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                        {Array.from(validationResults.missingTags).filter(tag => !allowedCreations.tags.has(tag)).length === 0 && (
                          <p className="text-sm text-gray-500 italic">Todas as tags foram selecionadas</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {/* Contatos */}
                <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
                  <div className="bg-gradient-to-r from-green-500 to-green-600 px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-white/20 rounded-lg flex items-center justify-center">
                          <span className="text-white text-lg">👥</span>
                        </div>
                        <h4 className="font-bold text-white">Contatos</h4>
                      </div>
                      {validationResults.missingContacts.size > 0 && (
                        <button
                          onClick={() => {
                            const allSelected = Array.from(validationResults.missingContacts).every(contact =>
                              allowedCreations.contacts.has(contact)
                            )
                            const newSet = new Set(allowedCreations.contacts)
                            if (allSelected) {
                              validationResults.missingContacts.forEach(contact => newSet.delete(contact))
                            } else {
                              validationResults.missingContacts.forEach(contact => newSet.add(contact))
                            }
                            setAllowedCreations(prev => ({ ...prev, contacts: newSet }))
                          }}
                          className="text-white/80 hover:text-white text-sm font-medium"
                        >
                          {Array.from(validationResults.missingContacts).every(contact =>
                            allowedCreations.contacts.has(contact)
                          ) ? 'Limpar todos' : 'Selecionar todos'}
                        </button>
                      )}
                    </div>
                  </div>
                  <div className="p-6">
                    {validationResults.foundContacts.size > 0 && (
                      <div className="mb-6">
                        <p className="text-sm font-semibold text-green-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Encontrados ({validationResults.foundContacts.size})
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.foundContacts).map(contact => (
                            <span key={contact} className="px-3 py-1.5 bg-green-100 text-green-800 rounded-full text-sm font-medium border border-green-200">
                              {contact}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {validationResults.missingContacts.size > 0 && (
                      <div>
                        <p className="text-sm font-semibold text-green-700 mb-3 flex items-center gap-2">
                          <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                          Selecionados ({Array.from(validationResults.missingContacts).filter(contact => allowedCreations.contacts.has(contact)).length})
                        </p>
                        <div className="flex flex-wrap gap-2 mb-4">
                          {Array.from(validationResults.missingContacts).filter(contact => allowedCreations.contacts.has(contact)).map(contact => (
                            <span
                              key={contact}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.contacts)
                                newSet.delete(contact)
                                setAllowedCreations(prev => ({ ...prev, contacts: newSet }))
                              }}
                              className="px-3 py-1.5 bg-green-100 text-green-800 rounded-full text-sm font-medium border border-green-200 cursor-pointer hover:bg-green-200 transition-colors flex items-center gap-1"
                            >
                              {contact} <span className="text-green-600">×</span>
                            </span>
                          ))}
                        </div>

                        <p className="text-sm font-semibold text-gray-600 mb-3">Disponíveis</p>
                        <div className="flex flex-wrap gap-2">
                          {Array.from(validationResults.missingContacts).filter(contact => !allowedCreations.contacts.has(contact)).map(contact => (
                            <span
                              key={contact}
                              onClick={() => {
                                const newSet = new Set(allowedCreations.contacts)
                                newSet.add(contact)
                                setAllowedCreations(prev => ({ ...prev, contacts: newSet }))
                              }}
                              className="px-3 py-1.5 bg-gray-100 text-gray-600 rounded-full text-sm font-medium border border-gray-200 cursor-pointer hover:bg-gray-200 transition-colors"
                            >
                              {contact}
                            </span>
                          ))}
                        </div>
                        {Array.from(validationResults.missingContacts).filter(contact => !allowedCreations.contacts.has(contact)).length === 0 && (
                          <p className="text-sm text-gray-500 italic">Todos os contatos foram selecionados</p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Erros de Validação */}
              {validationResults.errors.length > 0 && (
                <div className="bg-red-50 rounded-lg p-4 border border-red-200 mb-6">
                  <h4 className="font-medium text-red-900 mb-3">❌ Erros Encontrados</h4>
                  <div className="max-h-32 overflow-y-auto">
                    {validationResults.errors.map((error, index) => (
                      <div key={index} className="text-sm text-red-700 mb-1">
                        <strong>Linha {error.line}:</strong> {error.errors.join(', ')}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </>
          )}

          {/* Tabela de Pré-visualização */}
          <div className="bg-white rounded-xl border border-gray-200 shadow-sm overflow-hidden">
            <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
                  <span className="text-white text-lg">📋</span>
                </div>
                <h4 className="font-bold text-gray-900">Dados a serem importados</h4>
                <span className="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">
                  {filteredData.length} transações
                </span>
              </div>

              {/* Filtros */}
              <div className="grid grid-cols-2 md:grid-cols-6 gap-3">
                <input
                  type="text"
                  placeholder="🔍 Filtrar descrição..."
                  value={filters.descricao}
                  onChange={(e) => setFilters(prev => ({ ...prev, descricao: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input
                  type="text"
                  placeholder="📅 Filtrar data..."
                  value={filters.data}
                  onChange={(e) => setFilters(prev => ({ ...prev, data: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <select
                  value={filters.tipo}
                  onChange={(e) => setFilters(prev => ({ ...prev, tipo: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="">📊 Todos os tipos</option>
                  <option value="INCOME">💰 Receita</option>
                  <option value="EXPENSE">💸 Despesa</option>
                  <option value="INVESTMENT">📈 Investimento</option>
                  <option value="LOAN">🏦 Empréstimo</option>
                </select>
                <input
                  type="text"
                  placeholder="📂 Filtrar categoria..."
                  value={filters.categoria}
                  onChange={(e) => setFilters(prev => ({ ...prev, categoria: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <input
                  type="text"
                  placeholder="🏷️ Filtrar tags..."
                  value={filters.tags}
                  onChange={(e) => setFilters(prev => ({ ...prev, tags: e.target.value }))}
                  className="px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <button
                  onClick={() => setFilters({ descricao: '', data: '', tipo: '', categoria: '', tags: '' })}
                  className="px-3 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium"
                >
                  🗑️ Limpar
                </button>
              </div>
            </div>

            <div className="overflow-hidden">
              <div className="overflow-x-auto max-h-96">
                <table className="w-full divide-y divide-gray-200" style={{ minWidth: '1200px' }}>
                <thead className="bg-gradient-to-r from-gray-50 to-gray-100 sticky top-0 z-10">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-24">
                      📅 Data
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-48">
                      📝 Descrição
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                      📊 Tipo
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28">
                      💰 Valor
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                      📂 Categoria
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                      👥 Contato
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-36">
                      🏷️ Tags
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-24">
                      🏦 Banco
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-24">
                      💳 Pagto
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                      🔧 Ações
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {filteredData.map((row, index) => {
                    const originalIndex = importData.indexOf(row)
                    const isEditing = editingRow === originalIndex
                    const rowClass = getRowColor(row, originalIndex)

                    return (
                      <tr
                        key={originalIndex}
                        className={`${rowClass} cursor-pointer transition-all duration-200 hover:shadow-md`}
                        onClick={() => {
                          if (!isEditing) {
                            const newSet = new Set(selectedRows)
                            if (selectedRows.has(originalIndex)) {
                              newSet.delete(originalIndex)
                            } else {
                              newSet.add(originalIndex)
                            }
                            setSelectedRows(newSet)
                          }
                        }}
                      >
                        <td className="px-3 py-3">
                          <div className="flex items-center gap-2">
                            {selectedRows.has(originalIndex) ? (
                              <span className="w-4 h-4 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs">✓</span>
                            ) : (
                              <span className="w-4 h-4 border-2 border-gray-300 rounded-full"></span>
                            )}
                            <span className="text-xs font-medium text-gray-600">
                              {excludedRows.has(originalIndex) ? 'Excluída' :
                               selectedRows.has(originalIndex) ? 'Selecionada' : 'Disponível'}
                            </span>
                          </div>
                        </td>

                        {isEditing ? (
                          <>
                            <td className="px-3 py-3">
                              <input
                                type="text"
                                value={editData.Data || row.Data}
                                onChange={(e) => setEditData(prev => ({ ...prev, Data: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                placeholder="DD/MM/AAAA"
                              />
                            </td>
                            <td className="px-3 py-3">
                              <input
                                type="text"
                                value={editData.Descrição || row.Descrição}
                                onChange={(e) => setEditData(prev => ({ ...prev, Descrição: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              />
                            </td>
                            <td className="px-3 py-3">
                              <select
                                value={editData.Tipo || row.Tipo}
                                onChange={(e) => setEditData(prev => ({ ...prev, Tipo: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              >
                                <option value="INCOME">INCOME</option>
                                <option value="EXPENSE">EXPENSE</option>
                                <option value="INVESTMENT">INVESTMENT</option>
                                <option value="LOAN">LOAN</option>
                              </select>
                            </td>
                            <td className="px-3 py-3">
                              <input
                                type="text"
                                value={editData.Valor || row.Valor}
                                onChange={(e) => setEditData(prev => ({ ...prev, Valor: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                placeholder="0,00"
                              />
                            </td>
                            <td className="px-3 py-3">
                              <select
                                value={editData.Categoria || row.Categoria}
                                onChange={(e) => setEditData(prev => ({ ...prev, Categoria: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              >
                                <option value="">Selecione...</option>
                                {categories.map(cat => (
                                  <option key={cat.id} value={cat.name}>{cat.name}</option>
                                ))}
                                {editData.Categoria && !categories.find(c => c.name === editData.Categoria) && (
                                  <option value={editData.Categoria}>{editData.Categoria} (Nova)</option>
                                )}
                              </select>
                            </td>
                            <td className="px-3 py-3">
                              <select
                                value={editData.Contato || row.Contato}
                                onChange={(e) => setEditData(prev => ({ ...prev, Contato: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              >
                                <option value="">Selecione...</option>
                                {contacts.map(contact => (
                                  <option key={contact.id} value={contact.name}>{contact.name}</option>
                                ))}
                                {editData.Contato && !contacts.find(c => c.name === editData.Contato) && (
                                  <option value={editData.Contato}>{editData.Contato} (Novo)</option>
                                )}
                              </select>
                            </td>
                            <td className="px-3 py-3">
                              <input
                                type="text"
                                value={editData.Tags || row.Tags}
                                onChange={(e) => setEditData(prev => ({ ...prev, Tags: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                placeholder="tag1,tag2,tag3"
                              />
                            </td>
                            <td className="px-3 py-3">
                              <select
                                value={editData.Banco || row.Banco}
                                onChange={(e) => setEditData(prev => ({ ...prev, Banco: e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              >
                                <option value="">Selecione...</option>
                                {banks.map(bank => (
                                  <option key={bank.id} value={bank.name}>{bank.name}</option>
                                ))}
                              </select>
                            </td>
                            <td className="px-3 py-3">
                              <select
                                value={editData['Forma de Pagamento'] || row['Forma de Pagamento'] || ''}
                                onChange={(e) => setEditData(prev => ({ ...prev, 'Forma de Pagamento': e.target.value }))}
                                className="w-full px-2 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                              >
                                <option value="">Nenhuma</option>
                                {paymentMethods.map(pm => (
                                  <option key={pm.id} value={pm.name}>{pm.name}</option>
                                ))}
                              </select>
                            </td>
                            <td className="px-3 py-3">
                              <div className="flex gap-2">
                                <button
                                  onClick={() => {
                                    // Salvar edição
                                    const newData = [...importData]
                                    newData[originalIndex] = { ...row, ...editData }
                                    setImportData(newData)
                                    setEditingRow(null)
                                    setEditData({})
                                  }}
                                  className="px-3 py-1.5 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors text-sm font-medium"
                                >
                                  ✓ Salvar
                                </button>
                                <button
                                  onClick={() => {
                                    setEditingRow(null)
                                    setEditData({})
                                  }}
                                  className="px-3 py-1.5 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors text-sm font-medium"
                                >
                                  ✕ Cancelar
                                </button>
                              </div>
                            </td>
                          </>
                        ) : (
                          <>
                            <td className="px-3 py-3 text-sm font-medium text-gray-900">{row.Data}</td>
                            <td className="px-3 py-3 text-sm text-gray-900 truncate max-w-xs" title={row.Descrição}>
                              {row.Descrição}
                            </td>
                            <td className="px-3 py-3">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                row.Tipo === 'INCOME' ? 'bg-green-100 text-green-800 border border-green-200' :
                                row.Tipo === 'EXPENSE' ? 'bg-red-100 text-red-800 border border-red-200' :
                                row.Tipo === 'INVESTMENT' ? 'bg-blue-100 text-blue-800 border border-blue-200' :
                                'bg-gray-100 text-gray-800 border border-gray-200'
                              }`}>
                                {row.Tipo === 'INCOME' ? '💰' :
                                 row.Tipo === 'EXPENSE' ? '💸' :
                                 row.Tipo === 'INVESTMENT' ? '📈' :
                                 row.Tipo === 'LOAN' ? '🏦' : '❓'}
                              </span>
                            </td>
                            <td className="px-3 py-3 text-sm font-bold text-gray-900">
                              {formatCurrency(parseFloat(row.Valor?.replace(',', '.')))}
                            </td>
                            <td className="px-3 py-3">
                              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium border border-blue-200 truncate max-w-xs" title={row.Categoria}>
                                {row.Categoria}
                              </span>
                            </td>
                            <td className="px-3 py-3">
                              <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded-full text-xs font-medium border border-purple-200 truncate max-w-xs" title={row.Contato}>
                                {row.Contato}
                              </span>
                            </td>
                            <td className="px-3 py-3">
                              <div className="flex flex-wrap gap-1 max-w-xs">
                                {row.Tags?.split(',').slice(0, 2).map((tag, tagIndex) => (
                                  <span key={tagIndex} className="px-2 py-1 bg-orange-100 text-orange-800 rounded-full text-xs font-medium border border-orange-200">
                                    {tag.trim()}
                                  </span>
                                ))}
                                {row.Tags?.split(',').length > 2 && (
                                  <span className="px-2 py-1 bg-gray-100 text-gray-600 rounded-full text-xs font-medium border border-gray-200">
                                    +{row.Tags.split(',').length - 2}
                                  </span>
                                )}
                              </div>
                            </td>
                            <td className="px-3 py-3">
                              <div className={`w-4 h-4 rounded-full flex items-center justify-center text-xs ${
                                bankValidation[originalIndex]?.exists ?
                                'bg-green-500 text-white' :
                                'bg-red-500 text-white'
                              }`} title={`${row.Banco} - ${bankValidation[originalIndex]?.exists ? 'Válido' : 'Inválido'}`}>
                                {bankValidation[originalIndex]?.exists ? '✓' : '✗'}
                              </div>
                            </td>
                            <td className="px-3 py-3">
                              {row['Forma de Pagamento'] ? (
                                <div className="w-4 h-4 bg-indigo-500 rounded-full flex items-center justify-center text-white text-xs" title={row['Forma de Pagamento']}>
                                  💳
                                </div>
                              ) : (
                                <div className="w-4 h-4 bg-gray-300 rounded-full flex items-center justify-center text-gray-600 text-xs" title="Sem forma de pagamento">
                                  -
                                </div>
                              )}
                            </td>
                            <td className="px-3 py-3">
                              <div className="flex gap-1">
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    setEditingRow(originalIndex)
                                    setEditData(row)
                                  }}
                                  className="px-2 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 transition-colors"
                                >
                                  ✏️
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    const newExcluded = new Set(excludedRows)
                                    newExcluded.add(originalIndex)
                                    setExcludedRows(newExcluded)
                                  }}
                                  className="px-2 py-1 bg-red-500 text-white rounded text-xs hover:bg-red-600 transition-colors"
                                >
                                  🗑️
                                </button>
                              </div>
                            </td>
                          </>
                        )}
                      </tr>
                    )
                  })}
                </tbody>
                </table>
                {filteredData.length === 0 && (
                  <div className="bg-gray-50 px-4 py-8 text-center text-sm text-gray-600">
                    Nenhuma transação encontrada com os filtros aplicados
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-between items-center">
          <div className="text-sm text-gray-600">
            <div>
              Total: {importData.length} transações
            </div>
            <div>
              Selecionadas para importação: {importData.length - excludedRows.size}
            </div>
            <div>
              Bancos identificados: {Object.keys(calculateBankImpacts()).length}
            </div>
          </div>
          <div className="flex gap-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-gray-200 rounded-lg hover:bg-gray-300 transition-colors"
            >
              Cancelar
            </button>
            <button
              onClick={() => {
                // Preparar dados para importação
                const dataToImport = importData
                  .map((row, index) => ({ ...row, originalIndex: index }))
                  .filter((_, index) => !excludedRows.has(index))
                  .map(row => ({
                    ...row,
                    allowedCreations,
                    selectedBanksForImpact: Array.from(selectedBanksForImpact)
                  }))

                onConfirmImport(dataToImport)
              }}
              disabled={importData.length - excludedRows.size === 0}
              className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
            >
              Confirmar Importação ({importData.length - excludedRows.size} transações)
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Reports
