import React, { useState, useEffect } from 'react'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Settings,
  PiggyBank,
  Lightbulb,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Gauge,
  Zap,
  Award,
  Plus,
  FileText,
  Eye
} from 'lucide-react'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  Pie<PERSON>hart as Recharts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  Cell
} from 'recharts'
import GreenMeter from '../components/GreenMeter'
import EconomyTips from '../components/EconomyTips'
import ReportSetup from '../components/ReportSetup'
import api from '../services/api'
import toast from 'react-hot-toast'

function Reports({ selectedYear }) {
  const [reportConfig, setReportConfig] = useState(null)
  const [analysis, setAnalysis] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showSetup, setShowSetup] = useState(false)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYearState, setSelectedYearState] = useState(selectedYear || new Date().getFullYear())

  useEffect(() => {
    checkReportConfig()
  }, [selectedYearState, selectedMonth])

  const checkReportConfig = async () => {
    try {
      setLoading(true)

      // Verificar se existe configuração para o mês/ano
      const configResponse = await api.get(`/reports/config/${selectedYearState}/${selectedMonth}`)

      if (configResponse.data.exists && configResponse.data.config.isCompleted) {
        setReportConfig(configResponse.data.config)
        // Buscar análise
        await fetchAnalysis()
      } else {
        setReportConfig(null)
        setAnalysis(null)
      }
    } catch (error) {
      console.error('Erro ao verificar configuração:', error)
      setReportConfig(null)
      setAnalysis(null)
    } finally {
      setLoading(false)
    }
  }

  const fetchAnalysis = async () => {
    try {
      const response = await api.get(`/reports/analysis/${selectedYearState}/${selectedMonth}`)
      setAnalysis(response.data)
    } catch (error) {
      console.error('Erro ao buscar análise:', error)
      if (error.response?.data?.needsSetup) {
        setReportConfig(null)
        setAnalysis(null)
      } else {
        toast.error('Erro ao carregar análise do relatório')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getOverallHealthColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getOverallHealthMessage = (score) => {
    if (score >= 80) return 'Excelente controle financeiro!'
    if (score >= 60) return 'Bom controle, mas pode melhorar'
    if (score >= 40) return 'Atenção necessária'
    return 'Situação crítica - ação urgente'
  }

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const handleSetupComplete = () => {
    setShowSetup(false)
    checkReportConfig()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Relatórios Inteligentes</h1>
              <p className="text-sm text-gray-600">Análise detalhada baseada em suas configurações personalizadas</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Seletor de Mês/Ano */}
            <div className="flex items-center gap-2">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {months.map((month, index) => (
                  <option key={index} value={index + 1}>{month}</option>
                ))}
              </select>

              <select
                value={selectedYearState}
                onChange={(e) => setSelectedYearState(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[2023, 2024, 2025].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Botões de Ação */}
            {reportConfig ? (
              <div className="flex gap-2">
                <button
                  onClick={() => setShowSetup(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <Settings className="h-4 w-4" />
                  Editar Configuração
                </button>
                <button
                  onClick={fetchAnalysis}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Eye className="h-4 w-4" />
                  Atualizar Análise
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowSetup(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Configurar Relatório
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Conteúdo Principal */}
      {!reportConfig ? (
        /* Tela de Configuração Necessária */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Target className="h-10 w-10 text-blue-600" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Configure seu Relatório Personalizado
          </h2>

          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Para gerar relatórios inteligentes e personalizados, você precisa configurar suas preferências
            para <strong>{months[selectedMonth - 1]} de {selectedYearState}</strong>.
            Este processo leva apenas alguns minutos e pode ser feito em 3 passos simples.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">1. Definir Limites</h3>
              <p className="text-sm text-gray-600">Configure as porcentagens ideais para despesas, poupança e lazer</p>
            </div>

            <div className="bg-green-50 rounded-lg p-6 border border-green-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">2. Mapear Categorias</h3>
              <p className="text-sm text-gray-600">Selecione quais categorias, tags e contatos pertencem a cada limite</p>
            </div>

            <div className="bg-purple-50 rounded-lg p-6 border border-purple-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">3. Valores Esperados</h3>
              <p className="text-sm text-gray-600">Defina valores esperados para análises mais precisas</p>
            </div>
          </div>

          <button
            onClick={() => setShowSetup(true)}
            className="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
          >
            <Plus className="h-5 w-5" />
            Começar Configuração
          </button>
        </div>
      ) : analysis ? (
        /* Relatório Configurado */
        <>
          {/* Esverdeômetro - Score Geral */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-gray-900 flex items-center">
                <Gauge className="h-5 w-5 mr-2 text-green-600" />
                Esverdeômetro - {months[selectedMonth - 1]} {selectedYearState}
              </h2>

              <div className="text-center">
                <div className={`text-3xl font-bold ${getOverallHealthColor(analysis.analysis.scores.overall)}`}>
                  {Math.round(analysis.analysis.scores.overall)}%
                </div>
                <p className="text-sm text-gray-600">{getOverallHealthMessage(analysis.analysis.scores.overall)}</p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <GreenMeter
                score={analysis.analysis.scores.expense}
                title="Despesas Essenciais"
                current={analysis.analysis.actual.expensePercentage}
                target={analysis.analysis.expected.expensePercentage}
                type="percentage"
              />

              <GreenMeter
                score={analysis.analysis.scores.savings}
                title="Poupança/Investimentos"
                current={analysis.analysis.actual.savingsPercentage}
                target={analysis.analysis.expected.savingsPercentage}
                type="percentage"
              />

              <GreenMeter
                score={analysis.analysis.scores.leisure}
                title="Lazer"
                current={analysis.analysis.actual.leisurePercentage}
                target={analysis.analysis.expected.leisurePercentage}
                type="percentage"
              />
            </div>
          </div>

          {/* Resumo Financeiro */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
              <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
              Resumo do Mês
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {/* Valores Reais */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">Valores Reais</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-blue-600" />
                      <span className="font-medium">Renda</span>
                    </div>
                    <span className="font-bold text-blue-600">{formatCurrency(analysis.analysis.actual.income)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-5 w-5 text-red-600" />
                      <span className="font-medium">Despesas</span>
                    </div>
                    <span className="font-bold text-red-600">{formatCurrency(analysis.analysis.actual.expenses)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <PiggyBank className="h-5 w-5 text-green-600" />
                      <span className="font-medium">Poupança</span>
                    </div>
                    <span className="font-bold text-green-600">{formatCurrency(analysis.analysis.actual.savings)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Coffee className="h-5 w-5 text-purple-600" />
                      <span className="font-medium">Lazer</span>
                    </div>
                    <span className="font-bold text-purple-600">{formatCurrency(analysis.analysis.actual.leisure)}</span>
                  </div>
                </div>
              </div>

              {/* Valores Esperados */}
              <div>
                <h3 className="font-semibold text-gray-900 mb-4">Valores Esperados</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-5 w-5 text-gray-600" />
                      <span className="font-medium">Renda</span>
                    </div>
                    <span className="font-bold text-gray-600">{formatCurrency(analysis.analysis.expected.income || 0)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <TrendingDown className="h-5 w-5 text-gray-600" />
                      <span className="font-medium">Despesas</span>
                    </div>
                    <span className="font-bold text-gray-600">{formatCurrency(analysis.analysis.expected.expenses || 0)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <PiggyBank className="h-5 w-5 text-gray-600" />
                      <span className="font-medium">Poupança</span>
                    </div>
                    <span className="font-bold text-gray-600">{formatCurrency(analysis.analysis.expected.savings || 0)}</span>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Coffee className="h-5 w-5 text-gray-600" />
                      <span className="font-medium">Lazer</span>
                    </div>
                    <span className="font-bold text-gray-600">{formatCurrency(analysis.analysis.expected.leisure || 0)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      ) : (
        /* Loading da Análise */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando análise do relatório...</p>
        </div>
      )}

      {/* Dicas de Economia - apenas se há análise */}
      {analysis && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <EconomyTips analysis={analysis.analysis} />
        </div>
      )}

      {/* Conquistas e Motivação */}
      {analysis && analysis.analysis.scores.overall >= 80 && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4">
              <Award className="h-8 w-8 text-white" />
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-green-800">Parabéns!</h2>
              <p className="text-green-700">Você está com excelente controle financeiro!</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 text-center">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Meta Atingida</h3>
              <p className="text-sm text-gray-600">Continue assim!</p>
            </div>

            <div className="bg-white rounded-lg p-4 text-center">
              <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Economia Inteligente</h3>
              <p className="text-sm text-gray-600">Você está economizando com sabedoria</p>
            </div>

            <div className="bg-white rounded-lg p-4 text-center">
              <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Futuro Brilhante</h3>
              <p className="text-sm text-gray-600">Suas finanças estão no caminho certo</p>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Setup */}
      <ReportSetup
        isOpen={showSetup}
        onClose={() => setShowSetup(false)}
        year={selectedYearState}
        month={selectedMonth}
        onComplete={handleSetupComplete}
      />
    </div>
  )
}

export default Reports
