import React, { useState, useEffect } from 'react'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Settings,
  PiggyBank,
  Lightbulb,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Gauge,
  Zap,
  Award
} from 'lucide-react'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  PieChart as RechartsPieChart,
  Pie,
  Cell
} from 'recharts'
import GreenMeter from '../components/GreenMeter'
import EconomyTips from '../components/EconomyTips'
import GoalSettings from '../components/GoalSettings'
import api from '../services/api'
import toast from 'react-hot-toast'

function Reports({ selectedYear }) {
  const [analysis, setAnalysis] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showGoalSettings, setShowGoalSettings] = useState(false)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYearState, setSelectedYearState] = useState(selectedYear || new Date().getFullYear())

  useEffect(() => {
    fetchAnalysis()
  }, [selectedYearState, selectedMonth])

  const fetchAnalysis = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/financial-goals/analysis?year=${selectedYearState}&month=${selectedMonth}`)
      setAnalysis(response.data)
    } catch (error) {
      console.error('Erro ao buscar análise financeira:', error)
      toast.error('Erro ao carregar análise financeira')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getOverallHealthColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getOverallHealthMessage = (score) => {
    if (score >= 80) return 'Excelente controle financeiro!'
    if (score >= 60) return 'Bom controle, mas pode melhorar'
    if (score >= 40) return 'Atenção necessária'
    return 'Situação crítica - ação urgente'
  }

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
              <Target className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Gestão Financeira Inteligente</h1>
              <p className="text-sm text-gray-600">Controle suas metas e economize com sabedoria</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Seletor de Mês/Ano */}
            <div className="flex items-center gap-2">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {months.map((month, index) => (
                  <option key={index} value={index + 1}>{month}</option>
                ))}
              </select>

              <select
                value={selectedYearState}
                onChange={(e) => setSelectedYearState(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[2023, 2024, 2025].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Botão Configurar Metas */}
            <button
              onClick={() => setShowGoalSettings(true)}
              className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <Settings className="h-4 w-4" />
              Configurar Metas
            </button>
          </div>
        </div>
      </div>

      {/* Esverdeômetro - Score Geral */}
      {analysis && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-bold text-gray-900 flex items-center">
              <Gauge className="h-5 w-5 mr-2 text-green-600" />
              Esverdeômetro - {months[selectedMonth - 1]} {selectedYearState}
            </h2>

            <div className="text-center">
              <div className={`text-3xl font-bold ${getOverallHealthColor(analysis.scores.overall)}`}>
                {Math.round(analysis.scores.overall)}%
              </div>
              <p className="text-sm text-gray-600">{getOverallHealthMessage(analysis.scores.overall)}</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <GreenMeter
              score={analysis.scores.expense}
              title="Despesas Essenciais"
              current={analysis.current.expensePercentage}
              target={analysis.goal.expensePercentage}
              type="percentage"
            />

            <GreenMeter
              score={analysis.scores.savings}
              title="Poupança/Investimentos"
              current={analysis.current.savingsPercentage}
              target={analysis.goal.savingsPercentage}
              type="percentage"
            />

            <GreenMeter
              score={analysis.scores.leisure}
              title="Lazer"
              current={analysis.current.leisurePercentage}
              target={analysis.goal.leisurePercentage}
              type="percentage"
            />
          </div>
        </div>
      )}

      {/* Resumo Financeiro */}
      {analysis && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <h2 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-indigo-600" />
            Resumo do Mês
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <DollarSign className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900 mb-1">Renda</h3>
              <p className="text-xl font-bold text-blue-600">{formatCurrency(analysis.current.income)}</p>
            </div>

            <div className="text-center p-4 bg-red-50 rounded-lg">
              <TrendingDown className="h-8 w-8 text-red-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900 mb-1">Despesas Essenciais</h3>
              <p className="text-xl font-bold text-red-600">{formatCurrency(analysis.current.essentialExpenses)}</p>
            </div>

            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900 mb-1">Lazer</h3>
              <p className="text-xl font-bold text-purple-600">{formatCurrency(analysis.current.leisureExpenses)}</p>
            </div>

            <div className="text-center p-4 bg-green-50 rounded-lg">
              <PiggyBank className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900 mb-1">Poupança</h3>
              <p className="text-xl font-bold text-green-600">{formatCurrency(analysis.current.savings)}</p>
            </div>
          </div>
        </div>
      )}

      {/* Dicas de Economia */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <EconomyTips analysis={analysis} />
      </div>

      {/* Conquistas e Motivação */}
      {analysis && analysis.scores.overall >= 80 && (
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-100">
          <div className="flex items-center justify-center mb-4">
            <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-600 rounded-full flex items-center justify-center mr-4">
              <Award className="h-8 w-8 text-white" />
            </div>
            <div className="text-center">
              <h2 className="text-2xl font-bold text-green-800">Parabéns!</h2>
              <p className="text-green-700">Você está com excelente controle financeiro!</p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-white rounded-lg p-4 text-center">
              <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Meta Atingida</h3>
              <p className="text-sm text-gray-600">Continue assim!</p>
            </div>

            <div className="bg-white rounded-lg p-4 text-center">
              <Zap className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Economia Inteligente</h3>
              <p className="text-sm text-gray-600">Você está economizando com sabedoria</p>
            </div>

            <div className="bg-white rounded-lg p-4 text-center">
              <TrendingUp className="h-8 w-8 text-blue-600 mx-auto mb-2" />
              <h3 className="font-semibold text-gray-900">Futuro Brilhante</h3>
              <p className="text-sm text-gray-600">Suas finanças estão no caminho certo</p>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Configurações */}
      <GoalSettings
        isOpen={showGoalSettings}
        onClose={() => setShowGoalSettings(false)}
        onGoalUpdated={fetchAnalysis}
      />
    </div>
  )
}

export default Reports
