import React, { useState, useEffect } from 'react'
import {
  Target,
  TrendingUp,
  TrendingDown,
  Settings,
  PiggyBank,
  Lightbulb,
  DollarSign,
  Calendar,
  BarChart3,
  PieChart,
  ArrowRight,
  CheckCircle,
  AlertCircle,
  Gauge,
  Zap,
  Award,
  Plus,
  FileText,
  Eye,
  Download,
  Upload
} from 'lucide-react'
import {
  ResponsiveContainer,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  BarChart,
  Bar,
  <PERSON><PERSON><PERSON> as RechartsPie<PERSON>hart,
  Pie,
  Cell
} from 'recharts'
import GreenMeter from '../components/GreenMeter'
import EconomyTips from '../components/EconomyTips'
import ReportSetup from '../components/ReportSetup'
import EsverdeometroPanel from '../components/EsverdeometroPanel'
import ExpectedVsActualChart from '../components/ExpectedVsActualChart'
import GoalManager from '../components/GoalManager'
import api from '../services/api'
import toast from 'react-hot-toast'

function Reports({ selectedYear }) {
  const [reportConfig, setReportConfig] = useState(null)
  const [analysis, setAnalysis] = useState(null)
  const [loading, setLoading] = useState(true)
  const [showSetup, setShowSetup] = useState(false)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYearState, setSelectedYearState] = useState(selectedYear || new Date().getFullYear())
  const [activeTab, setActiveTab] = useState('reports')

  useEffect(() => {
    checkReportConfig()
  }, [selectedYearState, selectedMonth])

  const checkReportConfig = async () => {
    try {
      setLoading(true)

      // Verificar se existe configuração para o mês/ano
      const configResponse = await api.get(`/reports/config/${selectedYearState}/${selectedMonth}`)

      if (configResponse.data.exists && configResponse.data.config.isCompleted) {
        setReportConfig(configResponse.data.config)
        // Buscar análise
        await fetchAnalysis()
      } else {
        setReportConfig(null)
        setAnalysis(null)
      }
    } catch (error) {
      console.error('Erro ao verificar configuração:', error)
      setReportConfig(null)
      setAnalysis(null)
    } finally {
      setLoading(false)
    }
  }

  const fetchAnalysis = async () => {
    try {
      const response = await api.get(`/reports/analysis/${selectedYearState}/${selectedMonth}`)
      setAnalysis(response.data)
    } catch (error) {
      console.error('Erro ao buscar análise:', error)
      if (error.response?.data?.needsSetup) {
        setReportConfig(null)
        setAnalysis(null)
      } else {
        toast.error('Erro ao carregar análise do relatório')
      }
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatPercentage = (value) => {
    return `${value.toFixed(1)}%`
  }

  const getOverallHealthColor = (score) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-yellow-600'
    if (score >= 40) return 'text-orange-600'
    return 'text-red-600'
  }

  const getOverallHealthMessage = (score) => {
    if (score >= 80) return 'Excelente controle financeiro!'
    if (score >= 60) return 'Bom controle, mas pode melhorar'
    if (score >= 40) return 'Atenção necessária'
    return 'Situação crítica - ação urgente'
  }

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const handleSetupComplete = () => {
    setShowSetup(false)
    checkReportConfig()
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8 fade-in">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center mr-4">
              <FileText className="h-6 w-6 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Relatórios Inteligentes</h1>
              <p className="text-sm text-gray-600">Análise detalhada baseada em suas configurações personalizadas</p>
            </div>
          </div>

          <div className="flex items-center gap-4">
            {/* Seletor de Mês/Ano */}
            <div className="flex items-center gap-2">
              <select
                value={selectedMonth}
                onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {months.map((month, index) => (
                  <option key={index} value={index + 1}>{month}</option>
                ))}
              </select>

              <select
                value={selectedYearState}
                onChange={(e) => setSelectedYearState(parseInt(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[2023, 2024, 2025].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>

            {/* Botões de Ação */}
            {reportConfig ? (
              <div className="flex gap-2">
                <button
                  onClick={() => setShowSetup(true)}
                  className="flex items-center gap-2 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
                >
                  <Settings className="h-4 w-4" />
                  Editar Configuração
                </button>
                <button
                  onClick={fetchAnalysis}
                  className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Eye className="h-4 w-4" />
                  Atualizar Análise
                </button>
              </div>
            ) : (
              <button
                onClick={() => setShowSetup(true)}
                className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Plus className="h-4 w-4" />
                Configurar Relatório
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Abas */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div className="flex border-b border-gray-200">
          <button
            onClick={() => setActiveTab('reports')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'reports'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <BarChart3 className="h-4 w-4" />
              Relatórios
            </div>
          </button>
          <button
            onClick={() => setActiveTab('goals')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'goals'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Target className="h-4 w-4" />
              Objetivos
            </div>
          </button>
          <button
            onClick={() => setActiveTab('import-export')}
            className={`flex-1 px-6 py-4 text-sm font-medium transition-colors ${
              activeTab === 'import-export'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-blue-50'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            <div className="flex items-center justify-center gap-2">
              <Download className="h-4 w-4" />
              Importar/Exportar
            </div>
          </button>
        </div>
      </div>

      {/* Conteúdo Principal */}
      {activeTab === 'reports' ? (
        !reportConfig ? (
        /* Tela de Configuração Necessária */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <Target className="h-10 w-10 text-blue-600" />
          </div>

          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Configure seu Relatório Personalizado
          </h2>

          <p className="text-gray-600 mb-8 max-w-2xl mx-auto">
            Para gerar relatórios inteligentes e personalizados, você precisa configurar suas preferências
            para <strong>{months[selectedMonth - 1]} de {selectedYearState}</strong>.
            Este processo leva apenas alguns minutos e pode ser feito em 3 passos simples.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">1. Definir Limites</h3>
              <p className="text-sm text-gray-600">Configure as porcentagens ideais para despesas, poupança e lazer</p>
            </div>

            <div className="bg-green-50 rounded-lg p-6 border border-green-100">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <Settings className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">2. Mapear Categorias</h3>
              <p className="text-sm text-gray-600">Selecione quais categorias, tags e contatos pertencem a cada limite</p>
            </div>

            <div className="bg-purple-50 rounded-lg p-6 border border-purple-100">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                <DollarSign className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold text-gray-900 mb-2">3. Valores Esperados</h3>
              <p className="text-sm text-gray-600">Defina valores esperados para análises mais precisas</p>
            </div>
          </div>

          <button
            onClick={() => setShowSetup(true)}
            className="flex items-center gap-2 px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors mx-auto"
          >
            <Plus className="h-5 w-5" />
            Começar Configuração
          </button>
        </div>
      ) : reportConfig ? (
        /* Relatório Configurado */
        <>
          {/* Gráfico Esperado vs Real */}
          <ExpectedVsActualChart
            year={selectedYearState}
            month={selectedMonth}
          />

          {/* Painel de Esverdeômetro */}
          <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
            <EsverdeometroPanel
              year={selectedYearState}
              month={selectedMonth}
            />
          </div>
        </>
      ) : (
        /* Loading da Análise */
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Carregando análise do relatório...</p>
        </div>
        )
      ) : activeTab === 'goals' ? (
        /* Aba de Objetivos */
        <GoalManager />
      ) : (
        /* Aba de Importação/Exportação */
        <ImportExportPanel
          selectedYear={selectedYearState}
          selectedMonth={selectedMonth}
        />
      )}



      {/* Modal de Setup */}
      <ReportSetup
        isOpen={showSetup}
        onClose={() => setShowSetup(false)}
        year={selectedYearState}
        month={selectedMonth}
        onComplete={handleSetupComplete}
      />
    </div>
  )
}

// Componente de Importação/Exportação
const ImportExportPanel = ({ selectedYear, selectedMonth }) => {
  const [operationType, setOperationType] = useState('export')
  const [exportYear, setExportYear] = useState(selectedYear)
  const [exportMonth, setExportMonth] = useState(selectedMonth)

  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ]

  const handleExport = async (format) => {
    try {
      const response = await api.post('/reports/export', {
        year: exportYear,
        month: exportMonth,
        format: format
      }, {
        responseType: 'blob'
      })

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      // Definir nome do arquivo baseado no formato
      const monthStr = exportMonth.toString().padStart(2, '0')
      const extension = format.toLowerCase()
      link.setAttribute('download', `relatorio-transacoes-${exportYear}-${monthStr}.${extension}`)

      // Fazer download
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      toast.success(`Relatório ${format} exportado com sucesso!`)
    } catch (error) {
      console.error('Erro ao exportar:', error)
      toast.error(error.response?.data?.error || 'Erro ao exportar relatório')
    }
  }

  const handleDownloadTemplate = async (format) => {
    try {
      const response = await api.get(`/reports/import-template/${format}`, {
        responseType: 'blob'
      })

      // Criar URL para download
      const url = window.URL.createObjectURL(new Blob([response.data]))
      const link = document.createElement('a')
      link.href = url

      // Definir nome do arquivo
      const extension = format.toLowerCase()
      link.setAttribute('download', `modelo-importacao-sara.${extension}`)

      // Fazer download
      document.body.appendChild(link)
      link.click()
      link.remove()
      window.URL.revokeObjectURL(url)

      toast.success(`Modelo ${format} baixado com sucesso!`)
    } catch (error) {
      console.error('Erro ao baixar template:', error)
      toast.error('Erro ao baixar modelo de importação')
    }
  }

  const handleImport = () => {
    toast.info('🚧 Funcionalidade em desenvolvimento! Em breve você poderá importar dados.')
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-100">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
            <Download className="h-6 w-6 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-gray-900">Importar/Exportar Dados</h2>
            <p className="text-gray-600">Gerencie seus dados de relatórios e configurações</p>
          </div>
        </div>
      </div>

      {/* Seletor de Operação */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Tipo de Operação</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <button
            onClick={() => setOperationType('export')}
            className={`p-4 rounded-lg border-2 transition-all ${
              operationType === 'export'
                ? 'border-blue-500 bg-blue-50 text-blue-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-3">
              <Download className="h-6 w-6" />
              <div className="text-left">
                <h4 className="font-semibold">Exportar Dados</h4>
                <p className="text-sm text-gray-600">Baixar configurações e relatórios</p>
              </div>
            </div>
          </button>

          <button
            onClick={() => setOperationType('import')}
            className={`p-4 rounded-lg border-2 transition-all ${
              operationType === 'import'
                ? 'border-green-500 bg-green-50 text-green-700'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            <div className="flex items-center gap-3">
              <Upload className="h-6 w-6" />
              <div className="text-left">
                <h4 className="font-semibold">Importar Dados</h4>
                <p className="text-sm text-gray-600">Carregar configurações salvas</p>
              </div>
            </div>
          </button>
        </div>

        {/* Seleção de Período */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <h4 className="font-medium text-gray-900 mb-3">Selecionar Período</h4>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Mês</label>
              <select
                value={exportMonth}
                onChange={(e) => setExportMonth(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {months.map((month, index) => (
                  <option key={index} value={index + 1}>{month}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Ano</label>
              <select
                value={exportYear}
                onChange={(e) => setExportYear(parseInt(e.target.value))}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                {[2023, 2024, 2025].map(year => (
                  <option key={year} value={year}>{year}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Ações */}
        {operationType === 'export' ? (
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Opções de Exportação</h4>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <button
                onClick={() => handleExport('PDF')}
                className="flex items-center justify-center gap-2 p-3 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm"
              >
                <FileText className="h-4 w-4" />
                PDF
              </button>

              <button
                onClick={() => handleExport('XLSX')}
                className="flex items-center justify-center gap-2 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                XLSX
              </button>

              <button
                onClick={() => handleExport('XLS')}
                className="flex items-center justify-center gap-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                XLS
              </button>

              <button
                onClick={() => handleExport('CSV')}
                className="flex items-center justify-center gap-2 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
              >
                <Download className="h-4 w-4" />
                CSV
              </button>
            </div>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
              <h5 className="font-medium text-blue-900 mb-2">📋 O que será exportado:</h5>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Configurações de porcentagens para {months[exportMonth - 1]} de {exportYear}</li>
                <li>• Mapeamentos de categorias, tags e contatos</li>
                <li>• Valores esperados configurados</li>
                <li>• Dados de análise e relatórios gerados</li>
              </ul>
            </div>
          </div>
        ) : (
          <div className="space-y-6">
            <h4 className="font-medium text-gray-900">Baixar Modelos de Importação</h4>

            <div className="bg-blue-50 rounded-lg p-4 border border-blue-100 mb-4">
              <h5 className="font-medium text-blue-900 mb-2">📋 Primeiro, baixe um modelo:</h5>
              <p className="text-sm text-blue-800 mb-3">
                Baixe um modelo com exemplos e preencha com seus dados antes de importar.
              </p>

              <div className="grid grid-cols-3 gap-3">
                <button
                  onClick={() => handleDownloadTemplate('CSV')}
                  className="flex items-center justify-center gap-2 p-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  CSV
                </button>
                <button
                  onClick={() => handleDownloadTemplate('XLSX')}
                  className="flex items-center justify-center gap-2 p-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  XLSX
                </button>
                <button
                  onClick={() => handleDownloadTemplate('XLS')}
                  className="flex items-center justify-center gap-2 p-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                >
                  <Download className="h-4 w-4" />
                  XLS
                </button>
              </div>
            </div>

            <h4 className="font-medium text-gray-900">Importar Arquivo Preenchido</h4>

            <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600 mb-4">Arraste um arquivo aqui ou clique para selecionar</p>

              <button
                onClick={handleImport}
                className="flex items-center gap-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors mx-auto"
              >
                <Upload className="h-5 w-5" />
                Selecionar Arquivo
              </button>
            </div>

            <div className="bg-green-50 rounded-lg p-4 border border-green-100">
              <h5 className="font-medium text-green-900 mb-2">📁 Campos obrigatórios no arquivo:</h5>
              <ul className="text-sm text-green-800 space-y-1">
                <li>• <strong>Data:</strong> Formato DD/MM/AAAA</li>
                <li>• <strong>Descrição:</strong> Nome da transação</li>
                <li>• <strong>Tipo:</strong> INCOME, EXPENSE, INVESTMENT ou LOAN</li>
                <li>• <strong>Valor:</strong> Valor numérico (use vírgula para decimais)</li>
                <li>• <strong>Categoria:</strong> Nome da categoria (será criada se não existir)</li>
                <li>• <strong>Banco:</strong> Nome do banco</li>
                <li>• <strong>Contato:</strong> Nome do contato (opcional)</li>
                <li>• <strong>Tags:</strong> Separadas por vírgula (opcional)</li>
              </ul>
            </div>

            <div className="bg-yellow-50 rounded-lg p-4 border border-yellow-100">
              <h5 className="font-medium text-yellow-900 mb-2">⚠️ Importante:</h5>
              <ul className="text-sm text-yellow-800 space-y-1">
                <li>• O sistema buscará categorias, contatos e tags pelo nome</li>
                <li>• Se não encontrar, criará automaticamente</li>
                <li>• Você poderá revisar os dados antes de salvar</li>
                <li>• Máximo de 1000 transações por importação</li>
              </ul>
            </div>
          </div>
        )}
      </div>

      {/* Status em Desenvolvimento */}
      <div className="bg-yellow-50 rounded-xl p-6 border border-yellow-200">
        <div className="flex items-center gap-3">
          <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
            <Settings className="h-5 w-5 text-yellow-600" />
          </div>
          <div>
            <h3 className="font-semibold text-yellow-900">🚧 Funcionalidade em Desenvolvimento</h3>
            <p className="text-yellow-800 text-sm mt-1">
              Esta funcionalidade está sendo desenvolvida e estará disponível em breve.
              Você poderá exportar e importar todas as suas configurações de relatórios.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Reports
