const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar cofrinhos do usuário
router.get('/', async (req, res) => {
  try {
    const savings = await prisma.savings.findMany({
      where: { userId: req.user.id },
      include: {
        bank: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(savings);
  } catch (error) {
    console.error('Erro ao buscar cofrinhos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar cofrinho
router.post('/', async (req, res) => {
  try {
    const { name, description, targetAmount, currentAmount, icon, color, bankId, cdiRate } = req.body;

    if (!name || !bankId) {
      return res.status(400).json({ error: 'Nome e banco são obrigatórios' });
    }

    // Verificar se o banco existe e pertence ao usuário
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    const savings = await prisma.savings.create({
      data: {
        name,
        description: description || null,
        targetAmount: targetAmount || 0,
        currentAmount: currentAmount || 0,
        icon: icon || '🐷',
        color: color || '#22C55E',
        cdiRate: cdiRate || 0,
        bankId,
        userId: req.user.id
      },
      include: {
        bank: true
      }
    });

    res.status(201).json(savings);
  } catch (error) {
    console.error('Erro ao criar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar cofrinho
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, targetAmount, currentAmount, icon, color, isLocked, cdiRate } = req.body;

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const updatedSavings = await prisma.savings.update({
      where: { id },
      data: {
        name: name || savings.name,
        description: description !== undefined ? description : savings.description,
        targetAmount: targetAmount !== undefined ? targetAmount : savings.targetAmount,
        currentAmount: currentAmount !== undefined ? currentAmount : savings.currentAmount,
        icon: icon || savings.icon,
        color: color || savings.color,
        isLocked: isLocked !== undefined ? isLocked : savings.isLocked,
        cdiRate: cdiRate !== undefined ? cdiRate : savings.cdiRate
      },
      include: {
        bank: true
      }
    });

    res.json(updatedSavings);
  } catch (error) {
    console.error('Erro ao atualizar cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar cofrinho
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id },
      include: { bank: true }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    // Se há dinheiro no cofrinho, devolver para o banco
    if (savings.currentAmount > 0) {
      await prisma.bank.update({
        where: { id: savings.bankId },
        data: {
          currentBalance: savings.bank.currentBalance + savings.currentAmount
        }
      });
    }

    await prisma.savings.delete({
      where: { id }
    });

    res.json({ message: 'Cofrinho excluído com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Depositar no cofrinho
router.post('/:id/deposit', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, sourceBankId } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    if (!sourceBankId) {
      return res.status(400).json({ error: 'Banco de origem é obrigatório' });
    }

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id },
      include: { bank: true }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const sourceBank = await prisma.bank.findFirst({
      where: { id: sourceBankId, userId: req.user.id }
    });

    if (!sourceBank) {
      return res.status(404).json({ error: 'Banco de origem não encontrado' });
    }

    if (sourceBank.currentBalance < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente no banco de origem' });
    }

    // Realizar operação em transação
    const result = await prisma.$transaction(async (tx) => {
      // 1. Debitar do banco de origem
      await tx.bank.update({
        where: { id: sourceBankId },
        data: { currentBalance: sourceBank.currentBalance - amount }
      });

      // 2. Creditar no cofrinho
      const updatedSavings = await tx.savings.update({
        where: { id },
        data: { currentAmount: savings.currentAmount + amount }
      });

      // 3. Criar transação de investimento
      const transaction = await tx.transaction.create({
        data: {
          description: `Depósito no cofrinho: ${savings.name}`,
          amount: parseFloat(amount),
          type: 'INVESTMENT',
          date: new Date(),
          bankId: sourceBankId,
          userId: req.user.id,
          isPaid: true
        }
      });

      return { savings: updatedSavings, transaction };
    });

    res.json({
      message: 'Depósito realizado com sucesso',
      savings: result.savings,
      transaction: result.transaction
    });
  } catch (error) {
    console.error('Erro ao depositar no cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Sacar do cofrinho
router.post('/:id/withdraw', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount, targetBankId } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    if (!targetBankId) {
      return res.status(400).json({ error: 'Banco de destino é obrigatório' });
    }

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    if (savings.isLocked) {
      return res.status(400).json({ error: 'Cofrinho está bloqueado' });
    }

    if (savings.currentAmount < amount) {
      return res.status(400).json({ error: 'Saldo insuficiente no cofrinho' });
    }

    const targetBank = await prisma.bank.findFirst({
      where: { id: targetBankId, userId: req.user.id }
    });

    if (!targetBank) {
      return res.status(404).json({ error: 'Banco de destino não encontrado' });
    }

    // Realizar operação em transação
    const result = await prisma.$transaction(async (tx) => {
      // 1. Debitar do cofrinho
      const updatedSavings = await tx.savings.update({
        where: { id },
        data: { currentAmount: savings.currentAmount - amount }
      });

      // 2. Creditar no banco de destino
      await tx.bank.update({
        where: { id: targetBankId },
        data: { currentBalance: targetBank.currentBalance + amount }
      });

      // 3. Criar transação de receita
      const transaction = await tx.transaction.create({
        data: {
          description: `Saque do cofrinho: ${savings.name}`,
          amount: parseFloat(amount),
          type: 'INCOME',
          date: new Date(),
          bankId: targetBankId,
          userId: req.user.id,
          isPaid: true
        }
      });

      return { savings: updatedSavings, transaction };
    });

    res.json({
      message: 'Saque realizado com sucesso',
      savings: result.savings,
      transaction: result.transaction
    });
  } catch (error) {
    console.error('Erro ao sacar do cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Adicionar valor ao cofrinho (compatibilidade com PiggyBankManager)
router.post('/:id/add', async (req, res) => {
  try {
    const { id } = req.params;
    const { amount } = req.body;

    if (!amount || amount <= 0) {
      return res.status(400).json({ error: 'Valor deve ser maior que zero' });
    }

    const savings = await prisma.savings.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!savings) {
      return res.status(404).json({ error: 'Cofrinho não encontrado' });
    }

    const updatedSavings = await prisma.savings.update({
      where: { id },
      data: { currentAmount: savings.currentAmount + amount }
    });

    res.json(updatedSavings);
  } catch (error) {
    console.error('Erro ao adicionar valor ao cofrinho:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar CDI de todos os cofrinhos
router.post('/update-cdi', async (req, res) => {
  try {
    const { cdiRate } = req.body;

    if (cdiRate === undefined || cdiRate < 0) {
      return res.status(400).json({ error: 'Taxa CDI deve ser maior ou igual a zero' });
    }

    // Atualizar todos os cofrinhos do usuário com a nova taxa CDI
    const result = await prisma.savings.updateMany({
      where: { userId: req.user.id },
      data: {
        cdiRate: parseFloat(cdiRate),
        lastCdiUpdate: new Date()
      }
    });

    res.json({
      message: 'CDI atualizado com sucesso',
      updatedCount: result.count
    });
  } catch (error) {
    console.error('Erro ao atualizar CDI:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Aplicar rendimento CDI
router.post('/apply-cdi', async (req, res) => {
  try {
    // Buscar todos os cofrinhos do usuário
    const savings = await prisma.savings.findMany({
      where: { userId: req.user.id }
    });

    let updatedCount = 0;

    // Aplicar rendimento baseado na taxa CDI
    for (const saving of savings) {
      if (saving.cdiRate > 0 && saving.currentAmount > 0) {
        const rendimento = saving.currentAmount * (saving.cdiRate / 100) / 12; // Rendimento mensal

        await prisma.savings.update({
          where: { id: saving.id },
          data: {
            currentAmount: saving.currentAmount + rendimento,
            lastCdiUpdate: new Date()
          }
        });

        updatedCount++;
      }
    }

    res.json({
      message: 'Rendimento aplicado com sucesso',
      updatedCount
    });
  } catch (error) {
    console.error('Erro ao aplicar rendimento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
