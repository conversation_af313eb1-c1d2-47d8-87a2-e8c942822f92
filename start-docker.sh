#!/bin/bash
# 🚀 SARA - Deploy Super Simples

echo "🚀 SARA - Deploy Automático"
echo "=========================="

# Verificar se Docker está instalado
if ! command -v docker &> /dev/null; then
    echo "❌ Docker não encontrado!"
    echo "Instale o Docker primeiro:"
    echo "curl -fsSL https://get.docker.com -o get-docker.sh && sudo sh get-docker.sh"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose não encontrado!"
    echo "Instale o Docker Compose primeiro"
    exit 1
fi

# Verificar se arquivo .env existe
if [ ! -f ".env" ]; then
    if [ -f ".env.simple" ]; then
        echo "📋 Copiando configuração de exemplo..."
        cp .env.simple .env
        echo "⚠️  IMPORTANTE: Configure o arquivo .env antes de continuar!"
        echo ""
        echo "Variáveis obrigatórias:"
        echo "- JWT_SECRET (mude para algo seguro)"
        echo "- CLOUDINARY_* (suas credenciais)"
        echo "- GIT_TOKEN (token do GitHub para auto-deploy)"
        echo ""
        read -p "Pressione Enter após configurar o .env..."
    else
        echo "❌ Arquivo .env não encontrado!"
        echo "Crie um arquivo .env com as configurações necessárias"
        exit 1
    fi
fi

# Criar diretórios necessários
echo "📁 Criando diretórios..."
mkdir -p data/{database,logs,backups}

# Parar containers existentes
echo "🛑 Parando containers existentes..."
docker-compose down 2>/dev/null || true

# Limpar imagens antigas (opcional)
read -p "🗑️  Limpar imagens antigas? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🧹 Limpando imagens antigas..."
    docker system prune -f
fi

# Build e start
echo "🏗️  Buildando e iniciando SARA..."
docker-compose up -d --build

# Aguardar inicialização
echo "⏳ Aguardando inicialização..."
sleep 30

# Verificar status
echo "📊 Verificando status..."
if curl -f http://localhost/health > /dev/null 2>&1; then
    echo ""
    echo "🎉 SARA está rodando!"
    echo "=========================="
    echo "🌐 Frontend: http://localhost"
    echo "🔌 Backend:  http://localhost:3001"
    echo "👤 Login:    <EMAIL>"
    echo "🔑 Senha:    123456"
    echo ""
    echo "📋 Comandos úteis:"
    echo "docker-compose logs -f sara     # Ver logs"
    echo "docker-compose restart sara     # Reiniciar"
    echo "docker-compose down             # Parar"
    echo ""
    echo "⚠️  IMPORTANTE: Altere a senha padrão!"
else
    echo "❌ Falha na inicialização!"
    echo "📋 Verificar logs:"
    docker-compose logs sara
fi
