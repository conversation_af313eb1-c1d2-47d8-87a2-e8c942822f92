/**
 * @swagger
 * components:
 *   schemas:
 *     Transaction:
 *       type: object
 *       required:
 *         - description
 *         - amount
 *         - type
 *       properties:
 *         id:
 *           type: string
 *           description: ID único da transação
 *         description:
 *           type: string
 *           description: Descrição da transação
 *         amount:
 *           type: number
 *           description: Valor da transação
 *         type:
 *           type: string
 *           enum: [INCOME, EXPENSE, INVESTMENT, LOAN]
 *           description: Tipo da transação
 *         date:
 *           type: string
 *           format: date-time
 *           description: Data da transação
 */

const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const { upload, uploadToCloudinary } = require('../middleware/upload');
const billService = require('../services/billService');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

/**
 * @swagger
 * /transactions:
 *   get:
 *     summary: Lista todas as transações do usuário
 *     tags: [Transactions]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Lista de transações retornada com sucesso
 *         content:
 *           application/json:
 *             schema:
 *               type: array
 *               items:
 *                 $ref: '#/components/schemas/Transaction'
 *       401:
 *         description: Token de autenticação inválido
 *       500:
 *         description: Erro interno do servidor
 */
// Listar todas as transações do usuário
router.get('/', async (req, res) => {
  try {
    const transactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true,
        loan: {
          include: {
            contact: true
          }
        }
      },
      orderBy: {
        date: 'desc'
      }
    })

    res.json(transactions)
  } catch (error) {
    console.error('Erro ao buscar transações:', error)
    res.status(500).json({ error: 'Erro ao buscar transações' })
  }
})

// Criar uma nova transação
router.post('/', upload.single('receipt'), async (req, res) => {
  try {
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, installments, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Upload do comprovante se fornecido
    let receiptUrl = null;
    if (req.file) {
      try {
        const uploadResult = await uploadToCloudinary(
          req.file.buffer,
          req.file.originalname,
          req.file.mimetype
        );
        receiptUrl = uploadResult.secure_url;
      } catch (uploadError) {
        console.error('Erro ao fazer upload do comprovante:', uploadError);
        // Continuar sem o comprovante se o upload falhar
      }
    }

    // ✅ ATUALIZAR SALDO DO BANCO APENAS PARA TRANSAÇÕES NÃO PARCELADAS
    const numInstallments = parseInt(installments) || 1;
    const isInstallment = numInstallments > 1 && paymentMethodId;

    if (bankId && !isInstallment) {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      // Atualizar saldo do banco apenas para transações normais
      const balanceChange = type === 'INCOME' ? parseFloat(amount) : -parseFloat(amount);
      await prisma.bank.update({
        where: { id: bankId },
        data: { currentBalance: bank.currentBalance + balanceChange }
      });
    }

    // ✅ VERIFICAR FATURA EM ATRASO PARA TRANSAÇÕES NORMAIS DE CARTÃO
    if (paymentMethodId && type === 'EXPENSE' && !isInstallment) {
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (paymentMethod) {
        // Verificar se pode criar transação (fatura não está em atraso)
        if (paymentMethod.type === 'CREDIT' && paymentMethod.bank) {
          const canCreate = await billService.canCreateTransaction(paymentMethod.bank.id, req.user.id);
          if (!canCreate.canCreate) {
            return res.status(400).json({
              error: canCreate.reason,
              billAmount: canCreate.billAmount,
              dueDate: canCreate.dueDate
            });
          }
        }

        // Atualizar fatura do cartão de crédito
        await prisma.paymentMethod.update({
          where: { id: paymentMethodId },
          data: {
            currentBill: paymentMethod.currentBill + parseFloat(amount),
            isBillPaid: false
          }
        });
      }
    }

    if (isInstallment) {
      // Verificar se é cartão de crédito
      const paymentMethod = await prisma.paymentMethod.findFirst({
        where: { id: paymentMethodId, userId: req.user.id, type: 'CREDIT' },
        include: { bank: true }
      });

      if (!paymentMethod) {
        return res.status(400).json({ error: 'Parcelamento só é permitido para cartões de crédito' });
      }



      // Criar transação pai (registro de controle - NÃO SOMA NA FATURA)
      const parentTransaction = await prisma.transaction.create({
        data: {
          description: `[CONTROLE] ${description} (${numInstallments}x - Total: R$ ${parseFloat(amount).toFixed(2)})`,
          amount: parseFloat(amount), // Valor total para controle
          type,
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          receiptUrl: receiptUrl,
          installments: numInstallments,
          currentInstallment: 0, // 0 indica que é transação pai/controle
          date: date ? new Date(date) : new Date(),
          isPaid: true, // ✅ MARCADA COMO PAGA PARA NÃO SOMAR NA FATURA
          installmentStatus: 'CONTROL', // ✅ STATUS CONTROL PARA NÃO AFETAR SALDOS
          userId: req.user.id,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      // ✅ CRIAR TODAS AS PARCELAS COM LÓGICA DE STATUS E DESCONTO
      const installmentAmount = parseFloat(amount) / numInstallments;
      const installmentTransactions = [];
      const transactionDate = new Date(date ? new Date(date) : new Date());

      // Obter dados do banco para calcular datas de vencimento
      const bank = await prisma.bank.findFirst({
        where: { id: paymentMethod.bankId }
      });

      if (!bank || !bank.billDueDay) {
        return res.status(400).json({
          error: 'Banco deve ter dia de vencimento configurado para parcelamento'
        });
      }

      // Verificar se pode criar transação parcelada (fatura não está em atraso)
      const canCreate = await billService.canCreateTransaction(bank.id, req.user.id);
      if (!canCreate.canCreate) {
        return res.status(400).json({
          error: canCreate.reason,
          billAmount: canCreate.billAmount,
          dueDate: canCreate.dueDate
        });
      }

      // Calcular data de vencimento da fatura atual
      const currentDate = new Date();
      const currentBillDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      // Se já passou do dia de vencimento deste mês, a fatura atual vence no próximo mês
      if (currentDate.getDate() > bank.billDueDay) {
        currentBillDueDate.setMonth(currentBillDueDate.getMonth() + 1);
      }

      // Data limite para BANK_BALANCE (vencimento - 1 mês)
      const bankBalanceLimit = new Date(currentBillDueDate);
      bankBalanceLimit.setMonth(bankBalanceLimit.getMonth() - 1);

      // Data de fechamento da fatura atual (mesmo dia do mês atual)
      const currentBillCloseDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      let totalBankBalanceAmount = 0;
      let totalCurrentBillAmount = 0;
      let totalReservedAmount = 0;

      for (let i = 1; i <= numInstallments; i++) {
        const installmentDate = new Date(transactionDate);
        installmentDate.setMonth(installmentDate.getMonth() + (i - 1));

        // Determinar status da parcela baseado na data
        let installmentStatus;

        if (installmentDate < bankBalanceLimit) {
          // Regra 1: Data < (vencimento - 1 mês) = BANK_BALANCE
          installmentStatus = 'BANK_BALANCE';
          totalBankBalanceAmount += installmentAmount;
          console.log(`📅 Parcela ${i} (${installmentDate.toLocaleDateString()}) - BANK_BALANCE (desconta do saldo)`);
        } else if (installmentDate >= bankBalanceLimit && installmentDate <= currentBillCloseDate) {
          // Regra 2: Data dentro do range da fatura atual = CURRENT_BILL
          installmentStatus = 'CURRENT_BILL';
          totalCurrentBillAmount += installmentAmount;
          console.log(`📅 Parcela ${i} (${installmentDate.toLocaleDateString()}) - CURRENT_BILL (desconta do limite)`);
        } else {
          // Regra 3: Data > fechamento da fatura = RESERVED
          installmentStatus = 'RESERVED';
          totalReservedAmount += installmentAmount;
          console.log(`📅 Parcela ${i} (${installmentDate.toLocaleDateString()}) - RESERVED (desconta do limite)`);
        }

        const installmentTransaction = await prisma.transaction.create({
          data: {
            description: description,
            amount: installmentAmount,
            type,
            categoryId: categoryId || null,
            bankId: bankId || null,
            paymentMethodId: paymentMethodId || null,
            transactionContactId: transactionContactId || null,
            installments: numInstallments,
            currentInstallment: i,
            parentTransactionId: parentTransaction.id,
            date: installmentDate,
            isPaid: installmentStatus === 'BANK_BALANCE', // BANK_BALANCE já é "pago" (debitado)
            installmentStatus: installmentStatus,
            userId: req.user.id,
            tags: {
              connect: parsedTagIds.map(id => ({ id }))
            }
          },
          include: {
            category: true,
            bank: true,
            paymentMethod: true,
            transactionContact: true,
            tags: true
          }
        });

        installmentTransactions.push(installmentTransaction);
      }

      // ✅ APLICAR DESCONTOS NOS SALDOS/LIMITES
      console.log(`💰 Resumo dos descontos:`);
      console.log(`   BANK_BALANCE: R$ ${totalBankBalanceAmount.toFixed(2)}`);
      console.log(`   CURRENT_BILL: R$ ${totalCurrentBillAmount.toFixed(2)}`);
      console.log(`   RESERVED: R$ ${totalReservedAmount.toFixed(2)}`);

      // Descontar do saldo do banco (BANK_BALANCE)
      if (totalBankBalanceAmount > 0) {
        await prisma.bank.update({
          where: { id: bank.id },
          data: {
            currentBalance: {
              decrement: totalBankBalanceAmount
            }
          }
        });
        console.log(`✅ Descontado R$ ${totalBankBalanceAmount.toFixed(2)} do saldo do banco`);
      }

      // Descontar do limite disponível (CURRENT_BILL + RESERVED)
      const totalLimitDeduction = totalCurrentBillAmount + totalReservedAmount;
      if (totalLimitDeduction > 0) {
        // Verificar se há limite suficiente
        if (bank.availableLimit < totalLimitDeduction) {
          return res.status(400).json({
            error: `Limite insuficiente. Disponível: R$ ${bank.availableLimit.toFixed(2)}, Necessário: R$ ${totalLimitDeduction.toFixed(2)}`
          });
        }

        await prisma.bank.update({
          where: { id: bank.id },
          data: {
            availableLimit: {
              decrement: totalLimitDeduction
            }
          }
        });
        console.log(`✅ Descontado R$ ${totalLimitDeduction.toFixed(2)} do limite disponível`);
      }

      res.status(201).json({
        message: 'Transação parcelada criada com sucesso',
        controlTransaction: parentTransaction,
        installments: installmentTransactions,
        summary: {
          totalAmount: parseFloat(amount),
          installmentAmount: installmentAmount,
          bankBalanceAmount: totalBankBalanceAmount,
          currentBillAmount: totalCurrentBillAmount,
          reservedAmount: totalReservedAmount
        }
      });
    } else {
      // Transação normal (sem parcelamento)
      // Verificar se é cartão de crédito para definir isPaid
      let isPaidValue = true; // Por padrão, transações são pagas imediatamente
      if (paymentMethodId) {
        const paymentMethod = await prisma.paymentMethod.findFirst({
          where: { id: paymentMethodId, userId: req.user.id }
        });
        if (paymentMethod && paymentMethod.type === 'CREDIT') {
          isPaidValue = false; // Cartão de crédito não é pago imediatamente
        }
      }

      const transaction = await prisma.transaction.create({
        data: {
          description,
          amount: parseFloat(amount),
          type,
          date: date ? new Date(date) : new Date(),
          categoryId: categoryId || null,
          bankId: bankId || null,
          paymentMethodId: paymentMethodId || null,
          transactionContactId: transactionContactId || null,
          isPaid: isPaidValue,
          userId: req.user.id,
          receiptUrl: receiptUrl,
          installments: 1,
          currentInstallment: 1,
          tags: {
            connect: parsedTagIds.map(id => ({ id }))
          }
        },
        include: {
          category: true,
          bank: true,
          paymentMethod: true,
          transactionContact: true,
          tags: true
        }
      });

      res.status(201).json(transaction);
    }
  } catch (error) {
    console.error('Erro ao criar transação:', error);
    res.status(500).json({ error: 'Erro ao criar transação' });
  }
});

// Atualizar uma transação
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params
    const { description, amount, type, date, categoryId, bankId, paymentMethodId, transactionContactId, tagIds } = req.body

    // Validar dados obrigatórios
    if (!description || !amount || !type) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' })
    }

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: {
        id,
        userId: req.user.id
      }
    })

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' })
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Atualizar transação
    const transaction = await prisma.transaction.update({
      where: { id },
      data: {
        description,
        amount: parseFloat(amount),
        type,
        date: date ? new Date(date) : existingTransaction.date,
        categoryId: categoryId || null,
        bankId: bankId || null,
        paymentMethodId: paymentMethodId || null,
        transactionContactId: transactionContactId || null,
        tags: {
          set: parsedTagIds.map(id => ({ id }))
        }
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true
      }
    })

    res.json(transaction)
  } catch (error) {
    console.error('Erro ao atualizar transação:', error)
    res.status(500).json({ error: 'Erro ao atualizar transação' })
  }
})

// Buscar detalhes das parcelas de uma transação
router.get('/:id/installments', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const transaction = await prisma.transaction.findFirst({
      where: { id, userId },
      include: {
        category: true,
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!transaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    let installments = [];

    // Se a transação tem parcelas (installments > 1)
    if (transaction.installments > 1) {
      // Se é uma transação pai (parentTransactionId é null), buscar todas as parcelas filhas
      if (!transaction.parentTransactionId) {
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: id,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      } else {
        // Se é uma parcela filha, buscar todas as parcelas irmãs
        installments = await prisma.transaction.findMany({
          where: {
            parentTransactionId: transaction.parentTransactionId,
            userId
          },
          orderBy: { currentInstallment: 'asc' },
          include: {
            category: true,
            paymentMethod: {
              include: {
                bank: true
              }
            }
          }
        });
      }
    } else {
      // Se não tem parcelas, retornar apenas a transação atual
      installments = [transaction];
    }

    res.json(installments);
  } catch (error) {
    console.error('Erro ao buscar parcelas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar transação
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se a transação pertence ao usuário
    const existingTransaction = await prisma.transaction.findFirst({
      where: { id, userId }
    });

    if (!existingTransaction) {
      return res.status(404).json({ error: 'Transação não encontrada' });
    }

    await prisma.transaction.delete({
      where: { id }
    });

    res.json({ message: 'Transação deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar transação:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Importar transações
router.post('/import', async (req, res) => {
  try {
    const { transactions, allowedCreations, selectedBanksForImpact } = req.body;
    const userId = req.user.id;

    if (!transactions || !Array.isArray(transactions) || transactions.length === 0) {
      return res.status(400).json({ error: 'Dados de transações não fornecidos' });
    }

    // Buscar todos os bancos do usuário
    const banks = await prisma.bank.findMany({
      where: { userId }
    });

    let imported = 0;
    let errors = [];
    const bankImpacts = {};

    for (const [index, transactionData] of transactions.entries()) {
      try {
        // Validar dados obrigatórios
        if (!transactionData.Data || !transactionData.Descrição || !transactionData.Tipo || !transactionData.Valor || !transactionData.Banco) {
          errors.push(`Linha ${index + 2}: Dados obrigatórios não fornecidos`);
          continue;
        }

        // Encontrar banco
        const bank = banks.find(b => b.name.toLowerCase() === transactionData.Banco.toLowerCase());
        if (!bank) {
          errors.push(`Linha ${index + 2}: Banco "${transactionData.Banco}" não encontrado`);
          continue;
        }

        // Converter data
        const [day, month, year] = transactionData.Data.split('/');
        const date = new Date(parseInt(year), parseInt(month) - 1, parseInt(day));

        if (isNaN(date.getTime())) {
          errors.push(`Linha ${index + 2}: Data inválida`);
          continue;
        }

        // Converter valor
        const amount = parseFloat(transactionData.Valor.replace(',', '.'));
        if (isNaN(amount)) {
          errors.push(`Linha ${index + 2}: Valor inválido`);
          continue;
        }

        // Validar tipo
        const type = transactionData.Tipo.toUpperCase();
        if (!['INCOME', 'EXPENSE', 'INVESTMENT', 'LOAN'].includes(type)) {
          errors.push(`Linha ${index + 2}: Tipo inválido`);
          continue;
        }

        // Calcular impacto no banco
        const impact = type === 'INCOME' ? amount : -amount;
        if (!bankImpacts[bank.id]) {
          bankImpacts[bank.id] = 0;
        }
        bankImpacts[bank.id] += impact;

        // Buscar ou criar categoria
        let categoryId = null;
        if (transactionData.Categoria) {
          let category = await prisma.category.findFirst({
            where: {
              name: transactionData.Categoria,
              userId
            }
          });

          if (!category) {
            category = await prisma.category.create({
              data: {
                name: transactionData.Categoria,
                userId
              }
            });
          }
          categoryId = category.id;
        }

        // Buscar ou criar contato
        let transactionContactId = null;
        if (transactionData.Contato) {
          let contact = await prisma.transactionContact.findFirst({
            where: {
              name: transactionData.Contato,
              userId
            }
          });

          if (!contact) {
            contact = await prisma.transactionContact.create({
              data: {
                name: transactionData.Contato,
                userId
              }
            });
          }
          transactionContactId = contact.id;
        }

        // Processar tags
        let tagIds = [];
        if (transactionData.Tags) {
          const tagNames = transactionData.Tags.split(',').map(t => t.trim());
          for (const tagName of tagNames) {
            if (tagName) {
              let tag = await prisma.tag.findFirst({
                where: {
                  name: tagName,
                  userId
                }
              });

              if (!tag) {
                tag = await prisma.tag.create({
                  data: {
                    name: tagName,
                    userId
                  }
                });
              }
              tagIds.push(tag.id);
            }
          }
        }

        // Buscar forma de pagamento (opcional)
        let paymentMethodId = null;
        if (transactionData['Forma de Pagamento']) {
          const paymentMethod = await prisma.paymentMethod.findFirst({
            where: {
              name: transactionData['Forma de Pagamento'],
              userId: userId
            }
          });

          if (paymentMethod) {
            paymentMethodId = paymentMethod.id;
          }
          // Se não encontrar, não cria automaticamente - deixa null
        }

        // Criar transação
        await prisma.transaction.create({
          data: {
            description: transactionData.Descrição,
            amount: amount,
            type: type,
            date: date,
            categoryId: categoryId,
            bankId: bank.id,
            paymentMethodId: paymentMethodId,
            transactionContactId: transactionContactId,
            isPaid: true,
            userId: userId,
            installments: 1,
            currentInstallment: 1,
            tags: {
              connect: tagIds.map(id => ({ id }))
            }
          }
        });

        imported++;
      } catch (error) {
        console.error(`Erro ao importar transação ${index + 2}:`, error);
        errors.push(`Linha ${index + 2}: ${error.message}`);
      }
    }

    // Aplicar impacto nos bancos selecionados
    if (selectedBanksForImpact && selectedBanksForImpact.length > 0) {
      for (const [bankId, impact] of Object.entries(bankImpacts)) {
        if (selectedBanksForImpact.includes(bankId)) {
          await prisma.bank.update({
            where: { id: bankId },
            data: {
              balance: {
                increment: impact
              }
            }
          });
        }
      }
    }

    res.json({
      imported,
      total: transactions.length,
      bankImpacts: selectedBanksForImpact && selectedBanksForImpact.length > 0 ? bankImpacts : undefined,
      appliedBanks: selectedBanksForImpact || [],
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Erro ao importar transações:', error);
    res.status(500).json({ error: 'Erro ao importar transações' });
  }
});

module.exports = router;
