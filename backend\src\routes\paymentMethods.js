const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar formas de pagamento do usuário
router.get('/', async (req, res) => {
  try {
    const paymentMethods = await prisma.paymentMethod.findMany({
      where: { userId: req.user.id },
      include: {
        bank: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(paymentMethods);
  } catch (error) {
    console.error('Erro ao buscar formas de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar forma de pagamento
router.post('/', async (req, res) => {
  try {
    const { name, icon, color, type, bankId, transactionLimit } = req.body;

    if (!name || !type || !bankId) {
      return res.status(400).json({ error: 'Nome, tipo e banco são obrigatórios' });
    }

    const validTypes = ['CREDIT', 'DEBIT', 'CASH', 'PIX', 'TRANSFER', 'OTHER'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({ error: 'Tipo inválido' });
    }

    // Buscar banco para obter billDueDay se for cartão de crédito
    let billDueDate = null;
    if (type === 'CREDIT') {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId: req.user.id }
      });

      if (!bank) {
        return res.status(404).json({ error: 'Banco não encontrado' });
      }

      if (bank.billDueDay) {
        const now = new Date();
        billDueDate = new Date(now.getFullYear(), now.getMonth(), bank.billDueDay);

        // Se a data já passou este mês, usar o próximo mês
        if (billDueDate < now) {
          billDueDate = new Date(now.getFullYear(), now.getMonth() + 1, bank.billDueDay);
        }
      }
    }

    const paymentMethod = await prisma.paymentMethod.create({
      data: {
        name,
        icon: icon || '💳',
        color: color || '#10B981',
        type,
        bankId: bankId,
        billDueDate: billDueDate,
        transactionLimit: transactionLimit || null,
        userId: req.user.id
      },
      include: {
        bank: true
      }
    });

    res.status(201).json(paymentMethod);
  } catch (error) {
    console.error('Erro ao criar forma de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar forma de pagamento
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, icon, color, type } = req.body;

    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!paymentMethod) {
      return res.status(404).json({ error: 'Forma de pagamento não encontrada' });
    }

    const validTypes = ['CREDIT', 'DEBIT', 'CASH', 'PIX', 'TRANSFER', 'OTHER'];
    if (type && !validTypes.includes(type)) {
      return res.status(400).json({ error: 'Tipo inválido' });
    }

    const updatedPaymentMethod = await prisma.paymentMethod.update({
      where: { id },
      data: {
        name: name || paymentMethod.name,
        icon: icon || paymentMethod.icon,
        color: color || paymentMethod.color,
        type: type || paymentMethod.type
      }
    });

    res.json(updatedPaymentMethod);
  } catch (error) {
    console.error('Erro ao atualizar forma de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar forma de pagamento
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!paymentMethod) {
      return res.status(404).json({ error: 'Forma de pagamento não encontrada' });
    }

    // Verificar se há transações vinculadas
    const transactionCount = await prisma.transaction.count({
      where: { paymentMethodId: id }
    });

    if (transactionCount > 0) {
      return res.status(400).json({
        error: 'Não é possível excluir forma de pagamento com transações vinculadas'
      });
    }

    await prisma.paymentMethod.delete({
      where: { id }
    });

    res.json({ message: 'Forma de pagamento excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir forma de pagamento:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar formas de pagamento padrão
router.post('/create-defaults', async (req, res) => {
  try {
    const defaultPaymentMethods = [
      { name: 'Dinheiro', icon: '💵', color: '#22C55E', type: 'CASH' },
      { name: 'Cartão de Crédito', icon: '💳', color: '#3B82F6', type: 'CREDIT' },
      { name: 'Cartão de Débito', icon: '💳', color: '#EF4444', type: 'DEBIT' },
      { name: 'PIX', icon: '📱', color: '#8B5CF6', type: 'PIX' },
      { name: 'Transferência', icon: '🏦', color: '#F59E0B', type: 'TRANSFER' },
      { name: 'Boleto', icon: '📄', color: '#6B7280', type: 'OTHER' }
    ];

    const createdMethods = [];
    for (const method of defaultPaymentMethods) {
      // Verificar se já existe
      const existing = await prisma.paymentMethod.findFirst({
        where: {
          userId: req.user.id,
          name: method.name
        }
      });

      if (!existing) {
        const created = await prisma.paymentMethod.create({
          data: {
            ...method,
            userId: req.user.id
          }
        });
        createdMethods.push(created);
      }
    }

    res.json({
      message: `${createdMethods.length} formas de pagamento criadas`,
      created: createdMethods
    });
  } catch (error) {
    console.error('Erro ao criar formas de pagamento padrão:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura de cartão de crédito
router.post('/:id/pay-bill', async (req, res) => {
  try {
    const { id } = req.params;

    const paymentMethod = await prisma.paymentMethod.findFirst({
      where: { id, userId: req.user.id, type: 'CREDIT' },
      include: { bank: true }
    });

    if (!paymentMethod) {
      return res.status(404).json({ error: 'Cartão de crédito não encontrado' });
    }

    if (paymentMethod.isBillPaid) {
      return res.status(400).json({ error: 'Fatura já está paga' });
    }

    if (!paymentMethod.bank) {
      return res.status(400).json({ error: 'Banco não encontrado para este cartão' });
    }

    if (paymentMethod.bank.currentBalance < paymentMethod.currentBill) {
      return res.status(400).json({ error: 'Saldo insuficiente no banco' });
    }

    // Calcular próxima data de vencimento
    const nextBillDate = new Date(paymentMethod.billDueDate);
    nextBillDate.setMonth(nextBillDate.getMonth() + 1);

    await prisma.$transaction(async (tx) => {
      // Debitar do banco
      await tx.bank.update({
        where: { id: paymentMethod.bankId },
        data: { currentBalance: paymentMethod.bank.currentBalance - paymentMethod.currentBill }
      });

      // ✅ LIBERAR LIMITE DE CRÉDITO PELO VALOR DA FATURA PAGA
      if (paymentMethod.bankId) {
        const bank = await tx.bank.findFirst({
          where: { id: paymentMethod.bankId }
        });

        if (bank) {
          // Calcular novo limite disponível (não pode ultrapassar o limite total)
          const newAvailableLimit = Math.min(
            bank.availableLimit + paymentMethod.currentBill,
            bank.creditLimit || 0
          );

          await tx.bank.update({
            where: { id: paymentMethod.bankId },
            data: {
              availableLimit: newAvailableLimit
            }
          });

          console.log(`💳 Limite liberado: R$ ${paymentMethod.currentBill}`);
          console.log(`💳 Limite disponível: R$ ${newAvailableLimit} / R$ ${bank.creditLimit}`);
        }
      }

      // Atualizar cartão
      await tx.paymentMethod.update({
        where: { id },
        data: {
          currentBill: 0,
          isBillPaid: true,
          billDueDate: nextBillDate
        }
      });

      // ✅ MARCAR TRANSAÇÕES COMO PAGAS COM STATUS CORRETO
      const unpaidTransactions = await tx.transaction.findMany({
        where: {
          paymentMethodId: id,
          isPaid: false,
          userId: req.user.id
        }
      });

      for (const transaction of unpaidTransactions) {
        await tx.transaction.update({
          where: { id: transaction.id },
          data: {
            isPaid: true,
            installmentStatus: transaction.parentTransactionId ? INSTALLMENT_STATUS.PAID : null
          }
        });
      }

      console.log(`✅ ${unpaidTransactions.length} transações marcadas como pagas`);
    });

    // Processamento básico concluído

    res.json({ message: 'Fatura paga com sucesso' });
  } catch (error) {
    console.error('Erro ao pagar fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
