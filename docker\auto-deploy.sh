#!/bin/bash
# 🔄 SARA - Script de Auto Deploy

set -e

# ================================
# Configurações
# ================================
GIT_REPO=${GIT_REPO:-""}
GIT_BRANCH=${GIT_BRANCH:-"main"}
DEPLOY_INTERVAL=${DEPLOY_INTERVAL:-300}
CACHE_DIR="/tmp/git-cache"
LAST_COMMIT_FILE="$CACHE_DIR/last_commit"

# ================================
# Funções Auxiliares
# ================================
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] AUTO-DEPLOY: $1"
}

error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] AUTO-DEPLOY ERROR: $1" >&2
}

# ================================
# Verificar Configuração
# ================================
check_config() {
    if [ "$AUTO_DEPLOY" != "true" ]; then
        log "Auto-deploy desabilitado. Saindo..."
        exit 0
    fi

    if [ -z "$GIT_REPO" ]; then
        error "GIT_REPO não configurado!"
        exit 1
    fi

    log "Configuração:"
    log "  - Repositório: $GIT_REPO"
    log "  - Branch: $GIT_BRANCH"
    log "  - Intervalo: ${DEPLOY_INTERVAL}s"
}

# ================================
# Verificar Mudanças no Git
# ================================
check_git_changes() {
    log "🔍 Verificando mudanças no repositório..."

    # Criar diretório de cache se não existir
    mkdir -p "$CACHE_DIR"

    # Construir URL com token se disponível
    local git_url="$GIT_REPO"
    if [ -n "$GIT_TOKEN" ]; then
        # Converter https://github.com/user/repo.git para https://<EMAIL>/user/repo.git
        git_url=$(echo "$GIT_REPO" | sed "s|https://github.com/|https://${GIT_TOKEN}@github.com/|")
    fi

    # Obter último commit do repositório remoto
    local remote_commit
    remote_commit=$(git ls-remote "$git_url" "$GIT_BRANCH" | cut -f1)

    if [ $? -ne 0 ]; then
        error "Falha ao acessar repositório remoto"
        return 1
    fi

    # Verificar se há commit anterior salvo
    local last_commit=""
    if [ -f "$LAST_COMMIT_FILE" ]; then
        last_commit=$(cat "$LAST_COMMIT_FILE")
    fi

    log "Último commit local: ${last_commit:-"(nenhum)"}"
    log "Último commit remoto: $remote_commit"

    # Comparar commits
    if [ "$remote_commit" != "$last_commit" ]; then
        log "🆕 Novas mudanças detectadas!"
        echo "$remote_commit" > "$LAST_COMMIT_FILE"
        return 0
    else
        log "✅ Nenhuma mudança detectada"
        return 1
    fi
}

# ================================
# Fazer Deploy
# ================================
deploy() {
    log "🚀 Iniciando deploy..."
    
    local temp_dir="/tmp/sara-deploy-$(date +%s)"
    
    # Criar diretório temporário
    mkdir -p "$temp_dir"
    
    # Construir URL com token se disponível
    local git_url="$GIT_REPO"
    if [ -n "$GIT_TOKEN" ]; then
        git_url=$(echo "$GIT_REPO" | sed "s|https://github.com/|https://${GIT_TOKEN}@github.com/|")
    fi

    # Clone do repositório
    log "📦 Clonando repositório..."
    if ! git clone --depth 1 --branch "$GIT_BRANCH" "$git_url" "$temp_dir"; then
        error "Falha ao clonar repositório"
        rm -rf "$temp_dir"
        return 1
    fi
    
    cd "$temp_dir"
    
    # Verificar se é um projeto SARA válido
    if [ ! -f "package.json" ] || [ ! -d "frontend" ] || [ ! -d "backend" ]; then
        error "Repositório não parece ser um projeto SARA válido"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Backup do banco atual
    log "💾 Fazendo backup do banco de dados..."
    cp /app/backend/production.db "/app/logs/backup_pre_deploy_$(date +%Y%m%d_%H%M%S).db"
    
    # Build do frontend
    log "🏗️ Buildando frontend..."
    cd frontend
    if ! npm ci --only=production; then
        error "Falha ao instalar dependências do frontend"
        rm -rf "$temp_dir"
        return 1
    fi
    
    if ! npm run build; then
        error "Falha no build do frontend"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Atualizar frontend
    log "📁 Atualizando arquivos do frontend..."
    rm -rf /app/frontend/dist/*
    cp -r dist/* /app/frontend/dist/
    
    # Atualizar backend
    log "🔧 Atualizando backend..."
    cd ../backend
    
    # Instalar dependências
    if ! npm ci --only=production; then
        error "Falha ao instalar dependências do backend"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Gerar Prisma Client
    if ! npx prisma generate; then
        error "Falha ao gerar Prisma Client"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Executar migrações
    log "🗄️ Executando migrações do banco..."
    if ! npx prisma db push; then
        error "Falha nas migrações do banco"
        rm -rf "$temp_dir"
        return 1
    fi
    
    # Atualizar arquivos do backend (exceto banco)
    log "📁 Atualizando arquivos do backend..."
    rsync -av --exclude='production.db' --exclude='node_modules' . /app/backend/
    
    # Limpar diretório temporário
    rm -rf "$temp_dir"
    
    # Reiniciar serviços
    log "🔄 Reiniciando serviços..."
    supervisorctl restart backend
    supervisorctl restart nginx
    
    # Aguardar serviços subirem
    sleep 10
    
    # Verificar saúde
    if curl -f http://localhost/api/health > /dev/null 2>&1; then
        log "✅ Deploy concluído com sucesso!"
        
        # Notificar webhook se configurado
        if [ -n "$WEBHOOK_URL" ]; then
            curl -X POST "$WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{\"status\":\"success\",\"message\":\"Deploy realizado com sucesso\",\"timestamp\":\"$(date -Iseconds)\"}" \
                > /dev/null 2>&1 || true
        fi
        
        return 0
    else
        error "❌ Falha na verificação de saúde após deploy"
        
        # Notificar webhook se configurado
        if [ -n "$WEBHOOK_URL" ]; then
            curl -X POST "$WEBHOOK_URL" \
                -H "Content-Type: application/json" \
                -d "{\"status\":\"error\",\"message\":\"Falha na verificação de saúde\",\"timestamp\":\"$(date -Iseconds)\"}" \
                > /dev/null 2>&1 || true
        fi
        
        return 1
    fi
}

# ================================
# Loop Principal
# ================================
main() {
    log "🔄 Iniciando monitoramento de auto-deploy..."
    
    # Verificar configuração
    check_config
    
    # Loop infinito
    while true; do
        if check_git_changes; then
            if deploy; then
                log "🎉 Deploy realizado com sucesso!"
            else
                error "❌ Falha no deploy"
            fi
        fi
        
        log "😴 Aguardando ${DEPLOY_INTERVAL}s até próxima verificação..."
        sleep "$DEPLOY_INTERVAL"
    done
}

# ================================
# Tratamento de Sinais
# ================================
trap 'log "🛑 Recebido sinal de parada. Finalizando..."; exit 0' SIGTERM SIGINT

# ================================
# Executar
# ================================
main
