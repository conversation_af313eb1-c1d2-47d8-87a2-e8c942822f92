import React, { useState, useEffect } from 'react'

const PercentageInput = ({ 
  value, 
  onChange, 
  className = '', 
  placeholder = '0,0%',
  min = 0,
  max = 100,
  step = 0.1,
  disabled = false
}) => {
  const [displayValue, setDisplayValue] = useState('')
  const [isFocused, setIsFocused] = useState(false)

  useEffect(() => {
    if (!isFocused) {
      // Quando não está focado, mostrar valor formatado
      setDisplayValue(formatPercentage(value))
    }
  }, [value, isFocused])

  const formatPercentage = (val) => {
    if (val === null || val === undefined || val === '') return ''
    const num = parseFloat(val)
    if (isNaN(num)) return ''
    return num.toLocaleString('pt-BR', { 
      minimumFractionDigits: 1, 
      maximumFractionDigits: 1 
    }) + '%'
  }

  const parsePercentage = (str) => {
    if (!str) return 0
    // Remove tudo exceto números, vírgulas e pontos
    const cleaned = str.replace(/[^\d,.-]/g, '')
    // Substitui vírgula por ponto
    const normalized = cleaned.replace(',', '.')
    const num = parseFloat(normalized)
    return isNaN(num) ? 0 : Math.max(min, Math.min(max, num))
  }

  const handleFocus = () => {
    setIsFocused(true)
    // Quando foca, mostrar apenas o número sem formatação
    const numValue = parseFloat(value) || 0
    setDisplayValue(numValue.toString().replace('.', ','))
  }

  const handleBlur = () => {
    setIsFocused(false)
    // Quando perde o foco, aplicar a mudança e formatar
    const numValue = parsePercentage(displayValue)
    onChange(numValue)
  }

  const handleChange = (e) => {
    const inputValue = e.target.value
    
    if (isFocused) {
      // Durante a edição, permitir digitação livre (com algumas validações)
      const cleaned = inputValue.replace(/[^\d,.-]/g, '')
      setDisplayValue(cleaned)
    } else {
      // Se não está focado, processar imediatamente
      const numValue = parsePercentage(inputValue)
      onChange(numValue)
    }
  }

  const handleKeyDown = (e) => {
    // Permitir teclas especiais
    const allowedKeys = [
      'Backspace', 'Delete', 'Tab', 'Escape', 'Enter',
      'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
      'Home', 'End'
    ]

    if (allowedKeys.includes(e.key)) {
      if (e.key === 'Enter') {
        e.target.blur()
      }
      return
    }

    // Permitir Ctrl+A, Ctrl+C, Ctrl+V, etc.
    if (e.ctrlKey || e.metaKey) {
      return
    }

    // Permitir números, vírgula e ponto
    if (!/[\d,.-]/.test(e.key)) {
      e.preventDefault()
    }

    // Permitir apenas uma vírgula ou ponto
    if ((e.key === ',' || e.key === '.') && (displayValue.includes(',') || displayValue.includes('.'))) {
      e.preventDefault()
    }
  }

  const handleKeyUp = (e) => {
    // Setas para cima/baixo para incrementar/decrementar
    if (e.key === 'ArrowUp') {
      e.preventDefault()
      const currentValue = parsePercentage(displayValue)
      const newValue = Math.min(max, currentValue + step)
      onChange(newValue)
    } else if (e.key === 'ArrowDown') {
      e.preventDefault()
      const currentValue = parsePercentage(displayValue)
      const newValue = Math.max(min, currentValue - step)
      onChange(newValue)
    }
  }

  return (
    <div className="relative">
      <input
        type="text"
        value={displayValue}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onKeyUp={handleKeyUp}
        placeholder={placeholder}
        disabled={disabled}
        className={`
          ${className}
          ${isFocused ? 'ring-2 ring-blue-500 border-transparent' : ''}
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}
        `}
      />
      
      {/* Indicador visual quando focado */}
      {isFocused && (
        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-400 pointer-events-none">
          %
        </div>
      )}
      
      {/* Tooltip com dicas */}
      {isFocused && (
        <div className="absolute top-full left-0 mt-1 z-10 bg-gray-800 text-white text-xs rounded px-2 py-1 whitespace-nowrap">
          Use ↑↓ para ajustar • Enter para confirmar
        </div>
      )}
    </div>
  )
}

export default PercentageInput
