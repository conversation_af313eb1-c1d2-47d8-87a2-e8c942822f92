import React from 'react'
import { X, Calendar, DollarSign, Tag, CreditCard, Building, Eye, Download, FileText, User, ExternalLink } from 'lucide-react'

function TransactionDetailModal({ isOpen, onClose, transaction, onContactClick, onLoanClick }) {
  if (!isOpen || !transaction) return null

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getTypeLabel = (type) => {
    switch (type) {
      case 'INCOME': return 'Receita'
      case 'EXPENSE': return 'Despesa'
      case 'INVESTMENT': return 'Investimento'
      case 'LOAN': return 'Empréstimo'
      default: return type
    }
  }

  const getTypeColor = (type) => {
    switch (type) {
      case 'INCOME': return 'bg-green-100 text-green-800'
      case 'EXPENSE': return 'bg-red-100 text-red-800'
      case 'INVESTMENT': return 'bg-blue-100 text-blue-800'
      case 'LOAN': return 'bg-yellow-100 text-yellow-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getTypeIcon = (type) => {
    switch (type) {
      case 'INCOME': return '💰'
      case 'EXPENSE': return '💸'
      case 'INVESTMENT': return '📈'
      case 'LOAN': return '🏦'
      default: return '💳'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 px-6 py-4 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-2xl">
                {getTypeIcon(transaction.type)}
              </div>
              <div>
                <h2 className="text-xl font-bold">Detalhes da Transação</h2>
                <p className="text-blue-100 text-sm">
                  {getTypeLabel(transaction.type)} • {formatDate(transaction.date)}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Informações Principais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Descrição e Valor */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <FileText className="inline h-4 w-4 mr-1" />
                  Descrição
                </label>
                <p className="text-lg font-semibold text-gray-900 bg-gray-50 p-3 rounded-lg">
                  {transaction.description}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <DollarSign className="inline h-4 w-4 mr-1" />
                  Valor
                </label>
                <p className={`text-2xl font-bold p-3 rounded-lg ${
                  transaction.type === 'INCOME'
                    ? 'text-green-600 bg-green-50'
                    : 'text-red-600 bg-red-50'
                }`}>
                  {transaction.type === 'INCOME' ? '+' : '-'} {formatCurrency(transaction.amount)}
                </p>
              </div>
            </div>

            {/* Tipo e Data */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <Tag className="inline h-4 w-4 mr-1" />
                  Tipo
                </label>
                <span className={`inline-flex items-center px-3 py-2 rounded-lg text-sm font-medium ${getTypeColor(transaction.type)}`}>
                  {getTypeIcon(transaction.type)} {getTypeLabel(transaction.type)}
                </span>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <Calendar className="inline h-4 w-4 mr-1" />
                  Data da Transação
                </label>
                <p className="text-lg text-gray-900 bg-gray-50 p-3 rounded-lg">
                  {formatDate(transaction.date)}
                </p>
              </div>
            </div>
          </div>

          {/* Informações Adicionais */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            {/* Categoria */}
            {transaction.category && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  Categoria
                </label>
                <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
                  <span className="text-2xl">{transaction.category.icon}</span>
                  <span className="text-lg text-gray-900">{transaction.category.name}</span>
                </div>
              </div>
            )}

            {/* Tags */}
            {transaction.tags && transaction.tags.length > 0 && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <Tag className="inline h-4 w-4 mr-1" />
                  Tags
                </label>
                <div className="flex flex-wrap gap-2 bg-gray-50 p-3 rounded-lg">
                  {transaction.tags.map((tag) => (
                    <div
                      key={tag.id}
                      className="inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-sm"
                      style={{ backgroundColor: tag.color, color: '#FFF' }}
                    >
                      <Tag className="h-3.5 w-3.5" />
                      <span>{tag.name}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Banco */}
            {transaction.bank && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <Building className="inline h-4 w-4 mr-1" />
                  Banco
                </label>
                <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
                  <span className="text-xl">{transaction.bank.icon}</span>
                  <span className="text-lg text-gray-900">{transaction.bank.name}</span>
                </div>
              </div>
            )}

            {/* Forma de Pagamento */}
            {transaction.paymentMethod && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <CreditCard className="inline h-4 w-4 mr-1" />
                  Forma de Pagamento
                </label>
                <div className="flex items-center gap-2 bg-gray-50 p-3 rounded-lg">
                  <span className="text-xl">{transaction.paymentMethod.icon}</span>
                  <span className="text-lg text-gray-900">{transaction.paymentMethod.name}</span>
                </div>
              </div>
            )}

            {/* Contato */}
            {(transaction.transactionContact || transaction.contact) && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <User className="inline h-4 w-4 mr-1" />
                  Contato Relacionado
                </label>
                <div
                  className="bg-gradient-to-r from-blue-50 to-purple-50 p-3 rounded-lg border border-blue-200 cursor-pointer hover:from-blue-100 hover:to-purple-100 transition-all duration-200"
                  onClick={() => onContactClick && onContactClick(transaction.transactionContact || transaction.contact)}
                >
                  <div className="flex items-center gap-3">
                    {/* Foto do contato */}
                    <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-white shadow-sm flex-shrink-0">
                      {(transaction.transactionContact?.photo || transaction.contact?.photo) ? (
                        <img
                          src={transaction.transactionContact?.photo || transaction.contact?.photo}
                          alt={(transaction.transactionContact?.name || transaction.contact?.name)}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null
                            e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(transaction.transactionContact?.name || transaction.contact?.name)}&background=random&size=48&bold=true&color=fff`
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                          <span className="text-white font-bold text-lg">
                            {(transaction.transactionContact?.name || transaction.contact?.name).charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Informações do contato */}
                    <div className="flex-1 min-w-0">
                      <p className="text-lg font-semibold text-gray-900 truncate">
                        {transaction.transactionContact?.name || transaction.contact?.name}
                      </p>
                      {(transaction.transactionContact?.description || transaction.contact?.description) && (
                        <p className="text-sm text-gray-600 truncate">
                          {transaction.transactionContact?.description || transaction.contact?.description}
                        </p>
                      )}

                      {/* Informações de contato */}
                      <div className="flex gap-4 mt-1 text-xs text-gray-500">
                        {(transaction.transactionContact?.email || transaction.contact?.email) && (
                          <span className="flex items-center gap-1">
                            📧 {transaction.transactionContact?.email || transaction.contact?.email}
                          </span>
                        )}
                        {(transaction.transactionContact?.phone || transaction.contact?.phone) && (
                          <span className="flex items-center gap-1">
                            📱 {transaction.transactionContact?.phone || transaction.contact?.phone}
                          </span>
                        )}
                      </div>

                      <p className="text-xs text-blue-600 font-medium mt-2 flex items-center gap-1">
                        <ExternalLink className="h-3 w-3" />
                        Clique para ver detalhes do contato
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Parcelamento */}
            {transaction.installments > 1 && (
              <div>
                <label className="block text-sm font-medium text-gray-600 mb-1">
                  <CreditCard className="inline h-4 w-4 mr-1" />
                  Parcelamento
                </label>
                <div className="bg-blue-50 p-3 rounded-lg">
                  <p className="text-lg font-semibold text-blue-900">
                    Parcela {transaction.currentInstallment}/{transaction.installments}
                  </p>
                  <p className="text-sm text-blue-700">
                    {transaction.installmentStatus === 'CONTROL' ? 'Valor por parcela: '+ formatCurrency(transaction.amount / transaction.installments) : "Valor desta parcela: "+ formatCurrency(transaction.amount)}
                  </p>
                  <p className="text-sm text-blue-700">
                    Total do parcelamento: {transaction.installmentStatus === 'CONTROL' ? formatCurrency(transaction.amount) : formatCurrency(transaction.amount * transaction.installments)}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Comprovante */}
          {transaction.receiptUrl && (
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-600 mb-3">
                <Eye className="inline h-4 w-4 mr-1" />
                Comprovante
              </label>
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="flex items-center gap-4">
                  {/* Thumbnail */}
                  <div className="w-20 h-20 rounded-lg border overflow-hidden bg-white">
                    {transaction.receiptUrl.includes('image') || transaction.receiptUrl.match(/\.(jpg|jpeg|png|gif|webp)$/i) ? (
                      <img
                        src={transaction.receiptUrl}
                        alt="Comprovante"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          e.target.style.display = 'none'
                          e.target.nextSibling.style.display = 'flex'
                        }}
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center text-gray-400 text-2xl">
                        📄
                      </div>
                    )}
                    <div className="w-full h-full hidden items-center justify-center text-gray-400 text-2xl">
                      📄
                    </div>
                  </div>

                  {/* Ações */}
                  <div className="flex-1">
                    <p className="text-sm text-gray-600 mb-3">
                      Clique para visualizar o comprovante em tamanho completo
                    </p>
                    <div className="flex gap-2">
                      <button
                        onClick={() => window.open(transaction.receiptUrl, '_blank')}
                        className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                      >
                        <Eye className="h-4 w-4" />
                        Visualizar
                      </button>
                      <a
                        href={transaction.receiptUrl}
                        download
                        className="flex items-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                      >
                        <Download className="h-4 w-4" />
                        Download
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Empréstimo Relacionado */}
          {transaction.loanId && (
            <div className="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 p-4 rounded-lg mb-6">
              <h4 className="font-medium text-green-900 mb-3 flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Empréstimo/Dívida Relacionado
              </h4>
              <p className="text-green-700 text-sm mb-4">
                Esta transação foi gerada automaticamente a partir de um empréstimo/dívida.
              </p>

              {/* Card do contato do empréstimo (se disponível) */}
              {transaction.loan?.contact && (
                <div className="bg-white border border-green-200 p-3 rounded-lg mb-4">
                  <div className="flex items-center gap-3">
                    {/* Foto do contato do empréstimo */}
                    <div className="w-10 h-10 rounded-full overflow-hidden border-2 border-green-200 flex-shrink-0">
                      {transaction.loan.contact.photo ? (
                        <img
                          src={transaction.loan.contact.photo}
                          alt={transaction.loan.contact.name}
                          className="w-full h-full object-cover"
                          onError={(e) => {
                            e.target.onerror = null
                            e.target.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(transaction.loan.contact.name)}&background=random&size=40&bold=true&color=fff`
                          }}
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-br from-green-500 to-emerald-600 flex items-center justify-center">
                          <span className="text-white font-bold text-sm">
                            {transaction.loan.contact.name.charAt(0).toUpperCase()}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Informações do contato do empréstimo */}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-semibold text-green-900 truncate">
                        {transaction.loan.contact.name}
                      </p>
                      <p className="text-xs text-green-600">
                        Contato do empréstimo/dívida
                      </p>
                    </div>
                  </div>
                </div>
              )}

              <button
                onClick={() => onLoanClick && onLoanClick(transaction.loanId)}
                className="flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                <ExternalLink className="h-4 w-4" />
                Ir para Empréstimo/Dívida
              </button>
            </div>
          )}

          {/* Informações do Sistema */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-medium text-gray-900 mb-3">Informações do Sistema</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">ID da Transação:</span>
                <span className="ml-2 font-mono text-gray-900">{transaction.id}</span>
              </div>
              <div>
                <span className="text-gray-600">Criado em:</span>
                <span className="ml-2 text-gray-900">{formatDate(transaction.createdAt)}</span>
              </div>
              {transaction.updatedAt !== transaction.createdAt && (
                <div>
                  <span className="text-gray-600">Última atualização:</span>
                  <span className="ml-2 text-gray-900">{formatDate(transaction.updatedAt)}</span>
                </div>
              )}
              {transaction.loanId && (
                <div>
                  <span className="text-gray-600">ID do Empréstimo:</span>
                  <span className="ml-2 font-mono text-gray-900">{transaction.loanId}</span>
                </div>
              )}
              {transaction.loanPaymentId && (
                <div>
                  <span className="text-gray-600">ID da Parcela:</span>
                  <span className="ml-2 font-mono text-gray-900">{transaction.loanPaymentId}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 px-6 py-4 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default TransactionDetailModal
