const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

class DateService {
  constructor() {
    this.simulatedDate = null;
  }

  /**
   * Define uma data simulada para testes
   */
  async setSimulatedDate(date) {
    try {
      const dateObj = new Date(date);
      
      if (isNaN(dateObj.getTime())) {
        throw new Error('Data inválida');
      }

      this.simulatedDate = dateObj;
      
      // Salvar no banco para persistir entre reinicializações
      await prisma.systemConfig.upsert({
        where: { key: 'simulatedDate' },
        update: { value: dateObj.toISOString() },
        create: { 
          key: 'simulatedDate', 
          value: dateObj.toISOString() 
        }
      });

      console.log(`📅 Data simulada definida: ${dateObj.toLocaleDateString()}`);
      return { success: true, simulatedDate: dateObj };
      
    } catch (error) {
      console.error('❌ Erro ao definir data simulada:', error);
      throw error;
    }
  }

  /**
   * Remove a data simulada (volta para data real)
   */
  async clearSimulatedDate() {
    try {
      this.simulatedDate = null;
      
      // Remover do banco
      await prisma.systemConfig.deleteMany({
        where: { key: 'simulatedDate' }
      });

      console.log('📅 Data simulada removida - usando data real');
      return { success: true, message: 'Usando data real do sistema' };
      
    } catch (error) {
      console.error('❌ Erro ao remover data simulada:', error);
      throw error;
    }
  }

  /**
   * Obtém a data atual (simulada ou real)
   */
  async getCurrentDate() {
    try {
      // Se não há data simulada em memória, verificar no banco
      if (!this.simulatedDate) {
        const config = await prisma.systemConfig.findUnique({
          where: { key: 'simulatedDate' }
        });

        if (config && config.value) {
          this.simulatedDate = new Date(config.value);
        }
      }

      return this.simulatedDate || new Date();
      
    } catch (error) {
      console.error('❌ Erro ao obter data atual:', error);
      // Em caso de erro, usar data real
      return new Date();
    }
  }

  /**
   * Verifica se está usando data simulada
   */
  async isUsingSimulatedDate() {
    const currentDate = await this.getCurrentDate();
    return this.simulatedDate !== null;
  }

  /**
   * Obtém informações sobre a data atual
   */
  async getDateInfo() {
    try {
      const currentDate = await this.getCurrentDate();
      const realDate = new Date();
      const isSimulated = await this.isUsingSimulatedDate();

      return {
        currentDate,
        realDate,
        isSimulated,
        currentDateString: currentDate.toLocaleDateString(),
        realDateString: realDate.toLocaleDateString(),
        difference: isSimulated ? Math.ceil((currentDate - realDate) / (1000 * 60 * 60 * 24)) : 0
      };
      
    } catch (error) {
      console.error('❌ Erro ao obter informações da data:', error);
      throw error;
    }
  }

  /**
   * Avança a data simulada em X dias
   */
  async advanceDate(days) {
    try {
      const currentDate = await this.getCurrentDate();
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() + days);

      await this.setSimulatedDate(newDate);
      
      console.log(`📅 Data avançada ${days} dias: ${newDate.toLocaleDateString()}`);
      return { success: true, newDate, days };
      
    } catch (error) {
      console.error('❌ Erro ao avançar data:', error);
      throw error;
    }
  }

  /**
   * Retrocede a data simulada em X dias
   */
  async rewindDate(days) {
    try {
      const currentDate = await this.getCurrentDate();
      const newDate = new Date(currentDate);
      newDate.setDate(newDate.getDate() - days);

      await this.setSimulatedDate(newDate);
      
      console.log(`📅 Data retrocedida ${days} dias: ${newDate.toLocaleDateString()}`);
      return { success: true, newDate, days };
      
    } catch (error) {
      console.error('❌ Erro ao retroceder data:', error);
      throw error;
    }
  }
}

// Instância singleton
const dateService = new DateService();

module.exports = dateService;
