const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const billService = require('../services/billService');
const billUpdateService = require('../services/billUpdateService');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware de autenticação para todas as rotas
router.use(authenticateToken);

// Obter fatura de um banco
router.get('/bank/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Obter dados completos da fatura
    const billData = await billService.getBillData(bankId, req.user.id);

    res.json({
      bill: billData.bill,
      bank: billData.bank,
      transactions: billData.transactions,
      subscriptions: billData.subscriptions,
      totals: billData.totals
    });

  } catch (error) {
    console.error('Erro ao obter fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura
router.post('/bank/:bankId/pay', async (req, res) => {
  try {
    const { bankId } = req.params;
    const { paymentBankId } = req.body;

    if (!paymentBankId) {
      return res.status(400).json({ error: 'ID do banco de pagamento é obrigatório' });
    }

    const result = await billService.payBill(bankId, paymentBankId, req.user.id);

    res.json(result);

  } catch (error) {
    console.error('Erro ao pagar fatura:', error);
    res.status(500).json({ error: error.message || 'Erro interno do servidor' });
  }
});

// Listar todas as faturas do usuário
router.get('/', async (req, res) => {
  try {
    // Buscar todos os bancos com cartão de crédito
    const banks = await prisma.bank.findMany({
      where: {
        userId: req.user.id,
        creditLimit: { gt: 0 },
        billDueDay: { not: null }
      },
      include: {
        paymentMethods: {
          where: { type: 'CREDIT' }
        }
      }
    });

    const billsData = [];

    for (const bank of banks) {
      try {
        const billData = await billService.getBillData(bank.id, req.user.id);
        billsData.push(billData);
      } catch (error) {
        console.error(`Erro ao obter fatura do banco ${bank.id}:`, error);
        // Continuar com outros bancos mesmo se um falhar
      }
    }

    res.json(billsData);

  } catch (error) {
    console.error('Erro ao listar faturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Executar atualização manual de faturas
router.post('/update', async (req, res) => {
  try {
    console.log('🔄 Executando atualização manual de faturas...');

    await billUpdateService.runManualUpdate();

    const status = billUpdateService.getStatus();

    res.json({
      success: true,
      message: 'Atualização de faturas executada com sucesso',
      status: status
    });

  } catch (error) {
    console.error('Erro na atualização manual de faturas:', error);
    res.status(500).json({
      error: 'Erro na atualização de faturas',
      details: error.message
    });
  }
});

// Obter status do serviço de atualização
router.get('/update/status', async (req, res) => {
  try {
    const status = billUpdateService.getStatus();

    res.json({
      service: 'Bill Update Service',
      ...status
    });

  } catch (error) {
    console.error('Erro ao obter status do serviço:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
