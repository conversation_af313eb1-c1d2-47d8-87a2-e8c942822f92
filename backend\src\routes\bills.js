const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const billService = require('../services/billService');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware de autenticação para todas as rotas
router.use(authenticateToken);

// Obter fatura de um banco (básico)
router.get('/bank/:bankId', async (req, res) => {
  try {
    const { bankId } = req.params;

    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId: req.user.id }
    });

    if (!bank) {
      return res.status(404).json({ error: 'Banco não encontrado' });
    }

    // Calcular valor básico da fatura
    const billData = await billService.calculateBillAmount(bankId);

    res.json({
      bank: {
        id: bank.id,
        name: bank.name
      },
      transactions: billData.transactions,
      subscriptions: billData.subscriptions,
      totalAmount: billData.total,
      transactionsTotal: billData.transactionsTotal,
      subscriptionsTotal: billData.subscriptionsTotal
    });

  } catch (error) {
    console.error('Erro ao obter fatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Pagar fatura (básico)
router.post('/bank/:bankId/pay', async (req, res) => {
  try {
    const { bankId } = req.params;
    const { paymentBankId } = req.body;

    if (!paymentBankId) {
      return res.status(400).json({ error: 'ID do banco de pagamento é obrigatório' });
    }

    const result = await billService.payBill(bankId, paymentBankId, req.user.id);

    res.json(result);

  } catch (error) {
    console.error('Erro ao pagar fatura:', error);
    res.status(500).json({ error: error.message || 'Erro interno do servidor' });
  }
});

module.exports = router;
