import React from 'react'
import { TrendingUp, TrendingDown, Target, AlertTriangle, CheckCircle } from 'lucide-react'

const GreenMeter = ({ score, title, current, target, type = 'percentage' }) => {
  // Determinar cor baseada no score
  const getColor = (score) => {
    if (score >= 80) return 'green'
    if (score >= 60) return 'yellow'
    if (score >= 40) return 'orange'
    return 'red'
  }

  // Determinar ícone baseado no score
  const getIcon = (score) => {
    if (score >= 80) return <CheckCircle className="h-5 w-5" />
    if (score >= 60) return <Target className="h-5 w-5" />
    if (score >= 40) return <TrendingDown className="h-5 w-5" />
    return <AlertTriangle className="h-5 w-5" />
  }

  // Determinar mensagem baseada no score
  const getMessage = (score) => {
    if (score >= 80) return 'Excelente!'
    if (score >= 60) return 'Bom'
    if (score >= 40) return 'Atenção'
    return 'Crítico'
  }

  const color = getColor(score)
  const icon = getIcon(score)
  const message = getMessage(score)

  // Cores para cada nível
  const colors = {
    green: {
      bg: 'bg-green-50',
      border: 'border-green-200',
      text: 'text-green-800',
      meter: 'bg-green-500',
      icon: 'text-green-600'
    },
    yellow: {
      bg: 'bg-yellow-50',
      border: 'border-yellow-200',
      text: 'text-yellow-800',
      meter: 'bg-yellow-500',
      icon: 'text-yellow-600'
    },
    orange: {
      bg: 'bg-orange-50',
      border: 'border-orange-200',
      text: 'text-orange-800',
      meter: 'bg-orange-500',
      icon: 'text-orange-600'
    },
    red: {
      bg: 'bg-red-50',
      border: 'border-red-200',
      text: 'text-red-800',
      meter: 'bg-red-500',
      icon: 'text-red-600'
    }
  }

  const colorClasses = colors[color]

  return (
    <div className={`p-4 rounded-lg border-2 ${colorClasses.bg} ${colorClasses.border}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <h3 className={`font-semibold ${colorClasses.text}`}>{title}</h3>
        <div className={`${colorClasses.icon}`}>
          {icon}
        </div>
      </div>

      {/* Meter */}
      <div className="mb-3">
        <div className="flex justify-between items-center mb-1">
          <span className={`text-sm ${colorClasses.text}`}>{message}</span>
          <span className={`text-sm font-bold ${colorClasses.text}`}>
            {Math.round(score)}%
          </span>
        </div>
        
        {/* Barra de progresso */}
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className={`h-3 rounded-full transition-all duration-500 ${colorClasses.meter}`}
            style={{ width: `${Math.min(score, 100)}%` }}
          />
        </div>
      </div>

      {/* Valores */}
      <div className="flex justify-between text-xs">
        <div className={colorClasses.text}>
          <span className="font-medium">Atual: </span>
          {type === 'percentage' ? `${current.toFixed(1)}%` : `R$ ${current.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
        </div>
        <div className={colorClasses.text}>
          <span className="font-medium">Meta: </span>
          {type === 'percentage' ? `${target.toFixed(1)}%` : `R$ ${target.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`}
        </div>
      </div>
    </div>
  )
}

export default GreenMeter
