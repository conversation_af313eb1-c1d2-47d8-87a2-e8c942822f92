const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const cloudinary = require('../config/cloudinary');

// Verificar se o Cloudinary está configurado
console.log('Cloudinary config:', {
  cloud_name: cloudinary.config().cloud_name,
  api_key: cloudinary.config().api_key,
  api_secret: cloudinary.config().api_secret ? '***' : 'undefined'
});

const router = express.Router();
const prisma = new PrismaClient();

// Configurar multer para upload de arquivos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Apenas imagens são permitidas'));
    }
  }
});

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar todos os objetivos do usuário
router.get('/', async (req, res) => {
  try {
    const userId = req.user.id;
    const goals = await prisma.goal.findMany({
      where: { userId },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(goals);
  } catch (error) {
    console.error('Erro ao buscar objetivos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar novo objetivo
router.post('/', upload.single('image'), async (req, res) => {
  try {
    const { name, description, targetAmount, targetYear, targetMonth, categoryIds, tagIds } = req.body;
    const userId = req.user.id;

    if (!name || !targetAmount || !targetYear || !targetMonth) {
      return res.status(400).json({ error: 'Dados obrigatórios não fornecidos' });
    }

    // Validar data mínima (3 meses no futuro)
    const currentDate = new Date();
    const targetDate = new Date(parseInt(targetYear), parseInt(targetMonth) - 1, 1);
    const minDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 3, 1);

    if (targetDate < minDate) {
      const minMonth = minDate.getMonth() + 1;
      const minYear = minDate.getFullYear();
      return res.status(400).json({
        error: `O objetivo deve ser planejado para pelo menos 3 meses no futuro. Data mínima: ${minMonth.toString().padStart(2, '0')}/${minYear}`
      });
    }

    // Parse dos IDs
    let parsedCategoryIds = [];
    let parsedTagIds = [];

    if (categoryIds) {
      try {
        parsedCategoryIds = Array.isArray(categoryIds) ? categoryIds : JSON.parse(categoryIds);
      } catch (e) {
        return res.status(400).json({ error: 'IDs de categorias inválidos' });
      }
    }

    if (tagIds) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds);
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' });
      }
    }

    // Verificar se categorias e tags pertencem ao usuário
    if (parsedCategoryIds.length > 0) {
      const categories = await prisma.category.findMany({
        where: {
          id: { in: parsedCategoryIds },
          userId
        }
      });
      if (categories.length !== parsedCategoryIds.length) {
        return res.status(400).json({ error: 'Uma ou mais categorias inválidas' });
      }
    }

    if (parsedTagIds.length > 0) {
      const tags = await prisma.tag.findMany({
        where: {
          id: { in: parsedTagIds },
          userId
        }
      });
      if (tags.length !== parsedTagIds.length) {
        return res.status(400).json({ error: 'Uma ou mais tags inválidas' });
      }
    }

    // Processar upload de imagem se houver
    let imageUrl = null;
    if (req.file) {
      console.log('Arquivo recebido:', req.file.originalname, req.file.mimetype);
      console.log('Buffer size:', req.file.buffer ? req.file.buffer.length : 'undefined');
      try {
        const result = await new Promise((resolve, reject) => {
          const uploadStream = cloudinary.uploader.upload_stream(
            {
              resource_type: 'image',
              folder: 'goals',
              transformation: [
                { width: 800, height: 600, crop: 'fill' }
              ]
            },
            (error, result) => {
              if (error) {
                console.error('Erro no Cloudinary:', error);
                reject(error);
              } else {
                console.log('Resultado do Cloudinary:', result);
                resolve(result);
              }
            }
          );

          uploadStream.end(req.file.buffer);
        });

        imageUrl = result.secure_url;
        console.log('Upload realizado com sucesso:', imageUrl);
      } catch (uploadError) {
        console.error('Erro no upload da imagem:', uploadError);
        return res.status(500).json({ error: 'Erro ao fazer upload da imagem' });
      }
    } else {
      console.log('Nenhum arquivo enviado');
    }

    // Criar objetivo
    const goal = await prisma.goal.create({
      data: {
        name,
        description: description || null,
        targetAmount: parseFloat(targetAmount),
        targetYear: parseInt(targetYear),
        targetMonth: parseInt(targetMonth),
        imageUrl,
        userId,
        categories: {
          create: parsedCategoryIds.map(categoryId => ({
            categoryId
          }))
        },
        tags: {
          create: parsedTagIds.map(tagId => ({
            tagId
          }))
        }
      },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      }
    });

    res.status(201).json(goal);
  } catch (error) {
    console.error('Erro ao criar objetivo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar objetivo
router.put('/:id', upload.single('image'), async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, targetAmount, targetYear, targetMonth, categoryIds, tagIds } = req.body;
    const userId = req.user.id;

    // Verificar se o objetivo pertence ao usuário
    const existingGoal = await prisma.goal.findFirst({
      where: { id, userId }
    });

    if (!existingGoal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    // Parse dos IDs
    let parsedCategoryIds = [];
    let parsedTagIds = [];

    if (categoryIds) {
      try {
        parsedCategoryIds = Array.isArray(categoryIds) ? categoryIds : JSON.parse(categoryIds);
      } catch (e) {
        return res.status(400).json({ error: 'IDs de categorias inválidos' });
      }
    }

    if (tagIds) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds);
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' });
      }
    }

    // Processar upload de imagem se houver
    let imageUrl = existingGoal.imageUrl; // Manter a existente por padrão
    if (req.file) {
      console.log('Novo arquivo recebido para atualização:', req.file.originalname, req.file.mimetype);
      console.log('Buffer size:', req.file.buffer ? req.file.buffer.length : 'undefined');
      try {
        const result = await new Promise((resolve, reject) => {
          const uploadStream = cloudinary.uploader.upload_stream(
            {
              resource_type: 'image',
              folder: 'goals',
              transformation: [
                { width: 800, height: 600, crop: 'fill' }
              ]
            },
            (error, result) => {
              if (error) {
                console.error('Erro no Cloudinary na atualização:', error);
                reject(error);
              } else {
                console.log('Resultado do Cloudinary na atualização:', result);
                resolve(result);
              }
            }
          );

          uploadStream.end(req.file.buffer);
        });

        imageUrl = result.secure_url;
        console.log('Upload de atualização realizado com sucesso:', imageUrl);
      } catch (uploadError) {
        console.error('Erro no upload da imagem na atualização:', uploadError);
        return res.status(500).json({ error: 'Erro ao fazer upload da imagem' });
      }
    }

    // Atualizar objetivo
    const goal = await prisma.goal.update({
      where: { id },
      data: {
        name: name || existingGoal.name,
        description: description !== undefined ? description : existingGoal.description,
        targetAmount: targetAmount ? parseFloat(targetAmount) : existingGoal.targetAmount,
        targetYear: targetYear ? parseInt(targetYear) : existingGoal.targetYear,
        targetMonth: targetMonth ? parseInt(targetMonth) : existingGoal.targetMonth,
        imageUrl,
        categories: {
          deleteMany: {},
          create: parsedCategoryIds.map(categoryId => ({
            categoryId
          }))
        },
        tags: {
          deleteMany: {},
          create: parsedTagIds.map(tagId => ({
            tagId
          }))
        }
      },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      }
    });

    res.json(goal);
  } catch (error) {
    console.error('Erro ao atualizar objetivo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar objetivo
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se o objetivo pertence ao usuário
    const existingGoal = await prisma.goal.findFirst({
      where: { id, userId }
    });

    if (!existingGoal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    await prisma.goal.delete({
      where: { id }
    });

    res.json({ message: 'Objetivo deletado com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar objetivo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar detalhes de um objetivo específico
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    const goal = await prisma.goal.findFirst({
      where: { id, userId },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      }
    });

    if (!goal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    res.json(goal);
  } catch (error) {
    console.error('Erro ao buscar objetivo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Análise de projeção para atingir objetivo
router.get('/:id/projection', async (req, res) => {
  try {
    const { id } = req.params;
    const { year, month } = req.query;
    const userId = req.user.id;

    // Buscar objetivo
    const goal = await prisma.goal.findFirst({
      where: { id, userId },
      include: {
        categories: { include: { category: true } },
        tags: { include: { tag: true } }
      }
    });

    console.log(goal);

    if (!goal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    // Usar data de criação do objetivo como base (não alterar)
    const currentDate = new Date(goal.createdAt);
    const analysisYear = currentDate.getFullYear();
    const analysisMonth = currentDate.getMonth() + 1;

    // Gerar lista de meses do ano de análise (de janeiro até o mês de análise)
    const months = [];
    for (let monthNum = 1; monthNum <= analysisMonth; monthNum++) {
      months.push({
        year: analysisYear,
        month: monthNum
      });
    }

    // Buscar configuração do mês de análise como fallback
    const currentMonthConfig = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: { userId, year: analysisYear, month: analysisMonth }
      }
    });

    // Buscar dados históricos do ano atual
    const historicalData = [];

    for (const { year, month } of months) {
      // Buscar configuração do relatório
      let config = await prisma.reportConfig.findUnique({
        where: {
          userId_year_month: { userId, year, month }
        }
      });

      // Se não há configuração para este mês, usar a configuração do mês atual como fallback
      if (!config || !config.isCompleted) {
        if (currentMonthConfig && currentMonthConfig.isCompleted) {
          config = currentMonthConfig;
        } else {
          continue; // Pular meses sem configuração
        }
      }

      // Buscar transações do mês
      const startOfMonth = new Date(year, month-1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59);

      const transactions = await prisma.transaction.findMany({
        where: {
          userId: userId,
          date: { gte: startOfMonth, lte: endOfMonth },
          OR: [
      { installmentStatus: { in: ['RESERVED', 'BILLED', 'CURRENT_BILL'] } },
      { installmentStatus: null }
    ]
        },
        include: { category: true, tags: true }
      });

      // Calcular totais
      const income = transactions
        .filter(t => t.type === 'INCOME')
        .reduce((sum, t) => sum + t.amount, 0);

      const expenses = transactions
        .filter(t => t.type === 'EXPENSE')
        .reduce((sum, t) => sum + t.amount, 0);

      const investments = transactions
        .filter(t => t.type === 'INVESTMENT')
        .reduce((sum, t) => sum + t.amount, 0);
      
      console.log('Mês: ', month);
      console.log('Data de inicio de pesquisa: ', startOfMonth.toDateString());
      console.log('Data de fim de pesquisa: ', endOfMonth.toDateString());
      console.log('Transações: ', transactions.length);
      console.log('Receitas: ', income);
      console.log('Despesas: ', expenses);
      console.log('Investimentos: ', investments);
      console.log('--------------------------------------\n');

      // Calcular lazer baseado na configuração do relatório
      // Se não há configuração, considerar 0
      let leisure = 0;
      if (config) {
        // Calcular o que deveria ser lazer baseado na porcentagem configurada
        leisure = (income * config.leisurePercentage) / 100;
      }

      // Adicionar dados históricos
      historicalData.push({
        year,
        month,
        income,
        expenses,
        investments,
        leisure,
        expensePercentage: income > 0 ? (expenses / income) * 100 : 0,
        investmentPercentage: income > 0 ? (investments / income) * 100 : 0,
        leisurePercentage: income > 0 ? (leisure / income) * 100 : 0,
        config
      });
    }

    if (historicalData.length === 0) {
      return res.status(400).json({
        error: 'Não há dados históricos suficientes. Configure pelo menos um relatório mensal.'
      });
    }

    // Calcular médias históricas
    const avgIncome = historicalData.reduce((sum, data) => sum + data.income, 0) / historicalData.length;
    const avgExpensePercentage = historicalData.reduce((sum, data) => sum + data.expensePercentage, 0) / historicalData.length;
    const avgInvestmentPercentage = historicalData.reduce((sum, data) => sum + data.investmentPercentage, 0) / historicalData.length;

    // Usar a configuração mais recente ou do mês atual para limites futuros
    const latestConfig = historicalData.length > 0 ? historicalData[0].config : currentMonthConfig;

    if (!latestConfig) {
      return res.status(400).json({
        error: 'Não há configuração de relatório disponível. Configure pelo menos o mês atual.'
      });
    }

    // Calcular capacidade de poupança baseada nos limites
    const projectedIncome = avgIncome;
    const projectedExpenses = (projectedIncome * latestConfig.expensePercentage) / 100;
    const projectedInvestments = (projectedIncome * latestConfig.savingsPercentage) / 100;
    const projectedLeisure = (projectedIncome * latestConfig.leisurePercentage) / 100;

    // Capacidade mensal de poupança para o objetivo
    const monthlySavingsCapacity = projectedInvestments;

    // Calcular meses necessários baseado na capacidade de poupança
    const monthsToGoalOptimal = monthlySavingsCapacity > 0 ? Math.ceil(goal.targetAmount / monthlySavingsCapacity) : Infinity;

    // Calcular meses até a data escolhida pelo usuário
    const targetDate = new Date(goal.targetYear, goal.targetMonth - 1, 1);
    const currentDateCalc = new Date();
    const monthsToTargetDate = Math.max(1, Math.ceil((targetDate - currentDateCalc) / (1000 * 60 * 60 * 24 * 30.44)));

    // Calcular valor mensal necessário para atingir a meta na data escolhida
    const monthlyAmountNeeded = monthsToTargetDate > 0 ? goal.targetAmount / monthsToTargetDate : goal.targetAmount;

    // Calcular valores para o mês atual
    const currentMonthDate = new Date();
    const currentMonthNumber = currentMonthDate.getMonth() + 1;
    const currentYearNumber = currentMonthDate.getFullYear();

    // Valor recomendado para o mês atual (baseado na capacidade)
    const recommendedCurrentMonth = monthlySavingsCapacity;

    // Valor escolhido para o mês atual (baseado na meta)
    const chosenCurrentMonth = monthlyAmountNeeded;

    // Data provável de conquista baseada na capacidade
    const optimalTargetDate = new Date();
    optimalTargetDate.setMonth(optimalTargetDate.getMonth() + monthsToGoalOptimal);

    // Projeção mês a mês com duas linhas: capacidade atual e meta escolhida
    const monthlyProjection = [];
    let accumulatedAmountOptimal = 0;
    let accumulatedAmountTarget = 0;

    const maxMonths = Math.max(monthsToGoalOptimal === Infinity ? 24 : monthsToGoalOptimal, monthsToTargetDate, 24);
    inserirGrafico = false;
    for (let i = 1; i <= Math.min(maxMonths, 24); i++) { // Máximo 24 meses de projeção
      // Acúmulo baseado na capacidade atual
      accumulatedAmountOptimal += monthlySavingsCapacity;

      // Acúmulo baseado na meta escolhida
      accumulatedAmountTarget += monthlyAmountNeeded;

      const projectionDate = new Date();
      projectionDate.setMonth(projectionDate.getMonth() + i);
      

      if(!inserirGrafico){
        monthlyProjection.push({
          month: i,
          date: projectionDate,
          // Projeção baseada na capacidade atual
          optimalMonthlyAmount: monthlySavingsCapacity,
          optimalAccumulatedAmount: Math.min(accumulatedAmountOptimal, goal.targetAmount),
          optimalPercentage: Math.min((accumulatedAmountOptimal / goal.targetAmount) * 100, 100),
          // Projeção baseada na meta escolhida
          targetMonthlyAmount: monthlyAmountNeeded,
          targetAccumulatedAmount: Math.min(accumulatedAmountTarget, goal.targetAmount),
          targetPercentage: Math.min((accumulatedAmountTarget / goal.targetAmount) * 100, 100),
          // Indicador se é o mês da meta escolhida
          isTargetMonth: i === monthsToTargetDate
        });
        targetPercentage= Math.min((accumulatedAmountTarget / goal.targetAmount) * 100, 100);
        optimalPercentage= Math.min((accumulatedAmountOptimal / goal.targetAmount) * 100, 100);
        inserirGrafico = targetPercentage == optimalPercentage;
      }
    }

    // Análise de aderência aos limites
    const adherenceAnalysis = historicalData.map(data => ({
      year: data.year,
      month: data.month,
      expenseAdherence: {
        actual: data.expensePercentage,
        target: data.config.expensePercentage,
        withinLimit: data.expensePercentage <= data.config.expensePercentage,
        difference: data.expensePercentage - data.config.expensePercentage
      },
      investmentAdherence: {
        actual: data.investmentPercentage,
        target: data.config.savingsPercentage,
        withinLimit: data.investmentPercentage >= data.config.savingsPercentage,
        difference: data.investmentPercentage - data.config.savingsPercentage
      },
      leisureAdherence: {
        actual: data.leisurePercentage,
        target: data.config.leisurePercentage,
        withinLimit: data.leisurePercentage <= data.config.leisurePercentage,
        difference: data.leisurePercentage - data.config.leisurePercentage
      }
    }));

    res.json({
      goal,
      historicalData,
      projection: {
        avgIncome,
        projectedIncome,
        projectedExpenses,
        projectedInvestments,
        projectedLeisure,
        monthlySavingsCapacity,
        // Projeção baseada na capacidade atual
        monthsToGoalOptimal: monthsToGoalOptimal === Infinity ? null : monthsToGoalOptimal,
        optimalTargetDate: monthsToGoalOptimal === Infinity ? null : optimalTargetDate,
        canAchieveGoalOptimal: monthsToGoalOptimal !== Infinity,
        // Projeção baseada na meta escolhida
        monthsToTargetDate,
        targetDate,
        monthlyAmountNeeded,
        canAchieveTargetDate: monthlyAmountNeeded <= monthlySavingsCapacity * 1.2, // 20% de margem
        // Valores para o mês atual
        recommendedCurrentMonth: monthlySavingsCapacity,
        chosenCurrentMonth: monthlyAmountNeeded,
        // Dados combinados
        monthlyProjection,
        analysisYear: analysisYear
      },
      adherenceAnalysis,
      summary: {
        totalHistoricalMonths: historicalData.length,
        avgExpenseAdherence: adherenceAnalysis.length > 0 ? adherenceAnalysis.reduce((sum, a) => sum + (a.expenseAdherence.withinLimit ? 1 : 0), 0) / adherenceAnalysis.length : 0,
        avgInvestmentAdherence: adherenceAnalysis.length > 0 ? adherenceAnalysis.reduce((sum, a) => sum + (a.investmentAdherence.withinLimit ? 1 : 0), 0) / adherenceAnalysis.length : 0,
        avgLeisureAdherence: adherenceAnalysis.length > 0 ? adherenceAnalysis.reduce((sum, a) => sum + (a.leisureAdherence.withinLimit ? 1 : 0), 0) / adherenceAnalysis.length : 0
      }
    });

  } catch (error) {
    console.error('Erro ao calcular projeção do objetivo:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Marcar objetivo como concluído
router.patch('/:id/complete', async (req, res) => {
  try {
    const { id } = req.params;
    const { completedAmount } = req.body;
    const userId = req.user.id;

    // Verificar se o objetivo pertence ao usuário
    const existingGoal = await prisma.goal.findFirst({
      where: { id, userId }
    });

    if (!existingGoal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    if (existingGoal.isCompleted) {
      return res.status(400).json({ error: 'Objetivo já foi marcado como concluído' });
    }

    // Marcar como concluído
    const goal = await prisma.goal.update({
      where: { id },
      data: {
        isCompleted: true,
        completedAt: new Date(),
        completedAmount: completedAmount || existingGoal.targetAmount
      },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      }
    });

    res.json(goal);
  } catch (error) {
    console.error('Erro ao marcar objetivo como concluído:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Desmarcar objetivo como concluído
router.patch('/:id/uncomplete', async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Verificar se o objetivo pertence ao usuário
    const existingGoal = await prisma.goal.findFirst({
      where: { id, userId }
    });

    if (!existingGoal) {
      return res.status(404).json({ error: 'Objetivo não encontrado' });
    }

    if (!existingGoal.isCompleted) {
      return res.status(400).json({ error: 'Objetivo não está marcado como concluído' });
    }

    // Desmarcar como concluído
    const goal = await prisma.goal.update({
      where: { id },
      data: {
        isCompleted: false,
        completedAt: null,
        completedAmount: null
      },
      include: {
        categories: {
          include: { category: true }
        },
        tags: {
          include: { tag: true }
        }
      }
    });

    res.json(goal);
  } catch (error) {
    console.error('Erro ao desmarcar objetivo como concluído:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
