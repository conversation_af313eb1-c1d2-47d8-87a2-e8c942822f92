{"name": "sara-backend", "version": "1.0.0", "description": "Backend API para sistema de gastos", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:seed": "node src/seed.js"}, "dependencies": {"@prisma/client": "^5.7.1", "bcryptjs": "^2.4.3", "cloudinary": "^2.6.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "multer": "^2.0.0", "node-cron": "^4.1.0", "prisma": "^5.7.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "sinon": "^17.0.1"}, "devDependencies": {"nodemon": "^3.0.2"}}