# 🐳 SARA - Docker Compose Simplificado
version: '3.8'

services:
  # Aplicação Principal
  sara:
    build: .
    container_name: sara
    restart: unless-stopped
    ports:
      - "80:80"
      - "3001:3001"
    environment:
      # Obrigatórias
      - JWT_SECRET=${JWT_SECRET}
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}

      # Auto-Deploy
      - GIT_REPO=${GIT_REPO}
      - GIT_BRANCH=${GIT_BRANCH:-main}
      - GIT_TOKEN=${GIT_TOKEN}
      - AUTO_DEPLOY=${AUTO_DEPLOY:-true}

      # Sistema
      - NODE_ENV=production
      - PORT=3001
      - DATABASE_URL=file:/app/backend/production.db
    volumes:
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Auto-Update (Watchtower)
  watchtower:
    image: containrrr/watchtower
    container_name: sara-watchtower
    restart: unless-stopped
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=300
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - sara
