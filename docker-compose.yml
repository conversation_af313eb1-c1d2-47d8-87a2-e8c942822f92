# 🐳 SARA - Docker Compose para Produção
version: '3.8'

services:
  # ================================
  # Aplicação Principal SARA
  # ================================
  sara-app:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - GIT_REPO=${GIT_REPO:-https://github.com/seu-usuario/sara.git}
        - GIT_BRANCH=${GIT_BRANCH:-main}
    container_name: sara-app
    restart: unless-stopped
    ports:
      - "80:80"
      - "3001:3001"
    environment:
      # Banco de dados
      - DATABASE_URL=file:/app/backend/production.db
      
      # JWT
      - JWT_SECRET=${JWT_SECRET:-seu_jwt_secret_super_seguro_aqui_mude_em_producao}
      
      # Cloudinary
      - CLOUDINARY_CLOUD_NAME=${CLOUDINARY_CLOUD_NAME}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      
      # Servidor
      - NODE_ENV=production
      - PORT=3001
      - FRONTEND_URL=${FRONTEND_URL:-http://localhost}
      
      # Git Auto-Deploy
      - GIT_REPO=${GIT_REPO:-https://github.com/seu-usuario/sara.git}
      - GIT_BRANCH=${GIT_BRANCH:-main}
      - AUTO_DEPLOY=${AUTO_DEPLOY:-true}
      - DEPLOY_INTERVAL=${DEPLOY_INTERVAL:-300}  # 5 minutos
      
      # Webhook (opcional)
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
    volumes:
      # Persistir banco de dados
      - sara-database:/app/backend
      
      # Logs
      - sara-logs:/app/logs
      
      # Cache do Git
      - sara-git-cache:/tmp/git-cache
      
      # Socket do Docker (para restart)
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - sara-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.sara.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.sara.entrypoints=websecure"
      - "traefik.http.routers.sara.tls.certresolver=letsencrypt"

  # ================================
  # Watchtower - Auto Update
  # ================================
  watchtower:
    image: containrrr/watchtower:latest
    container_name: sara-watchtower
    restart: unless-stopped
    environment:
      - WATCHTOWER_CLEANUP=true
      - WATCHTOWER_POLL_INTERVAL=300  # 5 minutos
      - WATCHTOWER_INCLUDE_STOPPED=true
      - WATCHTOWER_REVIVE_STOPPED=true
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - sara-network
    depends_on:
      - sara-app

  # ================================
  # Traefik - Reverse Proxy (Opcional)
  # ================================
  traefik:
    image: traefik:v2.10
    container_name: sara-traefik
    restart: unless-stopped
    ports:
      - "443:443"
      - "8080:8080"  # Dashboard
    environment:
      - TRAEFIK_API_DASHBOARD=true
      - TRAEFIK_API_INSECURE=true
      - TRAEFIK_ENTRYPOINTS_WEBSECURE_ADDRESS=:443
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_EMAIL=${ACME_EMAIL}
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_STORAGE=/letsencrypt/acme.json
      - TRAEFIK_CERTIFICATESRESOLVERS_LETSENCRYPT_ACME_HTTPCHALLENGE_ENTRYPOINT=web
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - traefik-letsencrypt:/letsencrypt
    networks:
      - sara-network
    profiles:
      - traefik  # Ativar com: docker-compose --profile traefik up

  # ================================
  # Backup Service
  # ================================
  backup:
    image: alpine:latest
    container_name: sara-backup
    restart: unless-stopped
    environment:
      - BACKUP_INTERVAL=${BACKUP_INTERVAL:-86400}  # 24 horas
      - BACKUP_RETENTION=${BACKUP_RETENTION:-7}    # 7 dias
    volumes:
      - sara-database:/data/database:ro
      - sara-backups:/backups
    networks:
      - sara-network
    command: |
      sh -c '
        while true; do
          echo "🔄 Iniciando backup automático..."
          timestamp=$$(date +%Y%m%d_%H%M%S)
          cp /data/database/production.db /backups/sara_backup_$$timestamp.db
          
          # Manter apenas os últimos N backups
          ls -t /backups/sara_backup_*.db | tail -n +$$((${BACKUP_RETENTION} + 1)) | xargs -r rm
          
          echo "✅ Backup concluído: sara_backup_$$timestamp.db"
          sleep ${BACKUP_INTERVAL}
        done
      '
    profiles:
      - backup  # Ativar com: docker-compose --profile backup up

# ================================
# Volumes Persistentes
# ================================
volumes:
  sara-database:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/database
  
  sara-logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/logs
  
  sara-git-cache:
    driver: local
  
  sara-backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ${DATA_PATH:-./data}/backups
  
  traefik-letsencrypt:
    driver: local

# ================================
# Rede
# ================================
networks:
  sara-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
