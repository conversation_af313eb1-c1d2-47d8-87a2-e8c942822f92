import React from 'react'
import { 
  Lightbulb, 
  PiggyBank, 
  TrendingDown, 
  AlertCircle, 
  Target,
  Coffee,
  Car,
  ShoppingCart,
  Home,
  Smartphone
} from 'lucide-react'

const EconomyTips = ({ analysis }) => {
  // Gerar dicas baseadas na análise
  const generateTips = (analysis) => {
    const tips = []
    
    if (!analysis || !analysis.current || !analysis.goal) {
      return [{
        icon: <Lightbulb className="h-5 w-5" />,
        title: 'Configure suas metas',
        description: 'Defina suas metas financeiras para receber dicas personalizadas.',
        type: 'info',
        priority: 'high'
      }]
    }

    const { current, goal, scores } = analysis

    // Dicas para despesas altas
    if (current.expensePercentage > goal.expensePercentage + 10) {
      tips.push({
        icon: <TrendingDown className="h-5 w-5" />,
        title: 'Reduza suas despesas essenciais',
        description: `Você está gastando ${current.expensePercentage.toFixed(1)}% da renda com despesas essenciais. Meta: ${goal.expensePercentage}%. Revise contas fixas e negocie melhores preços.`,
        type: 'warning',
        priority: 'high',
        action: 'Revisar gastos fixos'
      })
    }

    // Dicas para poupança baixa
    if (current.savingsPercentage < goal.savingsPercentage - 5) {
      tips.push({
        icon: <PiggyBank className="h-5 w-5" />,
        title: 'Aumente sua poupança',
        description: `Você está poupando apenas ${current.savingsPercentage.toFixed(1)}% da renda. Meta: ${goal.savingsPercentage}%. Configure transferências automáticas.`,
        type: 'info',
        priority: 'high',
        action: 'Automatizar poupança'
      })
    }

    // Dicas para lazer excessivo
    if (current.leisurePercentage > goal.leisurePercentage + 10) {
      tips.push({
        icon: <Coffee className="h-5 w-5" />,
        title: 'Controle gastos com lazer',
        description: `Gastos com lazer estão em ${current.leisurePercentage.toFixed(1)}% da renda. Meta: ${goal.leisurePercentage}%. Considere atividades mais econômicas.`,
        type: 'warning',
        priority: 'medium',
        action: 'Planejar lazer'
      })
    }

    // Dicas gerais baseadas no score geral
    if (scores.overall < 60) {
      tips.push({
        icon: <Target className="h-5 w-5" />,
        title: 'Foque no planejamento',
        description: 'Seu score geral está baixo. Considere revisar todas as categorias de gastos e criar um orçamento mais detalhado.',
        type: 'info',
        priority: 'high',
        action: 'Criar orçamento detalhado'
      })
    }

    // Dicas de economia específicas
    const economyTips = [
      {
        icon: <ShoppingCart className="h-5 w-5" />,
        title: 'Lista de compras inteligente',
        description: 'Faça uma lista antes de ir ao mercado e evite compras por impulso. Isso pode reduzir gastos em até 20%.',
        type: 'success',
        priority: 'low',
        action: 'Fazer lista'
      },
      {
        icon: <Car className="h-5 w-5" />,
        title: 'Transporte econômico',
        description: 'Considere usar transporte público ou compartilhado. Pode economizar até R$ 300/mês em combustível.',
        type: 'success',
        priority: 'low',
        action: 'Avaliar transporte'
      },
      {
        icon: <Home className="h-5 w-5" />,
        title: 'Energia em casa',
        description: 'Desligue aparelhos da tomada e use lâmpadas LED. Economia de até 15% na conta de luz.',
        type: 'success',
        priority: 'low',
        action: 'Revisar consumo'
      },
      {
        icon: <Smartphone className="h-5 w-5" />,
        title: 'Planos de celular',
        description: 'Revise seu plano de celular. Muitas vezes você paga por serviços que não usa.',
        type: 'success',
        priority: 'low',
        action: 'Revisar plano'
      }
    ]

    // Adicionar algumas dicas gerais se não há muitas específicas
    if (tips.length < 3) {
      tips.push(...economyTips.slice(0, 4 - tips.length))
    }

    return tips.sort((a, b) => {
      const priorityOrder = { high: 3, medium: 2, low: 1 }
      return priorityOrder[b.priority] - priorityOrder[a.priority]
    })
  }

  const tips = generateTips(analysis)

  const getTypeStyles = (type) => {
    switch (type) {
      case 'warning':
        return {
          bg: 'bg-yellow-50',
          border: 'border-yellow-200',
          icon: 'text-yellow-600',
          title: 'text-yellow-800',
          text: 'text-yellow-700'
        }
      case 'success':
        return {
          bg: 'bg-green-50',
          border: 'border-green-200',
          icon: 'text-green-600',
          title: 'text-green-800',
          text: 'text-green-700'
        }
      case 'info':
      default:
        return {
          bg: 'bg-blue-50',
          border: 'border-blue-200',
          icon: 'text-blue-600',
          title: 'text-blue-800',
          text: 'text-blue-700'
        }
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 mb-4">
        <Lightbulb className="h-5 w-5 text-yellow-500" />
        <h3 className="text-lg font-semibold text-gray-800">Dicas para Economizar</h3>
      </div>

      <div className="grid gap-4">
        {tips.map((tip, index) => {
          const styles = getTypeStyles(tip.type)
          
          return (
            <div 
              key={index}
              className={`p-4 rounded-lg border ${styles.bg} ${styles.border} transition-all hover:shadow-md`}
            >
              <div className="flex items-start gap-3">
                <div className={`${styles.icon} mt-0.5`}>
                  {tip.icon}
                </div>
                
                <div className="flex-1">
                  <h4 className={`font-semibold ${styles.title} mb-1`}>
                    {tip.title}
                  </h4>
                  
                  <p className={`text-sm ${styles.text} mb-2`}>
                    {tip.description}
                  </p>
                  
                  {tip.action && (
                    <button className={`text-xs px-3 py-1 rounded-full border ${styles.border} ${styles.text} hover:bg-white transition-colors`}>
                      {tip.action}
                    </button>
                  )}
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}

export default EconomyTips
