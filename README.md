# 💰 SARA - Sistema de Acompanhamento de Recursos e Aplicações

![SARA Logo](https://via.placeholder.com/800x200/4F46E5/FFFFFF?text=SARA+-+Sistema+Financeiro+Completo)

## 📋 Sobre o Projeto

SARA é um sistema completo de gestão financeira pessoal desenvolvido com tecnologias modernas. O sistema oferece controle total sobre suas finanças, incluindo transações, empréstimos, cartões de crédito, investimentos, relatórios detalhados e muito mais.

### 🎯 Principais Funcionalidades

- **💳 Gestão de Transações**: Controle completo de receitas, despesas e investimentos
- **🏦 Gestão de Bancos**: Múltiplas contas bancárias com saldos em tempo real
- **💰 Sistema de Empréstimos**: Controle de empréstimos dados e recebidos com parcelas
- **📊 Relatórios Avançados**: Análises detalhadas com gráficos e métricas
- **🎯 Objetivos Financeiros**: Metas simples e complexas com projeções
- **💳 Cartões de Crédito**: Faturamento automático e controle de limites
- **📱 Interface Responsiva**: Design moderno e intuitivo
- **☁️ Upload de Imagens**: Integração com Cloudinary
- **🔒 Autenticação Segura**: Sistema de login com JWT

### 🛠️ Tecnologias Utilizadas

#### Frontend
- **React 18** - Biblioteca principal
- **Vite** - Build tool e dev server
- **Tailwind CSS** - Framework de estilos
- **Lucide React** - Ícones modernos
- **React Router** - Roteamento
- **Axios** - Cliente HTTP
- **React Hot Toast** - Notificações

#### Backend
- **Node.js** - Runtime JavaScript
- **Express.js** - Framework web
- **Prisma** - ORM moderno
- **SQLite** - Banco de dados
- **JWT** - Autenticação
- **Multer** - Upload de arquivos
- **Cloudinary** - Armazenamento de imagens
- **Bcrypt** - Criptografia de senhas

## 🚀 Instalação e Execução Local

### Pré-requisitos

- Node.js 18+ instalado
- Git instalado
- Conta no Cloudinary (para upload de imagens)

### 📦 Instalação Rápida

1. **Clone o repositório:**
```bash
git clone https://github.com/seu-usuario/sara.git
cd sara
```

2. **Execute a instalação automática:**
```bash
# No Windows
install.bat

# No Linux/Mac
chmod +x install.sh
./install.sh
```

3. **Configure as variáveis de ambiente:**
```bash
# Copie o arquivo de exemplo
cp backend/.env.example backend/.env

# Edite o arquivo .env com suas configurações
```

4. **Atualize o banco de dados:**
```bash
# No Windows
update-database.bat

# No Linux/Mac
./update-database.sh
```

5. **Inicie o sistema:**
```bash
# No Windows
start.bat

# No Linux/Mac
./start.sh
```

### 🔧 Instalação Manual

#### Backend

1. **Navegue para o diretório do backend:**
```bash
cd backend
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Configure o arquivo .env:**
```env
# Banco de dados
DATABASE_URL="file:./dev.db"

# JWT
JWT_SECRET="seu_jwt_secret_super_seguro_aqui"

# Cloudinary
CLOUDINARY_CLOUD_NAME="seu_cloud_name"
CLOUDINARY_API_KEY="sua_api_key"
CLOUDINARY_API_SECRET="seu_api_secret"

# Servidor
PORT=3001
NODE_ENV=development
```

4. **Execute as migrações do banco:**
```bash
npx prisma generate
npx prisma db push
```

5. **Inicie o servidor:**
```bash
npm run dev
```

#### Frontend

1. **Em um novo terminal, navegue para o frontend:**
```bash
cd frontend
```

2. **Instale as dependências:**
```bash
npm install
```

3. **Configure o arquivo .env:**
```env
VITE_API_URL=http://localhost:3001/api
```

4. **Inicie o servidor de desenvolvimento:**
```bash
npm run dev
```

### 🌐 Acesso ao Sistema

- **Frontend**: http://localhost:5173
- **Backend**: http://localhost:3001
- **Usuário padrão**: <EMAIL>
- **Senha padrão**: 123456

## 📁 Estrutura do Projeto

```
sara/
├── backend/                 # Servidor Node.js
│   ├── prisma/             # Schema e migrações do banco
│   ├── src/                # Código fonte
│   │   ├── middleware/     # Middlewares (auth, upload)
│   │   ├── routes/         # Rotas da API
│   │   └── server.js       # Servidor principal
│   ├── .env.example        # Exemplo de variáveis de ambiente
│   └── package.json        # Dependências do backend
├── frontend/               # Aplicação React
│   ├── src/                # Código fonte
│   │   ├── components/     # Componentes React
│   │   ├── services/       # Serviços de API
│   │   ├── pages/          # Páginas principais
│   │   └── App.jsx         # Componente principal
│   ├── .env.example        # Exemplo de variáveis de ambiente
│   └── package.json        # Dependências do frontend
├── install.bat             # Script de instalação (Windows)
├── start.bat               # Script de inicialização (Windows)
├── update-database.bat     # Script de atualização do BD (Windows)
└── README.md               # Este arquivo
```

## 🔄 Scripts Disponíveis

### Windows (.bat)

- **install.bat**: Instala todas as dependências
- **start.bat**: Inicia frontend e backend simultaneamente
- **update-database.bat**: Atualiza schema do banco de dados

### Linux/Mac (.sh)

- **install.sh**: Instala todas as dependências
- **start.sh**: Inicia frontend e backend simultaneamente
- **update-database.sh**: Atualiza schema do banco de dados

## 🐛 Solução de Problemas

### Erro de Porta em Uso
```bash
# Mate processos nas portas 3001 e 5173
npx kill-port 3001 5173
```

### Erro de Banco de Dados
```bash
# Execute o script de atualização
update-database.bat
```

### Erro de Dependências
```bash
# Reinstale as dependências
rm -rf node_modules package-lock.json
npm install
```

## 🚀 Deploy em Produção

### Quick Start (5 minutos)
```bash
# Clonar repositório
git clone https://github.com/seu-usuario/sara.git
cd sara

# Configurar e fazer deploy
chmod +x deploy.sh
./deploy.sh
```

📖 **Guias Completos:**
- [⚡ Quick Start Guide](QUICK_START.md) - Deploy em 5 minutos
- [🚀 Deploy Guide Completo](DEPLOY.md) - Guia detalhado de produção
- [🐳 Docker Configuration](docker/) - Arquivos de configuração

## 📖 Documentação Adicional

- [🎯 Fluxo de Telas e Funcionalidades](SCREENS_FLOW.md) - Diagrama completo do sistema
- [🚀 Deploy Guide](DEPLOY.md) - Guia completo de deploy
- [⚡ Quick Start](QUICK_START.md) - Deploy rápido em 5 minutos

## 🤝 Contribuição

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.

## 👨‍💻 Autor

Desenvolvido com ❤️ por [Seu Nome]

---

⭐ Se este projeto te ajudou, considere dar uma estrela no repositório!
