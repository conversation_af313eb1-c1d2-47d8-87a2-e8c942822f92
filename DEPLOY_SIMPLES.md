# 🚀 SARA - Deploy Super Simples

## ⚡ Deploy em 3 Comandos

### 1. Instalar Docker (se n<PERSON> tiver)
```bash
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Configurar
```bash
# Copiar configuração
cp .env.simple .env

# Editar (OBRIGATÓRIO!)
nano .env
```

**Configure estas variáveis:**
- `JWT_SECRET` - Mude para algo seguro
- `CLOUDINARY_*` - Suas credenciais do Cloudinary
- `GIT_TOKEN` - Token do GitHub para auto-deploy

### 3. Executar
```bash
chmod +x start-docker.sh
./start-docker.sh
```

## 🎯 Pronto!

- **Frontend**: http://localhost
- **Login**: <EMAIL>
- **Senha**: 123456

## 🔧 Comandos Úteis

```bash
# Ver logs
docker-compose logs -f sara

# Reiniciar
docker-compose restart sara

# Parar
docker-compose down

# Status
docker-compose ps
```

## 🔄 Auto-Deploy

O sistema monitora automaticamente seu repositório GitHub e faz deploy das mudanças.

**Para funcionar:**
1. Configure `GIT_TOKEN` no .env
2. Faça push para a branch configurada
3. Aguarde até 5 minutos

## 🔑 GitHub Token

1. Vá em GitHub → Settings → Developer settings → Personal access tokens
2. Gere um token com permissões de `repo`
3. Cole no arquivo `.env` na variável `GIT_TOKEN`

## 🛡️ Segurança

**IMPORTANTE:**
- Altere a senha padrão após primeiro login
- Use um `JWT_SECRET` seguro em produção
- Configure firewall se necessário

## 🚨 Problemas?

```bash
# Ver logs de erro
docker-compose logs sara | grep -i error

# Reiniciar do zero
docker-compose down
docker system prune -f
./start-docker.sh

# Verificar configuração
docker-compose config
```

---

## 🎉 É isso!

Deploy mais simples impossível! 🚀
