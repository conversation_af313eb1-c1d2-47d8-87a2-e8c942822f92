import React, { useState } from 'react'
import { 
  X, 
  Target, 
  TrendingUp, 
  CheckCircle, 
  Edit3, 
  Trash2, 
  DollarSign,
  Calendar,
  MoreVertical,
  Plus
} from 'lucide-react'
import CurrencyInput from './CurrencyInput'
import goalService from '../services/goalService'
import toast from 'react-hot-toast'

const ComplexGoalProjectionModal = ({ goal, isOpen, onClose, onUpdate }) => {
  const [showCompleteModal, setShowCompleteModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [completionValue, setCompletionValue] = useState(0)
  const [showItemProjection, setShowItemProjection] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [editingItem, setEditingItem] = useState(null)
  const [editFormData, setEditFormData] = useState({
    name: '',
    description: '',
    targetAmount: 0,
    targetYear: new Date().getFullYear(),
    targetMonth: new Date().getMonth() + 1
  })

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getMonthName = (month) => {
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ]
    return months[month - 1]
  }

  const handleCompleteItem = (item) => {
    setSelectedItem(item)
    setCompletionValue(item.targetAmount)
    setShowCompleteModal(true)
  }

  const confirmCompleteItem = async () => {
    if (!selectedItem || !completionValue) {
      toast.error('Valor é obrigatório')
      return
    }

    try {
      await goalService.completeComplexGoalItem(goal.id, selectedItem.id, {
        completedAmount: completionValue
      })
      toast.success('Item marcado como concluído!')
      setShowCompleteModal(false)
      setSelectedItem(null)
      setCompletionValue(0)
      onUpdate()
    } catch (error) {
      console.error('Erro ao marcar item como concluído:', error)
      toast.error('Erro ao marcar item como concluído')
    }
  }

  const handleDeleteItem = async (item) => {
    if (!confirm(`Tem certeza que deseja excluir o item "${item.name}"?`)) {
      return
    }

    try {
      await goalService.deleteComplexGoalItem(goal.id, item.id)
      toast.success('Item excluído com sucesso!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao excluir item:', error)
      toast.error('Erro ao excluir item')
    }
  }

  const handleItemProjection = (item) => {
    setSelectedItem(item)
    setShowItemProjection(true)
  }

  const handleEditItem = (item) => {
    setEditingItem(item)
    setEditFormData({
      name: item.name,
      description: item.description || '',
      targetAmount: item.targetAmount,
      targetYear: item.targetYear,
      targetMonth: item.targetMonth
    })
    setShowEditModal(true)
  }

  const confirmEditItem = async () => {
    if (!editingItem || !editFormData.name || !editFormData.targetAmount) {
      toast.error('Nome e valor são obrigatórios')
      return
    }

    try {
      await goalService.updateComplexGoalItem(goal.id, editingItem.id, editFormData)
      toast.success('Item atualizado com sucesso!')
      setShowEditModal(false)
      setEditingItem(null)
      onUpdate()
    } catch (error) {
      console.error('Erro ao atualizar item:', error)
      toast.error('Erro ao atualizar item')
    }
  }

  if (!isOpen) return null

  const completedItems = goal.subGoals?.filter(item => item.isCompleted) || []
  const totalItems = goal.subGoals?.length || 0
  const progressPercentage = totalItems > 0 ? (completedItems.length / totalItems) * 100 : 0
  
  const totalExpected = goal.subGoals?.reduce((sum, item) => sum + item.targetAmount, 0) || 0
  const totalPaid = goal.subGoals?.reduce((sum, item) => sum + (item.completedAmount || 0), 0) || 0
  const difference = totalExpected - totalPaid

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-2xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
          {/* Header */}
          <div className="p-6 border-b border-gray-200 bg-gradient-to-r from-purple-50 to-violet-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-violet-600 rounded-2xl flex items-center justify-center">
                  <Target className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">
                    Projeção: {goal.name}
                  </h3>
                  <p className="text-gray-600 mt-1">
                    Meta de {formatCurrency(goal.targetAmount)} para {getMonthName(goal.targetMonth)} de {goal.targetYear}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-100"
              >
                <X className="h-6 w-6" />
              </button>
            </div>
          </div>

          {/* Conteúdo */}
          <div className="p-6">
            {/* Seção de Itens do Objetivo Complexo */}
            <div className="mb-8">
              <div className="mb-6">
                <h4 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <Target className="h-5 w-5 text-purple-600" />
                  Itens do Objetivo Complexo
                </h4>
                <p className="text-gray-600 text-sm mt-1">
                  Gerencie cada item individualmente. Cada item pode ter sua própria projeção.
                </p>
              </div>
              
              {/* Progresso Geral */}
              <div className="mb-6 p-4 bg-purple-50 rounded-xl border border-purple-200">
                <div className="flex items-center justify-between text-sm mb-3">
                  <span className="text-purple-700 font-medium">Progresso Geral:</span>
                  <span className="text-purple-800 font-bold">
                    {completedItems.length}/{totalItems} itens concluídos
                  </span>
                </div>
                <div className="w-full bg-purple-200 rounded-full h-3">
                  <div 
                    className="bg-purple-600 h-3 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
                <div className="text-center mt-2">
                  <span className="text-purple-700 font-semibold text-lg">
                    {progressPercentage.toFixed(1)}%
                  </span>
                </div>
              </div>

              {/* Lista de Itens */}
              <div className="space-y-4">
                {goal.subGoals?.map((item, index) => (
                  <div key={index} className={`p-4 rounded-xl border transition-all ${
                    item.isCompleted 
                      ? 'bg-green-50 border-green-200' 
                      : 'bg-gray-50 border-gray-200 hover:border-purple-300'
                  }`}>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${
                          item.isCompleted 
                            ? 'bg-green-500 text-white' 
                            : 'bg-gray-300 text-gray-700'
                        }`}>
                          {item.isCompleted ? (
                            <CheckCircle className="h-5 w-5" />
                          ) : (
                            <span>{index + 1}</span>
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <h5 className={`font-semibold text-lg ${
                            item.isCompleted ? 'text-green-800 line-through' : 'text-gray-900'
                          }`}>
                            {item.name}
                          </h5>
                          <div className="flex items-center gap-6 text-sm text-gray-600 mt-1">
                            <span className="flex items-center gap-1">
                              <DollarSign className="h-3 w-3" />
                              Meta: {formatCurrency(item.targetAmount)}
                            </span>
                            {item.isCompleted && item.completedAmount && (
                              <span className="text-green-600 font-medium flex items-center gap-1">
                                <CheckCircle className="h-3 w-3" />
                                Pago: {formatCurrency(item.completedAmount)}
                              </span>
                            )}
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {getMonthName(item.targetMonth)}/{item.targetYear}
                            </span>
                          </div>
                          {item.description && (
                            <p className="text-sm text-gray-500 mt-2">{item.description}</p>
                          )}
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        {/* Botões de ação estilizados */}
                        <div className="flex items-center bg-purple-100 rounded-lg p-1 gap-1">
                          {!item.isCompleted && (
                            <button
                              onClick={() => handleCompleteItem(item)}
                              className="p-2 text-green-600 hover:bg-green-200 rounded-lg transition-colors"
                              title="Concluir"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleItemProjection(item)}
                            className="p-2 text-blue-600 hover:bg-blue-200 rounded-lg transition-colors"
                            title="Ver Projeção"
                          >
                            <TrendingUp className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleEditItem(item)}
                            className="p-2 text-purple-600 hover:bg-purple-200 rounded-lg transition-colors"
                            title="Editar"
                          >
                            <Edit3 className="h-4 w-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteItem(item)}
                            className="p-2 text-red-600 hover:bg-red-200 rounded-lg transition-colors"
                            title="Excluir"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Resumo Financeiro */}
              <div className="mt-8 p-6 bg-gray-50 rounded-xl">
                <h5 className="font-semibold text-gray-900 mb-4 text-lg">Resumo Financeiro</h5>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center p-4 bg-white rounded-lg">
                    <p className="text-gray-600 text-sm mb-1">Valor Total Esperado</p>
                    <p className="font-bold text-2xl text-gray-900">
                      {formatCurrency(totalExpected)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg">
                    <p className="text-gray-600 text-sm mb-1">Valor Total Pago</p>
                    <p className="font-bold text-2xl text-green-600">
                      {formatCurrency(totalPaid)}
                    </p>
                  </div>
                  <div className="text-center p-4 bg-white rounded-lg">
                    <p className="text-gray-600 text-sm mb-1">Diferença</p>
                    <p className={`font-bold text-2xl ${
                      difference >= 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {formatCurrency(Math.abs(difference))}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {difference >= 0 ? 'Dentro do orçamento' : 'Acima do orçamento'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal de Conclusão de Item */}
      {showCompleteModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Concluir Item: {selectedItem.name}
            </h3>
            
            <div className="mb-6">
              <p className="text-sm text-gray-600 mb-4">
                Valor esperado: {formatCurrency(selectedItem.targetAmount)}
              </p>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor realmente gasto *
              </label>
              <CurrencyInput
                value={completionValue}
                onChange={setCompletionValue}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="R$ 0,00"
              />
              <p className="text-xs text-gray-500 mt-2">
                Informe o valor que você realmente gastou para concluir este item
              </p>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowCompleteModal(false)
                  setSelectedItem(null)
                  setCompletionValue(0)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={confirmCompleteItem}
                className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
              >
                Confirmar Conclusão
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Projeção Individual */}
      {showItemProjection && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Projeção: {selectedItem.name}
            </h3>

            <div className="mb-6 space-y-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Informações do Item</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-blue-700">Meta:</span>
                    <span className="font-semibold">{formatCurrency(selectedItem.targetAmount)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-blue-700">Prazo:</span>
                    <span className="font-semibold">{getMonthName(selectedItem.targetMonth)}/{selectedItem.targetYear}</span>
                  </div>
                  {selectedItem.isCompleted && (
                    <div className="flex justify-between">
                      <span className="text-green-700">Valor Pago:</span>
                      <span className="font-semibold text-green-800">{formatCurrency(selectedItem.completedAmount)}</span>
                    </div>
                  )}
                </div>
              </div>

              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-2">Status</h4>
                <div className={`inline-flex items-center gap-2 px-3 py-1 rounded-full text-sm font-medium ${
                  selectedItem.isCompleted
                    ? 'bg-green-100 text-green-800'
                    : 'bg-yellow-100 text-yellow-800'
                }`}>
                  {selectedItem.isCompleted ? (
                    <>
                      <CheckCircle className="h-4 w-4" />
                      Concluído
                    </>
                  ) : (
                    <>
                      <Calendar className="h-4 w-4" />
                      Pendente
                    </>
                  )}
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowItemProjection(false)
                  setSelectedItem(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Fechar
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Edição de Item */}
      {showEditModal && editingItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60 p-4">
          <div className="bg-white rounded-xl max-w-md w-full p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Editar Item
            </h3>

            <div className="space-y-4 mb-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome *
                </label>
                <input
                  type="text"
                  value={editFormData.name}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Nome do item"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={editFormData.description}
                  onChange={(e) => setEditFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="Descrição do item"
                  rows="3"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor Meta *
                </label>
                <CurrencyInput
                  value={editFormData.targetAmount}
                  onChange={(value) => setEditFormData(prev => ({ ...prev, targetAmount: value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>

              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Mês
                  </label>
                  <select
                    value={editFormData.targetMonth}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, targetMonth: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {getMonthName(i + 1)}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ano
                  </label>
                  <input
                    type="number"
                    value={editFormData.targetYear}
                    onChange={(e) => setEditFormData(prev => ({ ...prev, targetYear: parseInt(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    min={new Date().getFullYear()}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingItem(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={confirmEditItem}
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                Salvar Alterações
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ComplexGoalProjectionModal
