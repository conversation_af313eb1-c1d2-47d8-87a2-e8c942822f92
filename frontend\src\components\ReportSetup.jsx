import React, { useState, useEffect } from 'react'
import { 
  Target, 
  ArrowRight, 
  ArrowLeft, 
  Check, 
  X,
  Settings,
  Users,
  DollarSign,
  Percent
} from 'lucide-react'
import Step1Percentages from './ReportSetup/Step1Percentages'
import Step2Mappings from './ReportSetup/Step2Mappings'
import Step3Expectations from './ReportSetup/Step3Expectations'
import api from '../services/api'
import toast from 'react-hot-toast'

const ReportSetup = ({ isOpen, onClose, year, month, onComplete }) => {
  const [currentStep, setCurrentStep] = useState(1)
  const [loading, setLoading] = useState(false)
  const [setupData, setSetupData] = useState(null)
  const [formData, setFormData] = useState({
    // Passo 1
    expensePercentage: 50,
    savingsPercentage: 20,
    leisurePercentage: 30,
    // Passo 2
    mappings: {
      expense: { categories: [], tags: [], transactionContacts: [], loanContacts: [] },
      savings: { categories: [], tags: [], transactionContacts: [], loanContacts: [] },
      leisure: { categories: [], tags: [], transactionContacts: [], loanContacts: [] }
    },
    // Passo 3
    expectedIncome: '',
    expectedExpenses: '',
    expectedSavings: '',
    expectedLeisure: '',
    categoryExpectations: {},
    tagExpectations: {}
  })

  useEffect(() => {
    if (isOpen) {
      loadSetupData()
      checkExistingConfig()
    }
  }, [isOpen, year, month])

  const loadSetupData = async () => {
    try {
      setLoading(true)
      const response = await api.get('/reports/setup-data')
      setSetupData(response.data)
    } catch (error) {
      console.error('Erro ao carregar dados de setup:', error)
      toast.error('Erro ao carregar dados necessários')
    } finally {
      setLoading(false)
    }
  }

  const checkExistingConfig = async () => {
    try {
      const response = await api.get(`/reports/config/${year}/${month}`)
      if (response.data.exists && response.data.config) {
        const config = response.data.config
        
        // Preencher dados existentes
        setFormData(prev => ({
          ...prev,
          expensePercentage: config.expensePercentage,
          savingsPercentage: config.savingsPercentage,
          leisurePercentage: config.leisurePercentage,
          expectedIncome: config.expectedIncome || '',
          expectedExpenses: config.expectedExpenses || '',
          expectedSavings: config.expectedSavings || '',
          expectedLeisure: config.expectedLeisure || '',
          mappings: {
            expense: {
              categories: config.categoryMappings?.filter(m => m.limitType === 'EXPENSE').map(m => m.categoryId) || [],
              tags: config.tagMappings?.filter(m => m.limitType === 'EXPENSE').map(m => m.tagId) || [],
              transactionContacts: config.transactionContactMappings?.filter(m => m.limitType === 'EXPENSE').map(m => m.contactId) || [],
              loanContacts: config.loanContactMappings?.filter(m => m.limitType === 'EXPENSE').map(m => m.contactId) || []
            },
            savings: {
              categories: config.categoryMappings?.filter(m => m.limitType === 'SAVINGS').map(m => m.categoryId) || [],
              tags: config.tagMappings?.filter(m => m.limitType === 'SAVINGS').map(m => m.tagId) || [],
              transactionContacts: config.transactionContactMappings?.filter(m => m.limitType === 'SAVINGS').map(m => m.contactId) || [],
              loanContacts: config.loanContactMappings?.filter(m => m.limitType === 'SAVINGS').map(m => m.contactId) || []
            },
            leisure: {
              categories: config.categoryMappings?.filter(m => m.limitType === 'LEISURE').map(m => m.categoryId) || [],
              tags: config.tagMappings?.filter(m => m.limitType === 'LEISURE').map(m => m.tagId) || [],
              transactionContacts: config.transactionContactMappings?.filter(m => m.limitType === 'LEISURE').map(m => m.contactId) || [],
              loanContacts: config.loanContactMappings?.filter(m => m.limitType === 'LEISURE').map(m => m.contactId) || []
            }
          },
          categoryExpectations: config.categoryExpectations?.reduce((acc, exp) => {
            acc[exp.categoryId] = exp.expectedAmount
            return acc
          }, {}) || {},
          tagExpectations: config.tagExpectations?.reduce((acc, exp) => {
            acc[exp.tagId] = exp.expectedAmount
            return acc
          }, {}) || {}
        }))

        if (config.isCompleted) {
          setCurrentStep(3) // Ir para o último passo se já está completo
        }
      }
    } catch (error) {
      console.error('Erro ao verificar configuração existente:', error)
    }
  }

  const handleStep1Submit = async () => {
    // Validar que soma 100%
    const total = formData.expensePercentage + formData.savingsPercentage + formData.leisurePercentage
    if (Math.abs(total - 100) > 0.01) {
      toast.error('As porcentagens devem somar exatamente 100%')
      return
    }

    try {
      setLoading(true)
      await api.post(`/reports/config/${year}/${month}/step1`, {
        expensePercentage: formData.expensePercentage,
        savingsPercentage: formData.savingsPercentage,
        leisurePercentage: formData.leisurePercentage
      })

      toast.success('Passo 1 concluído!')
      setCurrentStep(2)
    } catch (error) {
      console.error('Erro no passo 1:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar passo 1')
    } finally {
      setLoading(false)
    }
  }

  const handleStep2Submit = async () => {
    try {
      setLoading(true)
      await api.post(`/reports/config/${year}/${month}/step2`, {
        mappings: formData.mappings
      })
      
      toast.success('Passo 2 concluído!')
      setCurrentStep(3)
    } catch (error) {
      console.error('Erro no passo 2:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar passo 2')
    } finally {
      setLoading(false)
    }
  }

  const handleStep3Submit = async () => {
    try {
      setLoading(true)
      await api.post(`/reports/config/${year}/${month}/step3`, {
        expectedIncome: formData.expectedIncome ? parseFloat(formData.expectedIncome) : null,
        expectedExpenses: formData.expectedExpenses ? parseFloat(formData.expectedExpenses) : null,
        expectedSavings: formData.expectedSavings ? parseFloat(formData.expectedSavings) : null,
        expectedLeisure: formData.expectedLeisure ? parseFloat(formData.expectedLeisure) : null,
        categoryExpectations: formData.categoryExpectations,
        tagExpectations: formData.tagExpectations
      })
      
      toast.success('Configuração de relatório concluída!')
      onComplete?.()
      onClose()
    } catch (error) {
      console.error('Erro no passo 3:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar passo 3')
    } finally {
      setLoading(false)
    }
  }

  const steps = [
    {
      number: 1,
      title: 'Limites de Gastos',
      description: 'Defina as porcentagens para cada categoria',
      icon: <Percent className="h-5 w-5" />
    },
    {
      number: 2,
      title: 'Mapeamentos',
      description: 'Selecione categorias, tags e contatos',
      icon: <Users className="h-5 w-5" />
    },
    {
      number: 3,
      title: 'Valores Esperados',
      description: 'Configure valores e expectativas',
      icon: <DollarSign className="h-5 w-5" />
    }
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-6 border-b">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                <Target className="h-5 w-5 text-white" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-gray-800">
                  Configurar Relatório
                </h2>
                <p className="text-sm text-gray-600">
                  {new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })}
                </p>
              </div>
            </div>
            
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          {/* Progress Steps */}
          <div className="flex items-center justify-center mt-6">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center gap-2 px-4 py-2 rounded-lg ${
                  currentStep === step.number 
                    ? 'bg-blue-100 text-blue-700' 
                    : currentStep > step.number 
                      ? 'bg-green-100 text-green-700'
                      : 'bg-gray-100 text-gray-500'
                }`}>
                  {currentStep > step.number ? (
                    <Check className="h-4 w-4" />
                  ) : (
                    step.icon
                  )}
                  <span className="font-medium text-sm">{step.title}</span>
                </div>
                
                {index < steps.length - 1 && (
                  <ArrowRight className="h-4 w-4 text-gray-400 mx-2" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading && !setupData ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              <span className="ml-2 text-gray-600">Carregando...</span>
            </div>
          ) : (
            <>
              {currentStep === 1 && (
                <Step1Percentages
                  formData={formData}
                  setFormData={setFormData}
                />
              )}
              
              {currentStep === 2 && setupData && (
                <Step2Mappings
                  formData={formData}
                  setFormData={setFormData}
                  setupData={setupData}
                />
              )}
              
              {currentStep === 3 && setupData && (
                <Step3Expectations
                  formData={formData}
                  setFormData={setFormData}
                  setupData={setupData}
                />
              )}
            </>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
          <button
            onClick={currentStep === 1 ? onClose : () => setCurrentStep(currentStep - 1)}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 hover:text-gray-800"
            disabled={loading}
          >
            <ArrowLeft className="h-4 w-4" />
            {currentStep === 1 ? 'Cancelar' : 'Voltar'}
          </button>

          <button
            onClick={
              currentStep === 1 ? handleStep1Submit :
              currentStep === 2 ? handleStep2Submit :
              handleStep3Submit
            }
            disabled={loading}
            className="flex items-center gap-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {loading ? (
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
            ) : currentStep === 3 ? (
              <Check className="h-4 w-4" />
            ) : (
              <ArrowRight className="h-4 w-4" />
            )}
            {loading ? 'Salvando...' : currentStep === 3 ? 'Concluir' : 'Próximo'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default ReportSetup
