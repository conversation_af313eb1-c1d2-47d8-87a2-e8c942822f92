import React, { useState } from 'react'
import { TrendingDown, PiggyBank, Coffee, Tag, Users, Folder, Check } from 'lucide-react'

const Step2Mappings = ({ formData, setFormData, setupData }) => {
  const [activeTab, setActiveTab] = useState('expense')

  const limitTypes = [
    {
      key: 'expense',
      title: 'Despesas Essenciais',
      icon: <TrendingDown className="h-5 w-5" />,
      color: 'red',
      percentage: formData.expensePercentage
    },
    {
      key: 'savings',
      title: 'Poupança/Investimentos',
      icon: <PiggyBank className="h-5 w-5" />,
      color: 'green',
      percentage: formData.savingsPercentage
    },
    {
      key: 'leisure',
      title: 'Lazer',
      icon: <Coffee className="h-5 w-5" />,
      color: 'blue',
      percentage: formData.leisurePercentage
    }
  ]

  const handleItemToggle = (limitType, itemType, itemId) => {
    setFormData(prev => ({
      ...prev,
      mappings: {
        ...prev.mappings,
        [limitType]: {
          ...prev.mappings[limitType],
          [itemType]: prev.mappings[limitType][itemType].includes(itemId)
            ? prev.mappings[limitType][itemType].filter(id => id !== itemId)
            : [...prev.mappings[limitType][itemType], itemId]
        }
      }
    }))
  }

  const isItemSelected = (limitType, itemType, itemId) => {
    return formData.mappings[limitType][itemType].includes(itemId)
  }

  const getColorClasses = (color) => {
    const colors = {
      red: {
        bg: 'bg-red-50',
        border: 'border-red-200',
        text: 'text-red-700',
        button: 'bg-red-100 text-red-700 border-red-200',
        buttonActive: 'bg-red-500 text-white border-red-500'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-700',
        button: 'bg-green-100 text-green-700 border-green-200',
        buttonActive: 'bg-green-500 text-white border-green-500'
      },
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-700',
        button: 'bg-blue-100 text-blue-700 border-blue-200',
        buttonActive: 'bg-blue-500 text-white border-blue-500'
      }
    }
    return colors[color]
  }

  const renderItemGrid = (items, itemType, iconComponent) => {
    const activeLimit = limitTypes.find(l => l.key === activeTab)
    const colors = getColorClasses(activeLimit.color)

    return (
      <div className="space-y-3">
        <div className="flex items-center gap-2">
          {iconComponent}
          <h5 className="font-medium text-gray-900 capitalize">{itemType}</h5>
          <span className="text-sm text-gray-500">
            ({formData.mappings[activeTab][itemType].length} selecionados)
          </span>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
          {items.map(item => {
            const isSelected = isItemSelected(activeTab, itemType, item.id)
            
            return (
              <button
                key={item.id}
                onClick={() => handleItemToggle(activeTab, itemType, item.id)}
                className={`p-3 rounded-lg border text-left transition-all hover:shadow-sm ${
                  isSelected 
                    ? colors.buttonActive
                    : `${colors.button} hover:${colors.buttonActive.replace('text-white', colors.text)}`
                }`}
              >
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium truncate">{item.name}</span>
                  {isSelected && <Check className="h-4 w-4 flex-shrink-0" />}
                </div>
                {item.description && (
                  <p className="text-xs opacity-75 mt-1 truncate">{item.description}</p>
                )}
              </button>
            )
          })}
        </div>
        
        {items.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <p>Nenhum item encontrado</p>
            <p className="text-sm">Crie {itemType} primeiro para poder selecioná-los</p>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Mapeamento de Categorias
        </h3>
        <p className="text-gray-600">
          Para cada tipo de limite, selecione as categorias, tags e contatos que pertencem a ele.
        </p>
      </div>

      {/* Tabs dos tipos de limite */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {limitTypes.map(limitType => {
          const colors = getColorClasses(limitType.color)
          const isActive = activeTab === limitType.key
          
          return (
            <button
              key={limitType.key}
              onClick={() => setActiveTab(limitType.key)}
              className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-md transition-all ${
                isActive 
                  ? `${colors.bg} ${colors.text} border ${colors.border}` 
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {limitType.icon}
              <span className="font-medium text-sm">{limitType.title}</span>
              <span className="text-xs opacity-75">({limitType.percentage.toFixed(0)}%)</span>
            </button>
          )
        })}
      </div>

      {/* Conteúdo da tab ativa */}
      <div className={`${getColorClasses(limitTypes.find(l => l.key === activeTab).color).bg} rounded-lg p-6 border ${getColorClasses(limitTypes.find(l => l.key === activeTab).color).border}`}>
        <div className="space-y-8">
          {/* Categorias */}
          {renderItemGrid(
            setupData.categories || [], 
            'categories',
            <Folder className="h-4 w-4 text-gray-600" />
          )}

          {/* Tags */}
          {renderItemGrid(
            setupData.tags || [], 
            'tags',
            <Tag className="h-4 w-4 text-gray-600" />
          )}

          {/* Contatos de Transações */}
          {renderItemGrid(
            setupData.transactionContacts || [], 
            'transactionContacts',
            <Users className="h-4 w-4 text-gray-600" />
          )}

          {/* Contatos de Empréstimos */}
          {renderItemGrid(
            setupData.loanContacts || [], 
            'loanContacts',
            <Users className="h-4 w-4 text-gray-600" />
          )}
        </div>
      </div>

      {/* Resumo das seleções */}
      <div className="bg-gray-50 rounded-lg p-4">
        <h5 className="font-medium text-gray-900 mb-3">Resumo das Seleções</h5>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {limitTypes.map(limitType => {
            const colors = getColorClasses(limitType.color)
            const mappings = formData.mappings[limitType.key]
            const totalSelected = Object.values(mappings).reduce((sum, arr) => sum + arr.length, 0)
            
            return (
              <div key={limitType.key} className={`${colors.bg} rounded-lg p-3 border ${colors.border}`}>
                <div className="flex items-center gap-2 mb-2">
                  {limitType.icon}
                  <span className="font-medium text-sm">{limitType.title}</span>
                </div>
                <div className="text-xs space-y-1">
                  <div>Categorias: {mappings.categories.length}</div>
                  <div>Tags: {mappings.tags.length}</div>
                  <div>Contatos Transações: {mappings.transactionContacts.length}</div>
                  <div>Contatos Empréstimos: {mappings.loanContacts.length}</div>
                  <div className="font-medium pt-1 border-t border-current border-opacity-20">
                    Total: {totalSelected} itens
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Dicas */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
        <h5 className="font-medium text-blue-900 mb-2">💡 Dicas para mapeamento:</h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Selecione todas as categorias que pertencem a cada tipo de limite</li>
          <li>• Tags ajudam a classificar transações específicas</li>
          <li>• Contatos identificam com quem você fez a transação</li>
          <li>• Um item pode pertencer a apenas um tipo de limite</li>
        </ul>
      </div>
    </div>
  )
}

export default Step2Mappings
