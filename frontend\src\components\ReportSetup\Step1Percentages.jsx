import React from 'react'
import { TrendingDown, PiggyBank, Coffee, AlertCircle } from 'lucide-react'

const Step1Percentages = ({ formData, setFormData }) => {
  const handlePercentageChange = (field, value, isSlider = false) => {
    const numValue = Math.max(0, Math.min(100, parseFloat(value) || 0))

    setFormData(prev => {
      const newData = { ...prev, [field]: numValue }

      // Só ajustar automaticamente se for pelo slider
      if (isSlider) {
        const otherFields = ['expensePercentage', 'savingsPercentage', 'leisurePercentage'].filter(key => key !== field)

        const remaining = 100 - numValue
        const otherTotal = otherFields.reduce((sum, key) => sum + prev[key], 0)

        if (otherTotal > 0) {
          otherFields.forEach(key => {
            newData[key] = Math.max(0, (prev[key] / otherTotal) * remaining)
          })
        } else {
          // Se outros campos são 0, distribuir igualmente
          const valuePerField = remaining / otherFields.length
          otherFields.forEach(key => {
            newData[key] = valuePerField
          })
        }
      }

      return newData
    })
  }

  const totalPercentage = formData.expensePercentage + formData.savingsPercentage + formData.leisurePercentage

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Defina os Limites de Gastos
        </h3>
        <p className="text-gray-600">
          Configure as porcentagens ideais para cada categoria de gasto. O total deve somar 100%.
        </p>
      </div>

      <div className="grid gap-6">
        {/* Despesas Essenciais */}
        <div className="bg-red-50 rounded-lg p-6 border border-red-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <TrendingDown className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Despesas Essenciais</h4>
              <p className="text-sm text-gray-600">Moradia, alimentação, transporte, contas básicas</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Porcentagem: {formData.expensePercentage.toFixed(1)}%
              </label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.expensePercentage.toFixed(1)}
                onChange={(e) => handlePercentageChange('expensePercentage', e.target.value, false)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm"
              />
            </div>
            
            <input
              type="range"
              min="0"
              max="100"
              step="0.1"
              value={formData.expensePercentage}
              onChange={(e) => handlePercentageChange('expensePercentage', e.target.value, true)}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${formData.expensePercentage}%, #e5e7eb ${formData.expensePercentage}%, #e5e7eb 100%)`
              }}
            />
          </div>
        </div>

        {/* Poupança/Investimentos */}
        <div className="bg-green-50 rounded-lg p-6 border border-green-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <PiggyBank className="h-5 w-5 text-green-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Poupança/Investimentos</h4>
              <p className="text-sm text-gray-600">Reserva de emergência, investimentos, aposentadoria</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Porcentagem: {formData.savingsPercentage.toFixed(1)}%
              </label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.savingsPercentage.toFixed(1)}
                onChange={(e) => handlePercentageChange('savingsPercentage', e.target.value, false)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm"
              />
            </div>

            <input
              type="range"
              min="0"
              max="100"
              step="0.1"
              value={formData.savingsPercentage}
              onChange={(e) => handlePercentageChange('savingsPercentage', e.target.value, true)}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #10b981 0%, #10b981 ${formData.savingsPercentage}%, #e5e7eb ${formData.savingsPercentage}%, #e5e7eb 100%)`
              }}
            />
          </div>
        </div>

        {/* Lazer */}
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
          <div className="flex items-center gap-3 mb-4">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Coffee className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h4 className="font-semibold text-gray-900">Lazer</h4>
              <p className="text-sm text-gray-600">Entretenimento, viagens, restaurantes, hobbies</p>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-gray-700">
                Porcentagem: {formData.leisurePercentage.toFixed(1)}%
              </label>
              <input
                type="number"
                min="0"
                max="100"
                step="0.1"
                value={formData.leisurePercentage.toFixed(1)}
                onChange={(e) => handlePercentageChange('leisurePercentage', e.target.value, false)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-center text-sm"
              />
            </div>

            <input
              type="range"
              min="0"
              max="100"
              step="0.1"
              value={formData.leisurePercentage}
              onChange={(e) => handlePercentageChange('leisurePercentage', e.target.value, true)}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${formData.leisurePercentage}%, #e5e7eb ${formData.leisurePercentage}%, #e5e7eb 100%)`
              }}
            />
          </div>
        </div>
      </div>

      {/* Total */}
      <div className={`p-4 rounded-lg border-2 ${
        Math.abs(totalPercentage - 100) < 0.01 
          ? 'border-green-200 bg-green-50' 
          : 'border-yellow-200 bg-yellow-50'
      }`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {Math.abs(totalPercentage - 100) < 0.01 ? (
              <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                <div className="w-2 h-2 bg-white rounded-full"></div>
              </div>
            ) : (
              <AlertCircle className="h-5 w-5 text-yellow-600" />
            )}
            <span className="font-medium text-gray-700">Total:</span>
          </div>
          <span className={`text-xl font-bold ${
            Math.abs(totalPercentage - 100) < 0.01 
              ? 'text-green-600' 
              : 'text-yellow-600'
          }`}>
            {totalPercentage.toFixed(1)}%
          </span>
        </div>
        
        {Math.abs(totalPercentage - 100) > 0.01 && (
          <p className="text-sm text-yellow-600 mt-2">
            Ajuste as porcentagens para somar exatamente 100%
          </p>
        )}
      </div>

      {/* Dicas */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
        <h5 className="font-medium text-blue-900 mb-2">💡 Dicas para definir porcentagens:</h5>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• <strong>50% Despesas:</strong> Regra comum para gastos essenciais</li>
          <li>• <strong>20% Poupança:</strong> Recomendado para construir patrimônio</li>
          <li>• <strong>30% Lazer:</strong> Equilibrio para qualidade de vida</li>
          <li>• Ajuste conforme sua realidade e objetivos financeiros</li>
        </ul>
      </div>
    </div>
  )
}

export default Step1Percentages
