import React, { useState, useEffect } from 'react'
import { 
  CreditCard, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  Eye,
  EyeOff,
  TrendingUp
} from 'lucide-react'
import { bankService } from '../services/bankService'
import { paymentMethodService } from '../services/bankService'
import api from '../services/api'
import toast from 'react-hot-toast'

function BillManager() {
  const [banks, setBanks] = useState([])
  const [paymentMethods, setPaymentMethods] = useState([])
  const [paymentOptions, setPaymentOptions] = useState([])
  const [billHistory, setBillHistory] = useState([])
  const [billAmounts, setBillAmounts] = useState({}) // Novo estado para valores das faturas
  const [billsData, setBillsData] = useState({}) // Dados completos das faturas do novo sistema
  const [loading, setLoading] = useState(true)
  const [showBalances, setShowBalances] = useState(true)
  const [showPaymentModal, setShowPaymentModal] = useState(false)
  const [showHistory, setShowHistory] = useState(false)
  const [selectedBillBank, setSelectedBillBank] = useState(null)
  const [paymentData, setPaymentData] = useState({
    paymentBankId: '',
    paymentMethodId: ''
  })

  // Estados para o histórico melhorado
  const [historyPage, setHistoryPage] = useState(1)
  const [historyFilters, setHistoryFilters] = useState({
    bankId: '',
    startDate: '',
    endDate: '',
    minAmount: '',
    maxAmount: ''
  })
  const [filteredHistory, setFilteredHistory] = useState([])
  const itemsPerPage = 10

  useEffect(() => {
    fetchData()

    // Escutar atualizações de assinaturas
    const handleSubscriptionUpdate = () => {
      console.log('🔄 Atualizando dados do BillManager após mudança em assinatura')
      fetchData()
    }

    window.addEventListener('subscriptionUpdated', handleSubscriptionUpdate)

    return () => {
      window.removeEventListener('subscriptionUpdated', handleSubscriptionUpdate)
    }
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [banksData, paymentMethodsData, paymentOptionsData, billHistoryData] = await Promise.all([
        bankService.getBanks(),
        paymentMethodService.getPaymentMethods(),
        bankService.getPaymentOptions(),
        bankService.getBillHistory()
      ])
      console.log('Dados carregados:', { banksData, paymentMethodsData, paymentOptionsData, billHistoryData })
      setBanks(banksData)
      setPaymentMethods(paymentMethodsData)
      setPaymentOptions(paymentOptionsData)
      setBillHistory(billHistoryData)

      // Buscar dados das faturas para bancos com crédito usando o novo sistema
      const banksWithCredit = banksData.filter(bank => bank.creditLimit > 0 && bank.billDueDay)
      const billAmountsData = {}
      const billsDataNew = {}

      for (const bank of banksWithCredit) {
        try {
          // Usar o novo endpoint de faturas
          const response = await api.get(`/bills/bank/${bank.id}`)
          const billData = response.data

          billsDataNew[bank.id] = billData
          billAmountsData[bank.id] = {
            totalBill: billData.totals?.total || 0,
            subscriptionsTotal: billData.totals?.subscriptions || 0,
            transactionsTotal: billData.totals?.transactions || 0,
            billStatus: billData.bill?.status || 'PENDING'
          }
        } catch (error) {
          console.error(`Erro ao buscar fatura do banco ${bank.name}:`, error)
          billAmountsData[bank.id] = {
            totalBill: 0,
            subscriptionsTotal: 0,
            transactionsTotal: 0,
            billStatus: 'PENDING'
          }
          billsDataNew[bank.id] = null
        }
      }

      setBillAmounts(billAmountsData)
      setBillsData(billsDataNew)

    } catch (error) {
      console.error('Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handlePayBill = (bank) => {
    setSelectedBillBank(bank)
    setShowPaymentModal(true)
    setPaymentData({
      paymentBankId: '',
      paymentMethodId: ''
    })
  }

  const handleConfirmPayment = async () => {
    try {
      if (!paymentData.paymentBankId) {
        toast.error('Selecione um banco para pagamento')
        return
      }

      const billTotal = getBillTotal(selectedBillBank.id)
      if (billTotal <= 0) {
        toast.error('Não há fatura para pagar neste banco')
        return
      }

      // Usar o novo endpoint de faturas
      const response = await api.post(`/bills/bank/${selectedBillBank.id}/pay`, {
        paymentBankId: paymentData.paymentBankId
      })

      toast.success(
        `Fatura paga com sucesso!\n` +
        `Valor: ${response.data.amount.toLocaleString('pt-BR', { style: 'currency', currency: 'BRL' })}\n` +
        `${response.data.subscriptionsMarkedAsPaid} assinaturas pagas\n` +
        `${response.data.transactionsMarkedAsPaid} transações pagas\n` +
        `Pago com: ${response.data.paymentBank}`
      )
      setShowPaymentModal(false)
      setSelectedBillBank(null)
      fetchData()
    } catch (error) {
      console.error('Erro ao pagar fatura:', error)
      toast.error(error.response?.data?.error || 'Erro ao pagar fatura')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR')
  }

  const getBillTotal = (bankId) => {
    return billAmounts[bankId]?.totalBill || 0
  }

  // Função para filtrar histórico
  const filterHistory = () => {
    let filtered = [...billHistory]

    // Filtro por banco
    if (historyFilters.bankId) {
      filtered = filtered.filter(bill => bill.billBank.id === historyFilters.bankId)
    }

    // Filtro por data
    if (historyFilters.startDate) {
      const startDate = new Date(historyFilters.startDate)
      filtered = filtered.filter(bill => new Date(bill.date) >= startDate)
    }

    if (historyFilters.endDate) {
      const endDate = new Date(historyFilters.endDate)
      endDate.setHours(23, 59, 59, 999) // Final do dia
      filtered = filtered.filter(bill => new Date(bill.date) <= endDate)
    }

    // Filtro por valor mínimo
    if (historyFilters.minAmount) {
      const minAmount = parseFloat(historyFilters.minAmount)
      filtered = filtered.filter(bill => bill.totalAmount >= minAmount)
    }

    // Filtro por valor máximo
    if (historyFilters.maxAmount) {
      const maxAmount = parseFloat(historyFilters.maxAmount)
      filtered = filtered.filter(bill => bill.totalAmount <= maxAmount)
    }

    // Ordenar por data (mais recente primeiro)
    filtered.sort((a, b) => new Date(b.date) - new Date(a.date))

    setFilteredHistory(filtered)
    setHistoryPage(1) // Reset para primeira página
  }

  // Aplicar filtros quando histórico ou filtros mudarem
  useEffect(() => {
    filterHistory()
  }, [billHistory, historyFilters])

  // Calcular dados da paginação
  const totalPages = Math.ceil(filteredHistory.length / itemsPerPage)
  const startIndex = (historyPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentPageHistory = filteredHistory.slice(startIndex, endIndex)

  // Função para limpar filtros
  const clearFilters = () => {
    setHistoryFilters({
      bankId: '',
      startDate: '',
      endDate: '',
      minAmount: '',
      maxAmount: ''
    })
  }

  const getNextDueDate = (bank) => {
    if (!bank.billDueDay) return null
    
    const today = new Date()
    const dueDate = new Date(today.getFullYear(), today.getMonth(), bank.billDueDay)
    
    if (dueDate < today) {
      dueDate.setMonth(dueDate.getMonth() + 1)
    }
    
    return dueDate
  }

  const getDaysUntilDue = (bank) => {
    const dueDate = getNextDueDate(bank)
    if (!dueDate) return null
    
    const today = new Date()
    const diffTime = dueDate - today
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  const getBillStatus = (bank, creditMethods) => {
    if (creditMethods.length === 0) return 'no-card'

    // Usar dados do novo sistema de faturas
    const billData = billAmounts[bank.id]
    if (!billData) return 'no-bill'

    const totalBill = billData.totalBill || 0
    const billStatus = billData.billStatus || 'PENDING'

    if (totalBill === 0) return 'no-bill'

    // Mapear status do backend para o frontend
    switch (billStatus) {
      case 'PENDING':
        return 'pending'
      case 'OVERDUE':
        return 'overdue'
      case 'PAID':
        return 'paid'
      default:
        return 'pending'
    }
  }

  // Filtrar apenas bancos com limite de crédito
  const banksWithCredit = banks.filter(bank => bank.creditLimit > 0)

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  const totalPending = banksWithCredit.reduce((sum, bank) => {
    return sum + getBillTotal(bank.id)
  }, 0)

  const banksWithBills = banksWithCredit.filter(bank => getBillTotal(bank.id) > 0)

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Faturas dos Cartões</h2>
          <p className="text-gray-600 mt-1">
            Gerencie as faturas dos seus cartões de crédito por banco
          </p>
        </div>
        <div className="flex items-center gap-4">
          <button
            onClick={() => setShowHistory(!showHistory)}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            <Calendar className="h-4 w-4" />
            {showHistory ? 'Ocultar Histórico' : 'Ver Histórico'}
          </button>
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center gap-2 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {showBalances ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            {showBalances ? 'Ocultar Valores' : 'Mostrar Valores'}
          </button>
          <div className="text-right">
            <p className="text-sm text-gray-600">Total Pendente</p>
            <p className="text-2xl font-bold text-red-600">
              {showBalances ? formatCurrency(totalPending) : '••••••••'}
            </p>
          </div>
        </div>
      </div>

      {/* Resumo */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-red-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <DollarSign className="h-5 w-5 text-red-600" />
            <span className="font-medium text-red-900">Total Faturas</span>
          </div>
          <p className="text-2xl font-bold text-red-600">
            {showBalances ? formatCurrency(totalPending) : '••••••••'}
          </p>
        </div>

        <div className="bg-blue-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <CreditCard className="h-5 w-5 text-blue-600" />
            <span className="font-medium text-blue-900">Bancos com Crédito</span>
          </div>
          <p className="text-2xl font-bold text-blue-600">{banksWithCredit.length}</p>
        </div>

        <div className="bg-orange-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <AlertCircle className="h-5 w-5 text-orange-600" />
            <span className="font-medium text-orange-900">Com Faturas</span>
          </div>
          <p className="text-2xl font-bold text-orange-600">{banksWithBills.length}</p>
        </div>

        <div className="bg-green-50 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            <span className="font-medium text-green-900">Limite Total</span>
          </div>
          <p className="text-2xl font-bold text-green-600">
            {showBalances ? formatCurrency(banksWithCredit.reduce((sum, bank) => sum + bank.creditLimit, 0)) : '••••••••'}
          </p>
        </div>
      </div>

      {/* Lista de Faturas */}
      {banksWithCredit.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum cartão de crédito encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Configure o limite de crédito nos seus bancos para ver as faturas aqui
          </p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {banksWithCredit.map((bank) => {
            const creditMethods = paymentMethods.filter(
              method => method.bankId === bank.id && method.type === 'CREDIT'
            )
            const billStatus = getBillStatus(bank, creditMethods)
            const billTotal = getBillTotal(bank.id)
            const daysUntilDue = getDaysUntilDue(bank)
            const nextDueDate = getNextDueDate(bank)

            return (
              <div
                key={bank.id}
                className="bg-white rounded-xl border shadow-sm hover:shadow-md transition-all duration-200"
              >
                <div className="p-6">
                  {/* Header do Card */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div 
                        className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl"
                        style={{ backgroundColor: bank.color + '20' }}
                      >
                        {bank.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">
                          {bank.name}
                        </h3>
                        <p className="text-sm text-gray-600">
                          Limite: {showBalances ? formatCurrency(bank.creditLimit) : '••••••'}
                        </p>
                      </div>
                    </div>
                    
                    {/* Status da Fatura */}
                    {billStatus === 'pending' && (
                      <AlertCircle className="h-5 w-5 text-yellow-500" />
                    )}
                    {billStatus === 'overdue' && (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    {billStatus === 'paid' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {billStatus === 'no-bill' && (
                      <Clock className="h-5 w-5 text-gray-400" />
                    )}
                  </div>

                  {/* Informações da Fatura */}
                  <div className="space-y-3">
                    <div className="bg-gray-50 rounded-xl p-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-gray-600">Fatura Atual</span>
                        <span className={`text-xl font-bold ${
                          billTotal > 0 ? 'text-red-600' : 'text-gray-900'
                        }`}>
                          {showBalances ? formatCurrency(billTotal) : '••••••••'}
                        </span>
                      </div>
                      
                      {nextDueDate && (
                        <div className="flex justify-between items-center text-sm">
                          <span className="text-gray-500">Vencimento</span>
                          <span className={`font-medium ${
                            daysUntilDue <= 3 ? 'text-red-600' : 'text-gray-700'
                          }`}>
                            {formatDate(nextDueDate)}
                            {daysUntilDue !== null && (
                              <span className="ml-1">
                                ({daysUntilDue} dias)
                              </span>
                            )}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Limite Disponível */}
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-500">Limite Disponível</span>
                      <span className="text-blue-600 font-medium">
                        {showBalances ? formatCurrency(bank.availableLimit || 0) : '••••••'}
                      </span>
                    </div>

                    {/* Utilização do Limite */}
                    {bank.creditLimit > 0 && (
                      <div>
                        <div className="flex justify-between items-center text-sm mb-1">
                          <span className="text-gray-500">Utilização</span>
                          <span className="text-gray-700 font-medium">
                            {((bank.creditLimit - (bank.availableLimit || 0)) / bank.creditLimit * 100).toFixed(1)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{
                              width: `${(bank.creditLimit - (bank.availableLimit || 0)) / bank.creditLimit * 100}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Ações */}
                  {creditMethods.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-gray-200">
                      {(() => {
                        // Regra 0: Botão disponível apenas se status for PENDING ou OVERDUE e valor > 0
                        const canPay = billTotal > 0 && (billStatus === 'pending' || billStatus === 'overdue')
                        const buttonText = billTotal <= 0
                          ? 'Sem Fatura para Pagar'
                          : billStatus === 'paid'
                          ? 'Fatura Já Paga'
                          : billStatus === 'overdue'
                          ? 'Pagar Fatura (Em Atraso)'
                          : 'Pagar Fatura'

                        const buttonColor = billStatus === 'overdue'
                          ? 'bg-red-600 hover:bg-red-700'
                          : canPay
                          ? 'bg-green-600 hover:bg-green-700'
                          : 'bg-gray-400 cursor-not-allowed'

                        return (
                          <button
                            onClick={() => handlePayBill(bank)}
                            disabled={!canPay}
                            className={`w-full flex items-center justify-center gap-2 px-4 py-2 text-white rounded-lg transition-colors ${buttonColor}`}
                          >
                            <CheckCircle className="h-4 w-4" />
                            {buttonText}
                          </button>
                        )
                      })()}
                    </div>
                  )}
                </div>
              </div>
            )
          })}
        </div>
      )}

      {/* Histórico de Faturas Melhorado */}
      {showHistory && (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-bold text-gray-900">Histórico de Faturas</h3>
            <div className="text-sm text-gray-600">
              {filteredHistory.length} de {billHistory.length} faturas
            </div>
          </div>

          {/* Filtros */}
          <div className="bg-white rounded-xl border shadow-sm p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="font-medium text-gray-900">Filtros</h4>
              <button
                onClick={clearFilters}
                className="text-sm text-blue-600 hover:text-blue-700 font-medium"
              >
                Limpar Filtros
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              {/* Filtro por Banco */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Banco
                </label>
                <select
                  value={historyFilters.bankId}
                  onChange={(e) => setHistoryFilters({...historyFilters, bankId: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Todos os bancos</option>
                  {banksWithCredit.map(bank => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Filtro por Data Inicial */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Inicial
                </label>
                <input
                  type="date"
                  value={historyFilters.startDate}
                  onChange={(e) => setHistoryFilters({...historyFilters, startDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Filtro por Data Final */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Data Final
                </label>
                <input
                  type="date"
                  value={historyFilters.endDate}
                  onChange={(e) => setHistoryFilters({...historyFilters, endDate: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Filtro por Valor Mínimo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor Mínimo
                </label>
                <input
                  type="number"
                  step="0.01"
                  placeholder="0,00"
                  value={historyFilters.minAmount}
                  onChange={(e) => setHistoryFilters({...historyFilters, minAmount: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              {/* Filtro por Valor Máximo */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Valor Máximo
                </label>
                <input
                  type="number"
                  step="0.01"
                  placeholder="0,00"
                  value={historyFilters.maxAmount}
                  onChange={(e) => setHistoryFilters({...historyFilters, maxAmount: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Tabela de Histórico */}
          {filteredHistory.length === 0 ? (
            <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
              <Calendar className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {billHistory.length === 0 ? 'Nenhuma fatura paga encontrada' : 'Nenhuma fatura encontrada com os filtros aplicados'}
              </h3>
              {billHistory.length > 0 && (
                <button
                  onClick={clearFilters}
                  className="text-blue-600 hover:text-blue-700 font-medium"
                >
                  Limpar filtros
                </button>
              )}
            </div>
          ) : (
            <div className="bg-white rounded-xl border shadow-sm overflow-hidden">
              {/* Cabeçalho da Tabela */}
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 border-b border-gray-200">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Banco
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Data
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Valor
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pago com
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Transações
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {currentPageHistory.map((bill) => (
                      <tr key={bill.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-3">
                            <div
                              className="w-8 h-8 rounded-lg flex items-center justify-center text-sm"
                              style={{ backgroundColor: bill.billBank.color + '20' }}
                            >
                              {bill.billBank.icon}
                            </div>
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {bill.billBank.name}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {formatDate(bill.date)}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm font-medium text-green-600">
                            {showBalances ? formatCurrency(bill.totalAmount) : '••••••••'}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center gap-2">
                            <span className="text-sm">{bill.paymentBank.icon}</span>
                            <span className="text-sm text-gray-900">
                              {bill.paymentBank.name}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-600">
                            {bill.transactions.length} transação(ões)
                          </div>
                          {bill.transactions.length > 0 && (
                            <div className="mt-1 space-y-1">
                              {bill.transactions.slice(0, 2).map((transaction) => (
                                <div key={transaction.id} className="text-xs text-gray-500 flex items-center gap-1">
                                  {transaction.category && (
                                    <span>{transaction.category.icon}</span>
                                  )}
                                  <span className="truncate max-w-32">
                                    {transaction.description.replace('Pagamento - ', '')}
                                  </span>
                                  <span className="font-medium">
                                    {showBalances ? formatCurrency(transaction.amount) : '••••'}
                                  </span>
                                </div>
                              ))}
                              {bill.transactions.length > 2 && (
                                <div className="text-xs text-gray-400">
                                  +{bill.transactions.length - 2} mais...
                                </div>
                              )}
                            </div>
                          )}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {/* Paginação */}
              {totalPages > 1 && (
                <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-gray-700">
                      Mostrando {startIndex + 1} a {Math.min(endIndex, filteredHistory.length)} de {filteredHistory.length} resultados
                    </div>
                    <div className="flex items-center gap-2">
                      <button
                        onClick={() => setHistoryPage(Math.max(1, historyPage - 1))}
                        disabled={historyPage === 1}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Anterior
                      </button>

                      <div className="flex items-center gap-1">
                        {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                          let pageNum
                          if (totalPages <= 5) {
                            pageNum = i + 1
                          } else if (historyPage <= 3) {
                            pageNum = i + 1
                          } else if (historyPage >= totalPages - 2) {
                            pageNum = totalPages - 4 + i
                          } else {
                            pageNum = historyPage - 2 + i
                          }

                          return (
                            <button
                              key={pageNum}
                              onClick={() => setHistoryPage(pageNum)}
                              className={`px-3 py-1 text-sm border rounded-lg ${
                                historyPage === pageNum
                                  ? 'bg-blue-600 text-white border-blue-600'
                                  : 'border-gray-300 hover:bg-gray-100'
                              }`}
                            >
                              {pageNum}
                            </button>
                          )
                        })}
                      </div>

                      <button
                        onClick={() => setHistoryPage(Math.min(totalPages, historyPage + 1))}
                        disabled={historyPage === totalPages}
                        className="px-3 py-1 text-sm border border-gray-300 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Próximo
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Modal de Seleção de Pagamento */}
      {showPaymentModal && selectedBillBank && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Pagar Fatura - {selectedBillBank.name}
            </h3>

            <div className="space-y-4">
              {/* Valor da Fatura */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-gray-600">Valor da Fatura</span>
                  <span className="text-xl font-bold text-red-600">
                    {formatCurrency(getBillTotal(selectedBillBank.id))}
                  </span>
                </div>
              </div>

              {/* Seleção do Banco para Pagamento */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banco para Pagamento
                </label>
                {paymentOptions.length === 0 ? (
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500">
                    Nenhum banco com saldo disponível
                  </div>
                ) : (
                  <select
                    value={paymentData.paymentBankId}
                    onChange={(e) => setPaymentData({ ...paymentData, paymentBankId: e.target.value, paymentMethodId: '' })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Selecione um banco</option>
                    {paymentOptions.map((bank) => (
                      <option key={bank.id} value={bank.id}>
                        {bank.icon} {bank.name} - {formatCurrency(bank.currentBalance)}
                      </option>
                    ))}
                  </select>
                )}
              </div>

              {/* Seleção da Forma de Pagamento (Opcional) */}
              {paymentData.paymentBankId && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Forma de Pagamento (Opcional)
                  </label>
                  <select
                    value={paymentData.paymentMethodId}
                    onChange={(e) => setPaymentData({ ...paymentData, paymentMethodId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="">Débito direto da conta</option>
                    {paymentOptions
                      .find(bank => bank.id === paymentData.paymentBankId)
                      ?.paymentMethods?.map((method) => (
                        <option key={method.id} value={method.id}>
                          {method.name} ({method.type})
                        </option>
                      ))}
                  </select>
                </div>
              )}
            </div>

            {/* Botões */}
            <div className="flex gap-3 mt-6">
              <button
                onClick={() => {
                  setShowPaymentModal(false)
                  setSelectedBillBank(null)
                }}
                className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleConfirmPayment}
                disabled={!paymentData.paymentBankId}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
              >
                Confirmar Pagamento
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BillManager
