import { useState, useEffect } from 'react'
import {
  Target,
  Plus,
  Edit3,
  Trash2,
  Upload,
  X,
  TrendingUp,
  Check<PERSON>ir<PERSON>,
  RotateCcw
} from 'lucide-react'
import goalService from '../services/goalService'
import categoryService from '../services/categoryService'
import tagService from '../services/tagService'
import CurrencyInput from './CurrencyInput'
import TagSelector from './TagSelector'
import GoalProjectionModal from './GoalProjectionModal'
import ComplexGoalCard from './ComplexGoalCard'
import toast from 'react-hot-toast'

const GoalManager = () => {
  const [goals, setGoals] = useState([])
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingGoal, setEditingGoal] = useState(null)
  const [selectedImage, setSelectedImage] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [selectedGoal, setSelectedGoal] = useState(null)
  const [showProjectionModal, setShowProjectionModal] = useState(false)
  const [goalType, setGoalType] = useState('simple') // 'simple' ou 'complex'
  const [showComplexItemModal, setShowComplexItemModal] = useState(false)
  const [complexItems, setComplexItems] = useState([])
  const [editingComplexItem, setEditingComplexItem] = useState(null)
  const [showCompleteModal, setShowCompleteModal] = useState(false)
  const [goalToComplete, setGoalToComplete] = useState(null)
  const [completionValue, setCompletionValue] = useState(0)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    targetAmount: 0,
    targetYear: new Date().getFullYear(),
    targetMonth: new Date().getMonth() + 1,
    categoryIds: [],
    tagIds: [],
    type: 'simple', // 'simple' ou 'complex'
    items: [] // Para objetivos complexos
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [goalsData, categoriesData, tagsData] = await Promise.all([
        goalService.getGoals(),
        categoryService.getCategories(),
        tagService.getTags()
      ])
      
      setGoals(goalsData)
      setCategories(categoriesData)
      setTags(tagsData)
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()

    if (!formData.name) {
      toast.error('Nome é obrigatório')
      return
    }

    // Validações específicas por tipo
    if (goalType === 'complex' || formData.type === 'complex') {
      if (complexItems.length === 0) {
        toast.error('Objetivo complexo deve ter pelo menos um item')
        return
      }
    } else {
      if (!formData.targetAmount) {
        toast.error('Valor é obrigatório para objetivos simples')
        return
      }

      // Validar data mínima (3 meses no futuro) apenas para objetivos simples
      const currentDate = new Date()
      const targetDate = new Date(formData.targetYear, formData.targetMonth - 1, 1)
      const minDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 3, 1)

      if (targetDate < minDate) {
        const minMonth = minDate.getMonth() + 1
        const minYear = minDate.getFullYear()
        toast.error(`O objetivo deve ser planejado para pelo menos 3 meses no futuro. Data mínima: ${minMonth.toString().padStart(2, '0')}/${minYear}`)
        return
      }
    }

    try {
      let goalData;

      if (goalType === 'complex') {
        // Para objetivos complexos, enviar apenas os dados necessários
        goalData = {
          name: formData.name,
          description: formData.description,
          type: 'complex',
          items: JSON.stringify(complexItems),
          image: selectedImage,
          categoryIds: [], // Sem categorias para complexos
          tagIds: [] // Sem tags para complexos
        }
      } else {
        // Para objetivos simples, enviar todos os dados
        goalData = {
          ...formData,
          image: selectedImage,
          type: 'simple'
        }
      }

      if (editingGoal) {
        await goalService.updateGoal(editingGoal.id, goalData)
        toast.success('Objetivo atualizado com sucesso!')
      } else {
        await goalService.createGoal(goalData)
        toast.success('Objetivo criado com sucesso!')
      }

      resetForm()
      fetchData()
    } catch (error) {
      console.error('Erro ao salvar objetivo:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar objetivo')
    }
  }

  const handleEdit = (goal) => {
    setEditingGoal(goal)

    // Configurar tipo do objetivo
    setGoalType(goal.type || 'simple')

    if (goal.type === 'complex') {
      // Para objetivos complexos, carregar os itens
      setComplexItems(goal.subGoals || [])
      setFormData({
        name: goal.name,
        description: goal.description || '',
        targetAmount: goal.targetAmount,
        targetYear: goal.targetYear,
        targetMonth: goal.targetMonth,
        categoryIds: [],
        tagIds: [],
        type: 'complex'
      })
    } else {
      // Para objetivos simples
      setFormData({
        name: goal.name,
        description: goal.description || '',
        targetAmount: goal.targetAmount,
        targetYear: goal.targetYear,
        targetMonth: goal.targetMonth,
        categoryIds: goal.categories.map(c => c.categoryId),
        tagIds: goal.tags.map(t => t.tagId),
        type: 'simple'
      })
    }

    setImagePreview(goal.imageUrl)
    setShowModal(true)
  }

  const handleDelete = async (goal) => {
    if (!confirm(`Tem certeza que deseja excluir o objetivo "${goal.name}"?`)) {
      return
    }

    try {
      await goalService.deleteGoal(goal.id)
      toast.success('Objetivo excluído com sucesso!')
      fetchData()
    } catch (error) {
      console.error('Erro ao excluir objetivo:', error)
      toast.error('Erro ao excluir objetivo')
    }
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (e) => setImagePreview(e.target.result)
      reader.readAsDataURL(file)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      targetAmount: 0,
      targetYear: new Date().getFullYear(),
      targetMonth: new Date().getMonth() + 1,
      categoryIds: [],
      tagIds: [],
      type: 'simple',
      items: []
    })
    setEditingGoal(null)
    setSelectedImage(null)
    setImagePreview(null)
    setShowModal(false)
    setGoalType('simple')
    setComplexItems([])
    setShowComplexItemModal(false)
    setEditingComplexItem(null)
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getMonthName = (month) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ]
    return months[month - 1]
  }

  const handleViewProjection = async (goal) => {
    setSelectedGoal(goal)
    setShowProjectionModal(true)
  }

  const handleCompleteGoal = async (goal) => {
    setGoalToComplete(goal)
    setCompletionValue(goal.targetAmount)
    setShowCompleteModal(true)
  }

  const confirmCompleteGoal = async () => {
    if (!goalToComplete || !completionValue) {
      toast.error('Valor é obrigatório')
      return
    }

    try {
      await goalService.completeGoal(goalToComplete.id, completionValue)
      toast.success('Objetivo marcado como concluído!')
      setShowCompleteModal(false)
      setGoalToComplete(null)
      setCompletionValue(0)
      fetchData()
    } catch (error) {
      console.error('Erro ao marcar objetivo como concluído:', error)
      toast.error('Erro ao marcar objetivo como concluído')
    }
  }

  const handleUncompleteGoal = async (goal) => {
    if (!confirm(`Tem certeza que deseja desmarcar "${goal.name}" como concluído?`)) {
      return
    }

    try {
      await goalService.uncompleteGoal(goal.id)
      toast.success('Objetivo desmarcado como concluído!')
      fetchData()
    } catch (error) {
      console.error('Erro ao desmarcar objetivo:', error)
      toast.error('Erro ao desmarcar objetivo')
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Target className="h-6 w-6 text-blue-600" />
            Objetivos Financeiros
          </h2>
          <p className="text-gray-600 mt-1">Defina e acompanhe suas metas financeiras</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Objetivo
        </button>
      </div>

      {/* Lista de Objetivos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {goals.map((goal) => {
          // Renderizar card específico para objetivos complexos
          if (goal.type === 'complex') {
            return (
              <ComplexGoalCard
                key={goal.id}
                goal={goal}
                onUpdate={fetchData}
              />
            )
          }

          // Renderizar card padrão para objetivos simples
          return (
            <div key={goal.id} className={`group relative bg-white rounded-2xl shadow-lg border overflow-hidden hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 ${
              goal.isCompleted
                ? 'border-green-200 ring-2 ring-green-100'
                : 'border-gray-100'
            }`}>
            {/* Badge de Concluído */}
            {goal.isCompleted && (
              <div className="absolute top-4 left-4 z-10">
                <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1 shadow-lg">
                  <CheckCircle className="h-4 w-4" />
                  Concluído
                </div>
              </div>
            )}

            {/* Imagem de Fundo com Overlay */}
            <div className={`relative h-56 ${
              goal.isCompleted
                ? 'bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500'
                : 'bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500'
            }`}>
              {goal.imageUrl ? (
                <>
                  <img
                    src={goal.imageUrl}
                    alt={goal.name}
                    className={`w-full h-full object-cover ${goal.isCompleted ? 'opacity-80' : ''}`}
                  />
                  <div className={`absolute inset-0 ${
                    goal.isCompleted
                      ? 'bg-gradient-to-t from-green-900/60 via-green-900/20 to-transparent'
                      : 'bg-gradient-to-t from-black/60 via-black/20 to-transparent'
                  }`}></div>
                </>
              ) : (
                <div className={`absolute inset-0 opacity-90 ${
                  goal.isCompleted
                    ? 'bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500'
                    : 'bg-gradient-to-br from-blue-500 via-purple-500 to-pink-500'
                }`}></div>
              )}

              {/* Ações no Topo */}
              <div className="absolute top-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                {!goal.isCompleted ? (
                  <>
                    <button
                      onClick={() => handleCompleteGoal(goal)}
                      className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-green-500/80 rounded-xl transition-colors"
                      title="Marcar como Concluído"
                    >
                      <CheckCircle className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleViewProjection(goal)}
                      className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-xl transition-colors"
                      title="Ver Projeção"
                    >
                      <TrendingUp className="h-4 w-4" />
                    </button>
                  </>
                ) : (
                  <button
                    onClick={() => handleUncompleteGoal(goal)}
                    className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-orange-500/80 rounded-xl transition-colors"
                    title="Desmarcar como Concluído"
                  >
                    <RotateCcw className="h-4 w-4" />
                  </button>
                )}
                <button
                  onClick={() => handleEdit(goal)}
                  className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-xl transition-colors"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4" />
                </button>
                <button
                  onClick={() => handleDelete(goal)}
                  className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-red-500/80 rounded-xl transition-colors"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>

              {/* Valor em Destaque */}
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 border border-white/30">
                  {goal.isCompleted ? (
                    <div className="space-y-2">
                      <div className="text-center">
                        <p className="text-white/80 text-sm font-medium">Objetivo Concluído</p>
                        <p className="text-white/80 text-xs">
                          {new Date(goal.completedAt).toLocaleDateString('pt-BR')}
                        </p>
                      </div>
                      <div className="grid grid-cols-2 gap-3 text-center">
                        <div>
                          <p className="text-white/80 text-xs font-medium">Planejado</p>
                          <p className="text-white text-lg font-bold">
                            {formatCurrency(goal.targetAmount)}
                          </p>
                        </div>
                        <div>
                          <p className="text-white/80 text-xs font-medium">Gasto</p>
                          <p className="text-white text-lg font-bold">
                            {formatCurrency(goal.completedAmount || goal.targetAmount)}
                          </p>
                        </div>
                      </div>
                      {goal.completedAmount && goal.completedAmount !== goal.targetAmount && (
                        <div className="text-center">
                          <p className={`text-xs font-medium ${
                            goal.completedAmount <= goal.targetAmount ? 'text-green-200' : 'text-red-200'
                          }`}>
                            {goal.completedAmount <= goal.targetAmount ? 'Economia: ' : 'Excesso: '}
                            {formatCurrency(Math.abs(goal.targetAmount - goal.completedAmount))}
                          </p>
                        </div>
                      )}
                    </div>
                  ) : goal.type === 'complex' ? (
                    // Layout para objetivos complexos
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-white/80 text-sm font-medium">Meta Total</p>
                          <p className="text-white text-xl font-bold">
                            {formatCurrency(goal.targetAmount)}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-white/80 text-sm font-medium">Prazo</p>
                          <p className="text-white text-lg font-semibold">
                            {getMonthName(goal.targetMonth)}/{goal.targetYear}
                          </p>
                        </div>
                      </div>
                      {/* Progresso baseado em itens concluídos */}
                      {goal.subGoals && goal.subGoals.length > 0 && (
                        <div>
                          <div className="flex items-center justify-between text-white/80 text-xs mb-1">
                            <span>Progresso</span>
                            <span>{goal.subGoals.filter(item => item.isCompleted).length}/{goal.subGoals.length} itens</span>
                          </div>
                          <div className="w-full bg-white/20 rounded-full h-2">
                            <div
                              className="bg-white h-2 rounded-full transition-all duration-300"
                              style={{
                                width: `${(goal.subGoals.filter(item => item.isCompleted).length / goal.subGoals.length) * 100}%`
                              }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  ) : (
                    // Layout para objetivos simples
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-white/80 text-sm font-medium">Meta</p>
                        <p className="text-white text-2xl font-bold">
                          {formatCurrency(goal.targetAmount)}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="text-white/80 text-sm font-medium">Prazo</p>
                        <p className="text-white text-lg font-semibold">
                          {getMonthName(goal.targetMonth)}/{goal.targetYear}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Conteúdo Principal */}
            <div className="p-6">
              {/* Título e Descrição */}
              <div className="mb-4">
                <h3 className="font-bold text-gray-900 text-xl mb-2 line-clamp-1">{goal.name}</h3>
                {goal.description && (
                  <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">{goal.description}</p>
                )}
              </div>

              {/* Status do Objetivo */}
              {goal.isCompleted && (
                <div className="mb-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center gap-2 text-green-700 mb-3">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm font-medium">
                      Concluído em {new Date(goal.completedAt).toLocaleDateString('pt-BR')}
                    </span>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600 text-xs font-medium mb-1">Valor Planejado</p>
                      <p className="text-gray-900 font-semibold">
                        {formatCurrency(goal.targetAmount)}
                      </p>
                    </div>
                    <div>
                      <p className="text-green-600 text-xs font-medium mb-1">Valor Gasto</p>
                      <p className="text-green-700 font-semibold">
                        {formatCurrency(goal.completedAmount || goal.targetAmount)}
                      </p>
                    </div>
                  </div>

                  {goal.completedAmount && goal.completedAmount !== goal.targetAmount && (
                    <div className="mt-3 pt-3 border-t border-green-200">
                      <div className={`text-center text-sm font-medium ${
                        goal.completedAmount <= goal.targetAmount ? 'text-green-700' : 'text-red-600'
                      }`}>
                        {goal.completedAmount <= goal.targetAmount ? '💰 Economia: ' : '⚠️ Excesso: '}
                        {formatCurrency(Math.abs(goal.targetAmount - goal.completedAmount))}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Categorias e Tags */}
              {(goal.categories.length > 0 || goal.tags.length > 0) && (
                <div className="space-y-3">
                  {goal.categories.length > 0 && (
                    <div>
                      <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Categorias</p>
                      <div className="flex flex-wrap gap-1">
                        {goal.categories.slice(0, 3).map((mapping) => (
                          <span
                            key={mapping.id}
                            className="px-3 py-1 text-xs font-medium rounded-full bg-blue-50 text-blue-700 border border-blue-200"
                          >
                            {mapping.category.name}
                          </span>
                        ))}
                        {goal.categories.length > 3 && (
                          <span className="px-3 py-1 text-xs font-medium rounded-full bg-gray-50 text-gray-500 border border-gray-200">
                            +{goal.categories.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}

                  {goal.tags.length > 0 && (
                    <div>
                      <p className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">Tags</p>
                      <div className="flex flex-wrap gap-1">
                        {goal.tags.slice(0, 3).map((mapping) => (
                          <span
                            key={mapping.id}
                            className="px-3 py-1 text-xs font-medium rounded-full bg-purple-50 text-purple-700 border border-purple-200"
                          >
                            {mapping.tag.name}
                          </span>
                        ))}
                        {goal.tags.length > 3 && (
                          <span className="px-3 py-1 text-xs font-medium rounded-full bg-gray-50 text-gray-500 border border-gray-200">
                            +{goal.tags.length - 3}
                          </span>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Botões de Ação Principal */}
              <div className="mt-6 pt-4 border-t border-gray-100 space-y-3">
                {goal.isCompleted ? (
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => handleUncompleteGoal(goal)}
                      className="bg-gradient-to-r from-orange-500 to-red-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-orange-600 hover:to-red-600 transition-all duration-300 flex items-center justify-center gap-2 group"
                    >
                      <RotateCcw className="h-4 w-4 group-hover:scale-110 transition-transform" />
                      Reabrir
                    </button>
                    <button
                      onClick={() => handleEdit(goal)}
                      className="bg-gradient-to-r from-gray-500 to-gray-600 text-white font-semibold py-3 px-4 rounded-xl hover:from-gray-600 hover:to-gray-700 transition-all duration-300 flex items-center justify-center gap-2 group"
                    >
                      <Edit3 className="h-4 w-4 group-hover:scale-110 transition-transform" />
                      Editar
                    </button>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 gap-3">
                    <button
                      onClick={() => handleCompleteGoal(goal)}
                      className="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 flex items-center justify-center gap-2 group"
                    >
                      <CheckCircle className="h-4 w-4 group-hover:scale-110 transition-transform" />
                      Concluir
                    </button>
                    <button
                      onClick={() => handleViewProjection(goal)}
                      className="bg-gradient-to-r from-blue-500 to-purple-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-blue-600 hover:to-purple-600 transition-all duration-300 flex items-center justify-center gap-2 group"
                    >
                      <TrendingUp className="h-4 w-4 group-hover:scale-110 transition-transform" />
                      Projeção
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
          )
        })}
      </div>

      {goals.length === 0 && (
        <div className="text-center py-12">
          <Target className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum objetivo cadastrado</h3>
          <p className="text-gray-600 mb-4">Comece definindo seus objetivos financeiros</p>
          <button
            onClick={() => setShowModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Criar Primeiro Objetivo
          </button>
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
            {/* Header */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center">
                    <Target className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">
                      {editingGoal ? 'Editar Objetivo' : 'Novo Objetivo'}
                    </h3>
                    <p className="text-sm text-gray-600">
                      {editingGoal ? 'Modifique as informações do seu objetivo' : 'Defina uma nova meta financeira'}
                    </p>
                  </div>
                </div>
                <button
                  onClick={resetForm}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors rounded-lg hover:bg-gray-100"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              {/* Abas de Tipo de Objetivo */}
              {!editingGoal && (
                <div className="flex mt-4 bg-white rounded-lg p-1 shadow-sm">
                  <button
                    type="button"
                    onClick={() => {
                      setGoalType('simple')
                      setFormData({ ...formData, type: 'simple' })
                    }}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      goalType === 'simple'
                        ? 'bg-blue-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <Target className="h-4 w-4" />
                      Objetivo Simples
                    </div>
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      setGoalType('complex')
                      setFormData({ ...formData, type: 'complex' })
                    }}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                      goalType === 'complex'
                        ? 'bg-purple-500 text-white shadow-sm'
                        : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-center gap-2">
                      <div className="flex">
                        <Target className="h-4 w-4" />
                        <Target className="h-4 w-4 -ml-2" />
                      </div>
                      Objetivo Complexo
                    </div>
                  </button>
                </div>
              )}
            </div>

            {/* Conteúdo do Modal */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Formulário para Objetivo Simples */}
                {goalType === 'simple' && (
                  <>
                    {/* Nome */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nome do Objetivo *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Ex: Viagem para Europa"
                        required
                      />
                    </div>

                    {/* Descrição */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Descrição
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Descrição opcional do objetivo..."
                        rows="3"
                      />
                    </div>

                    {/* Valor e Data */}
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Valor *
                        </label>
                        <CurrencyInput
                          value={formData.targetAmount}
                          onChange={(value) => setFormData({ ...formData, targetAmount: value })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                          placeholder="R$ 0,00"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Ano *
                        </label>
                        <select
                          value={formData.targetYear}
                          onChange={(e) => setFormData({ ...formData, targetYear: parseInt(e.target.value) })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          {Array.from({ length: 10 }, (_, i) => {
                            const year = new Date().getFullYear() + i
                            return (
                              <option key={year} value={year}>{year}</option>
                            )
                          })}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          Mês *
                        </label>
                        <select
                          value={formData.targetMonth}
                          onChange={(e) => setFormData({ ...formData, targetMonth: parseInt(e.target.value) })}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                          {Array.from({ length: 12 }, (_, i) => (
                            <option key={i + 1} value={i + 1}>
                              {getMonthName(i + 1)}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>
                  </>
                )}

                {/* Formulário para Objetivo Complexo */}
                {goalType === 'complex' && (
                  <>
                    {/* Nome do Objetivo Complexo */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Nome do Objetivo Complexo *
                      </label>
                      <input
                        type="text"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="Ex: Comprar Casa Própria"
                        required
                      />
                    </div>

                    {/* Descrição */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Descrição
                      </label>
                      <textarea
                        value={formData.description}
                        onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                        placeholder="Descrição do objetivo complexo..."
                        rows="3"
                      />
                    </div>

                    {/* Lista de Itens do Objetivo Complexo */}
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <label className="block text-sm font-medium text-gray-700">
                          Itens do Objetivo *
                        </label>
                        <button
                          type="button"
                          onClick={() => setShowComplexItemModal(true)}
                          className="flex items-center gap-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors text-sm"
                        >
                          <Plus className="h-4 w-4" />
                          Adicionar Item
                        </button>
                      </div>

                      {complexItems.length === 0 ? (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                          <p className="text-gray-600 mb-4">Nenhum item adicionado ainda</p>
                          <button
                            type="button"
                            onClick={() => setShowComplexItemModal(true)}
                            className="inline-flex items-center gap-2 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                          >
                            <Plus className="h-4 w-4" />
                            Adicionar Primeiro Item
                          </button>
                        </div>
                      ) : (
                        <div className="space-y-3 max-h-64 overflow-y-auto border border-gray-200 rounded-lg p-4">
                          {complexItems.map((item, index) => (
                            <div key={index} className={`flex items-center justify-between p-3 rounded-lg transition-colors ${
                              item.isCompleted ? 'bg-green-50 border border-green-200' : 'bg-gray-50'
                            }`}>
                              <div className="flex items-center gap-3 flex-1">
                                {/* Checkbox para marcar como concluído */}
                                <input
                                  type="checkbox"
                                  checked={item.isCompleted || false}
                                  onChange={(e) => {
                                    const newItems = [...complexItems]
                                    newItems[index] = { ...newItems[index], isCompleted: e.target.checked }
                                    setComplexItems(newItems)
                                    setFormData({ ...formData, items: newItems })
                                  }}
                                  className="w-5 h-5 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2"
                                />

                                <div className="flex-1">
                                  <h4 className={`font-medium ${item.isCompleted ? 'text-green-800 line-through' : 'text-gray-900'}`}>
                                    {item.name}
                                  </h4>
                                  <p className={`text-sm ${item.isCompleted ? 'text-green-600' : 'text-gray-600'}`}>
                                    {formatCurrency(item.targetAmount)} - {getMonthName(item.targetMonth)}/{item.targetYear}
                                  </p>
                                  {item.description && (
                                    <p className={`text-xs mt-1 ${item.isCompleted ? 'text-green-500' : 'text-gray-500'}`}>
                                      {item.description}
                                    </p>
                                  )}
                                  {item.isCompleted && (
                                    <p className="text-xs text-green-600 font-medium mt-1">
                                      ✓ Concluído
                                    </p>
                                  )}
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <button
                                  type="button"
                                  onClick={() => {
                                    setEditingComplexItem(index)
                                    setShowComplexItemModal(true)
                                  }}
                                  className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                                >
                                  <Edit3 className="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  onClick={() => {
                                    const newItems = complexItems.filter((_, i) => i !== index)
                                    setComplexItems(newItems)
                                    setFormData({ ...formData, items: newItems })
                                  }}
                                  className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </>
                )}

                {/* Seções Comuns para ambos os tipos */}

                {/* Upload de Imagem */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Imagem do Objetivo
                  </label>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                      <Upload className="h-4 w-4" />
                      Escolher Imagem
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                      />
                    </label>
                    {imagePreview && (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedImage(null)
                            setImagePreview(null)
                          }}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Categorias e Tags apenas para objetivos simples */}
                {goalType === 'simple' && (
                  <>
                    {/* Categorias */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Categorias Relacionadas
                      </label>
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3">
                        {categories.map((category) => (
                          <label key={category.id} className="flex items-center gap-2 cursor-pointer">
                            <input
                              type="checkbox"
                              checked={formData.categoryIds.includes(category.id)}
                              onChange={(e) => {
                                if (e.target.checked) {
                                  setFormData({
                                    ...formData,
                                    categoryIds: [...formData.categoryIds, category.id]
                                  })
                                } else {
                                  setFormData({
                                    ...formData,
                                    categoryIds: formData.categoryIds.filter(id => id !== category.id)
                                  })
                                }
                              }}
                              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                            />
                            <span className="text-sm">{category.name}</span>
                          </label>
                        ))}
                      </div>
                    </div>

                    {/* Tags */}
                    <div>
                      <TagSelector
                        tags={tags}
                        selectedTags={formData.tagIds}
                        onTagToggle={(tagId) => {
                          if (formData.tagIds.includes(tagId)) {
                            setFormData({
                              ...formData,
                              tagIds: formData.tagIds.filter(id => id !== tagId)
                            })
                          } else {
                            setFormData({
                              ...formData,
                              tagIds: [...formData.tagIds, tagId]
                            })
                          }
                        }}
                        label="Tags Relacionadas"
                      />
                    </div>
                  </>
                )}

                {/* Botões */}
                <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingGoal ? 'Atualizar' : 'Criar'} Objetivo
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal para Itens de Objetivo Complexo */}
      {showComplexItemModal && (
        <ComplexItemModal
          isOpen={showComplexItemModal}
          onClose={() => {
            setShowComplexItemModal(false)
            setEditingComplexItem(null)
          }}
          onSave={(item) => {
            if (editingComplexItem !== null) {
              // Editando item existente
              const newItems = [...complexItems]
              newItems[editingComplexItem] = item
              setComplexItems(newItems)
              setFormData({ ...formData, items: newItems })
            } else {
              // Adicionando novo item
              const newItems = [...complexItems, item]
              setComplexItems(newItems)
              setFormData({ ...formData, items: newItems })
            }
            setShowComplexItemModal(false)
            setEditingComplexItem(null)
          }}
          editingItem={editingComplexItem !== null ? complexItems[editingComplexItem] : null}
        />
      )}

      {/* Modal de Projeção */}
      <GoalProjectionModal
        goal={selectedGoal}
        isOpen={showProjectionModal}
        onClose={() => {
          setShowProjectionModal(false)
          setSelectedGoal(null)
        }}
      />

      {/* Modal de Conclusão de Objetivo Simples */}
      {showCompleteModal && goalToComplete && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-6 shadow-2xl">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Concluir Objetivo
              </h3>
              <p className="text-gray-600">
                {goalToComplete.name}
              </p>
            </div>

            <div className="mb-6">
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Valor planejado:</span>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(goalToComplete.targetAmount)}
                  </span>
                </div>
              </div>

              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor realmente gasto *
              </label>
              <CurrencyInput
                value={completionValue}
                onChange={setCompletionValue}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                placeholder="R$ 0,00"
              />
              <p className="text-xs text-gray-500 mt-2">
                Informe o valor que você realmente gastou para alcançar este objetivo
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowCompleteModal(false)
                  setGoalToComplete(null)
                  setCompletionValue(0)
                }}
                className="flex-1 px-4 py-3 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={confirmCompleteGoal}
                className="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-300 font-semibold"
              >
                Confirmar Conclusão
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// Componente Modal para Itens de Objetivo Complexo
const ComplexItemModal = ({ isOpen, onClose, onSave, editingItem }) => {
  const [itemData, setItemData] = useState({
    name: '',
    description: '',
    targetAmount: 0,
    targetYear: new Date().getFullYear(),
    targetMonth: new Date().getMonth() + 1
  })

  useEffect(() => {
    if (editingItem) {
      setItemData(editingItem)
    } else {
      setItemData({
        name: '',
        description: '',
        targetAmount: 0,
        targetYear: new Date().getFullYear(),
        targetMonth: new Date().getMonth() + 1
      })
    }
  }, [editingItem, isOpen])

  const getMonthName = (month) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ]
    return months[month - 1]
  }

  const handleSubmit = (e) => {
    e.preventDefault()

    if (!itemData.name || !itemData.targetAmount) {
      toast.error('Nome e valor são obrigatórios')
      return
    }

    onSave(itemData)
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-semibold text-gray-900">
              {editingItem ? 'Editar Item' : 'Novo Item do Objetivo'}
            </h3>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Nome */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Nome do Item *
              </label>
              <input
                type="text"
                value={itemData.name}
                onChange={(e) => setItemData({ ...itemData, name: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Ex: Entrada do apartamento"
                required
              />
            </div>

            {/* Descrição */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Descrição
              </label>
              <textarea
                value={itemData.description}
                onChange={(e) => setItemData({ ...itemData, description: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                placeholder="Descrição opcional do item..."
                rows="3"
              />
            </div>

            {/* Valor e Data */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor *
                </label>
                <CurrencyInput
                  value={itemData.targetAmount}
                  onChange={(value) => setItemData({ ...itemData, targetAmount: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Ano *
                </label>
                <select
                  value={itemData.targetYear}
                  onChange={(e) => setItemData({ ...itemData, targetYear: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {Array.from({ length: 10 }, (_, i) => {
                    const year = new Date().getFullYear() + i
                    return (
                      <option key={year} value={year}>{year}</option>
                    )
                  })}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Mês *
                </label>
                <select
                  value={itemData.targetMonth}
                  onChange={(e) => setItemData({ ...itemData, targetMonth: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                >
                  {Array.from({ length: 12 }, (_, i) => (
                    <option key={i + 1} value={i + 1}>
                      {getMonthName(i + 1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            {/* Botões */}
            <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                type="submit"
                className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
              >
                {editingItem ? 'Atualizar' : 'Adicionar'} Item
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}

export default GoalManager
