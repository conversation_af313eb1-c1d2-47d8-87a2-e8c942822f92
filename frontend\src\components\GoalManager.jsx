import React, { useState, useEffect } from 'react'
import { 
  Target, 
  Plus, 
  Edit3, 
  Trash2, 
  Calendar, 
  DollarSign,
  Upload,
  X,
  Eye,
  TrendingUp
} from 'lucide-react'
import goalService from '../services/goalService'
import categoryService from '../services/categoryService'
import tagService from '../services/tagService'
import CurrencyInput from './CurrencyInput'
import TagSelector from './TagSelector'
import GoalProjectionModal from './GoalProjectionModal'
import toast from 'react-hot-toast'

const GoalManager = () => {
  const [goals, setGoals] = useState([])
  const [categories, setCategories] = useState([])
  const [tags, setTags] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingGoal, setEditingGoal] = useState(null)
  const [selectedImage, setSelectedImage] = useState(null)
  const [imagePreview, setImagePreview] = useState(null)
  const [selectedGoal, setSelectedGoal] = useState(null)
  const [showProjectionModal, setShowProjectionModal] = useState(false)

  const [formData, setFormData] = useState({
    name: '',
    description: '',
    targetAmount: 0,
    targetYear: new Date().getFullYear(),
    targetMonth: new Date().getMonth() + 1,
    categoryIds: [],
    tagIds: []
  })

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [goalsData, categoriesData, tagsData] = await Promise.all([
        goalService.getGoals(),
        categoryService.getCategories(),
        tagService.getTags()
      ])
      
      setGoals(goalsData)
      setCategories(categoriesData)
      setTags(tagsData)
    } catch (error) {
      console.error('Erro ao carregar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!formData.name || !formData.targetAmount) {
      toast.error('Nome e valor são obrigatórios')
      return
    }

    try {
      const goalData = {
        ...formData,
        image: selectedImage
      }

      if (editingGoal) {
        await goalService.updateGoal(editingGoal.id, goalData)
        toast.success('Objetivo atualizado com sucesso!')
      } else {
        await goalService.createGoal(goalData)
        toast.success('Objetivo criado com sucesso!')
      }

      resetForm()
      fetchData()
    } catch (error) {
      console.error('Erro ao salvar objetivo:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar objetivo')
    }
  }

  const handleEdit = (goal) => {
    setEditingGoal(goal)
    setFormData({
      name: goal.name,
      description: goal.description || '',
      targetAmount: goal.targetAmount,
      targetYear: goal.targetYear,
      targetMonth: goal.targetMonth,
      categoryIds: goal.categories.map(c => c.categoryId),
      tagIds: goal.tags.map(t => t.tagId)
    })
    setImagePreview(goal.imageUrl)
    setShowModal(true)
  }

  const handleDelete = async (goal) => {
    if (!confirm(`Tem certeza que deseja excluir o objetivo "${goal.name}"?`)) {
      return
    }

    try {
      await goalService.deleteGoal(goal.id)
      toast.success('Objetivo excluído com sucesso!')
      fetchData()
    } catch (error) {
      console.error('Erro ao excluir objetivo:', error)
      toast.error('Erro ao excluir objetivo')
    }
  }

  const handleImageChange = (e) => {
    const file = e.target.files[0]
    if (file) {
      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (e) => setImagePreview(e.target.result)
      reader.readAsDataURL(file)
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      targetAmount: 0,
      targetYear: new Date().getFullYear(),
      targetMonth: new Date().getMonth() + 1,
      categoryIds: [],
      tagIds: []
    })
    setEditingGoal(null)
    setSelectedImage(null)
    setImagePreview(null)
    setShowModal(false)
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getMonthName = (month) => {
    const months = [
      'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
      'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
    ]
    return months[month - 1]
  }

  const handleViewProjection = async (goal) => {
    setSelectedGoal(goal)
    setShowProjectionModal(true)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
            <Target className="h-6 w-6 text-blue-600" />
            Objetivos Financeiros
          </h2>
          <p className="text-gray-600 mt-1">Defina e acompanhe suas metas financeiras</p>
        </div>
        <button
          onClick={() => setShowModal(true)}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Objetivo
        </button>
      </div>

      {/* Lista de Objetivos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {goals.map((goal) => (
          <div key={goal.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden hover:shadow-lg transition-shadow">
            {/* Imagem */}
            {goal.imageUrl && (
              <div className="h-48 bg-gray-100">
                <img 
                  src={goal.imageUrl} 
                  alt={goal.name}
                  className="w-full h-full object-cover"
                />
              </div>
            )}
            
            {/* Conteúdo */}
            <div className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 text-lg mb-1">{goal.name}</h3>
                  {goal.description && (
                    <p className="text-gray-600 text-sm">{goal.description}</p>
                  )}
                </div>
                <div className="flex items-center gap-1 ml-2">
                  <button
                    onClick={() => handleViewProjection(goal)}
                    className="p-2 text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                    title="Ver Projeção"
                  >
                    <TrendingUp className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleEdit(goal)}
                    className="p-2 text-gray-600 hover:bg-gray-50 rounded-lg transition-colors"
                    title="Editar"
                  >
                    <Edit3 className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(goal)}
                    className="p-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
                    title="Excluir"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Valor e Data */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-600">
                    {formatCurrency(goal.targetAmount)}
                  </span>
                </div>
                
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-blue-600" />
                  <span className="text-gray-600">
                    {getMonthName(goal.targetMonth)} de {goal.targetYear}
                  </span>
                </div>
              </div>

              {/* Categorias e Tags */}
              {(goal.categories.length > 0 || goal.tags.length > 0) && (
                <div className="mt-4 pt-4 border-t border-gray-100">
                  {goal.categories.length > 0 && (
                    <div className="flex flex-wrap gap-1 mb-2">
                      {goal.categories.map((mapping) => (
                        <span
                          key={mapping.id}
                          className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-700"
                        >
                          {mapping.category.name}
                        </span>
                      ))}
                    </div>
                  )}
                  
                  {goal.tags.length > 0 && (
                    <div className="flex flex-wrap gap-1">
                      {goal.tags.map((mapping) => (
                        <span
                          key={mapping.id}
                          className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-700"
                        >
                          {mapping.tag.name}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {goals.length === 0 && (
        <div className="text-center py-12">
          <Target className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum objetivo cadastrado</h3>
          <p className="text-gray-600 mb-4">Comece definindo seus objetivos financeiros</p>
          <button
            onClick={() => setShowModal(true)}
            className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Criar Primeiro Objetivo
          </button>
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-gray-900">
                  {editingGoal ? 'Editar Objetivo' : 'Novo Objetivo'}
                </h3>
                <button
                  onClick={resetForm}
                  className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>

              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Nome */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Nome do Objetivo *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Ex: Viagem para Europa"
                    required
                  />
                </div>

                {/* Descrição */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Descrição
                  </label>
                  <textarea
                    value={formData.description}
                    onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Descrição opcional do objetivo..."
                    rows="3"
                  />
                </div>

                {/* Valor e Data */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Valor *
                    </label>
                    <CurrencyInput
                      value={formData.targetAmount}
                      onChange={(value) => setFormData({ ...formData, targetAmount: value })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="R$ 0,00"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Ano *
                    </label>
                    <select
                      value={formData.targetYear}
                      onChange={(e) => setFormData({ ...formData, targetYear: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {Array.from({ length: 10 }, (_, i) => {
                        const year = new Date().getFullYear() + i
                        return (
                          <option key={year} value={year}>{year}</option>
                        )
                      })}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Mês *
                    </label>
                    <select
                      value={formData.targetMonth}
                      onChange={(e) => setFormData({ ...formData, targetMonth: parseInt(e.target.value) })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      {Array.from({ length: 12 }, (_, i) => (
                        <option key={i + 1} value={i + 1}>
                          {getMonthName(i + 1)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>

                {/* Upload de Imagem */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Imagem do Objetivo
                  </label>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50 transition-colors">
                      <Upload className="h-4 w-4" />
                      Escolher Imagem
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleImageChange}
                        className="hidden"
                      />
                    </label>
                    {imagePreview && (
                      <div className="relative">
                        <img
                          src={imagePreview}
                          alt="Preview"
                          className="w-16 h-16 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            setSelectedImage(null)
                            setImagePreview(null)
                          }}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {/* Categorias */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categorias Relacionadas
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3">
                    {categories.map((category) => (
                      <label key={category.id} className="flex items-center gap-2 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={formData.categoryIds.includes(category.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setFormData({
                                ...formData,
                                categoryIds: [...formData.categoryIds, category.id]
                              })
                            } else {
                              setFormData({
                                ...formData,
                                categoryIds: formData.categoryIds.filter(id => id !== category.id)
                              })
                            }
                          }}
                          className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        />
                        <span className="text-sm">{category.name}</span>
                      </label>
                    ))}
                  </div>
                </div>

                {/* Tags */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags Relacionadas
                  </label>
                  <TagSelector
                    tags={tags}
                    selectedTags={formData.tagIds}
                    onChange={(selectedTagIds) => setFormData({ ...formData, tagIds: selectedTagIds })}
                  />
                </div>

                {/* Botões */}
                <div className="flex justify-end gap-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    onClick={resetForm}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                  >
                    Cancelar
                  </button>
                  <button
                    type="submit"
                    className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    {editingGoal ? 'Atualizar' : 'Criar'} Objetivo
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Projeção */}
      <GoalProjectionModal
        goal={selectedGoal}
        isOpen={showProjectionModal}
        onClose={() => {
          setShowProjectionModal(false)
          setSelectedGoal(null)
        }}
      />
    </div>
  )
}

export default GoalManager
