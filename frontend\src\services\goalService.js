import api from './api'

const goalService = {
  // Listar todos os objetivos
  async getGoals() {
    const response = await api.get('/goals')
    return response.data
  },

  // Buscar objetivo específico
  async getGoal(id) {
    const response = await api.get(`/goals/${id}`)
    return response.data
  },

  // Criar novo objetivo
  async createGoal(goalData) {
    const formData = new FormData()

    // Adicionar campos básicos
    formData.append('name', goalData.name)
    if (goalData.description) formData.append('description', goalData.description)

    // Adicionar tipo de objetivo
    formData.append('type', goalData.type || 'simple')

    // Para objetivos complexos
    if (goalData.type === 'complex') {
      if (goalData.items) {
        formData.append('items', goalData.items)
      }
      // Objetivos complexos não precisam de targetAmount, targetYear, targetMonth
      // Esses valores serão calculados automaticamente no backend
    } else {
      // Para objetivos simples
      formData.append('targetAmount', goalData.targetAmount.toString())
      formData.append('targetYear', goalData.targetYear.toString())
      formData.append('targetMonth', goalData.targetMonth.toString())
    }

    // Adicionar categorias e tags
    if (goalData.categoryIds && goalData.categoryIds.length > 0) {
      formData.append('categoryIds', JSON.stringify(goalData.categoryIds))
    }
    if (goalData.tagIds && goalData.tagIds.length > 0) {
      formData.append('tagIds', JSON.stringify(goalData.tagIds))
    }

    // Adicionar imagem se existir
    if (goalData.image) {
      formData.append('image', goalData.image)
    }

    const response = await api.post('/goals', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  // Atualizar objetivo
  async updateGoal(id, goalData) {
    const formData = new FormData()
    
    // Adicionar campos básicos
    if (goalData.name) formData.append('name', goalData.name)
    if (goalData.description !== undefined) formData.append('description', goalData.description)
    if (goalData.targetAmount) formData.append('targetAmount', goalData.targetAmount.toString())
    if (goalData.targetYear) formData.append('targetYear', goalData.targetYear.toString())
    if (goalData.targetMonth) formData.append('targetMonth', goalData.targetMonth.toString())
    
    // Adicionar categorias e tags
    if (goalData.categoryIds !== undefined) {
      formData.append('categoryIds', JSON.stringify(goalData.categoryIds))
    }
    if (goalData.tagIds !== undefined) {
      formData.append('tagIds', JSON.stringify(goalData.tagIds))
    }
    
    // Adicionar imagem se existir
    if (goalData.image) {
      formData.append('image', goalData.image)
    }

    const response = await api.put(`/goals/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  },

  // Deletar objetivo
  async deleteGoal(id) {
    const response = await api.delete(`/goals/${id}`)
    return response.data
  },

  // Buscar projeção de objetivo
  async getGoalProjection(id, year = null, month = null) {
    const params = new URLSearchParams()
    if (year) params.append('year', year)
    if (month) params.append('month', month)

    const url = `/goals/${id}/projection${params.toString() ? `?${params.toString()}` : ''}`
    const response = await api.get(url)
    return response.data
  },

  // Marcar objetivo como concluído
  async completeGoal(id, completedAmount) {
    const response = await api.patch(`/goals/${id}/complete`, {
      completedAmount
    })
    return response.data
  },

  // Desmarcar objetivo como concluído
  async uncompleteGoal(id) {
    const response = await api.patch(`/goals/${id}/uncomplete`)
    return response.data
  },

  // Marcar item de objetivo complexo como concluído
  async completeComplexGoalItem(goalId, itemId, data) {
    const response = await api.patch(`/goals/${goalId}/items/${itemId}/complete`, data)
    return response.data
  },

  // Excluir item de objetivo complexo
  async deleteComplexGoalItem(goalId, itemId) {
    const response = await api.delete(`/goals/${goalId}/items/${itemId}`)
    return response.data
  },

  // Atualizar item de objetivo complexo
  async updateComplexGoalItem(goalId, itemId, data) {
    const response = await api.put(`/goals/${goalId}/items/${itemId}`, data)
    return response.data
  }
}

export default goalService
