import api from './api'

export const piggyBankService = {
  // Listar todos os cofrinhos
  async getPiggyBanks() {
    const response = await api.get('/savings')
    return response.data
  },

  // Criar cofrinho
  async createPiggyBank(piggyBankData) {
    const response = await api.post('/savings', piggyBankData)
    return response.data
  },

  // Atualizar cofrinho
  async updatePiggyBank(id, piggyBankData) {
    const response = await api.put(`/savings/${id}`, piggyBankData)
    return response.data
  },

  // Deletar cofrinho
  async deletePiggyBank(id) {
    const response = await api.delete(`/savings/${id}`)
    return response.data
  },

  // Depositar valor no cofrinho (com banco de origem)
  async depositAmount(id, amount, sourceBankId) {
    const response = await api.post(`/savings/${id}/deposit`, {
      amount,
      sourceBankId
    })
    return response.data
  },

  // Retirar valor do cofrinho (com banco de destino)
  async withdrawAmount(id, amount, targetBankId) {
    const response = await api.post(`/savings/${id}/withdraw`, {
      amount,
      targetBankId
    })
    return response.data
  },

  // Adicionar valor ao cofrinho (método antigo - compatibilidade)
  async addAmount(id, amount) {
    const response = await api.post(`/savings/${id}/add`, { amount })
    return response.data
  },

  // Atualizar CDI de todos os cofrinhos
  async updateCdi(cdiRate) {
    const response = await api.post('/savings/update-cdi', { cdiRate })
    return response.data
  },

  // Aplicar rendimento CDI
  async applyCdi() {
    const response = await api.post('/savings/apply-cdi')
    return response.data
  }
}
