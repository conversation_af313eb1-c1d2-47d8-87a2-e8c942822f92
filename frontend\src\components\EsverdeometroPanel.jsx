import React, { useState, useEffect } from 'react'
import { 
  TrendingUp, 
  TrendingDown, 
  PiggyBank, 
  Coffee, 
  Eye,
  ChevronDown,
  ChevronUp,
  Calendar,
  DollarSign
} from 'lucide-react'
import api from '../services/api'
import toast from 'react-hot-toast'

const EsverdeometroPanel = ({ year, month }) => {
  const [data, setData] = useState({
    income: null,
    expenses: null,
    savings: null,
    leisure: null
  })
  const [loading, setLoading] = useState(true)
  const [expandedCard, setExpandedCard] = useState(null)

  useEffect(() => {
    if (year && month) {
      fetchAllData()
    }
  }, [year, month])

  const fetchAllData = async () => {
    try {
      setLoading(true)
      
      const [incomeRes, expensesRes, savingsRes, leisureRes] = await Promise.all([
        api.get(`/reports/income/${year}/${month}`),
        api.get(`/reports/expenses/${year}/${month}`).catch(e => ({ data: null, error: e })),
        api.get(`/reports/savings/${year}/${month}`).catch(e => ({ data: null, error: e })),
        api.get(`/reports/leisure/${year}/${month}`).catch(e => ({ data: null, error: e }))
      ])

      setData({
        income: incomeRes.data,
        expenses: expensesRes.data,
        savings: savingsRes.data,
        leisure: leisureRes.data
      })

    } catch (error) {
      console.error('Erro ao buscar dados do esverdeômetro:', error)
      toast.error('Erro ao carregar dados do painel')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getCardColor = (current, target, isIncome = false) => {
    if (isIncome) {
      return 'blue' // Renda sempre azul
    }

    if (!target || target === 0) return 'gray'

    const difference = Math.abs(current - target)
    const threshold = target * 0.1 // 10% do valor alvo

    if (current <= target) {
      return 'green' // Dentro do limite
    } else if (difference <= threshold) {
      return 'yellow' // Faltando 10% para ultrapassar
    } else {
      return 'red' // Ultrapassou o limite
    }
  }

  const getColorClasses = (color) => {
    const colors = {
      blue: {
        bg: 'bg-blue-50',
        border: 'border-blue-200',
        text: 'text-blue-800',
        icon: 'text-blue-600',
        button: 'bg-blue-100 hover:bg-blue-200 text-blue-700'
      },
      green: {
        bg: 'bg-green-50',
        border: 'border-green-200',
        text: 'text-green-800',
        icon: 'text-green-600',
        button: 'bg-green-100 hover:bg-green-200 text-green-700'
      },
      yellow: {
        bg: 'bg-yellow-50',
        border: 'border-yellow-200',
        text: 'text-yellow-800',
        icon: 'text-yellow-600',
        button: 'bg-yellow-100 hover:bg-yellow-200 text-yellow-700'
      },
      red: {
        bg: 'bg-red-50',
        border: 'border-red-200',
        text: 'text-red-800',
        icon: 'text-red-600',
        button: 'bg-red-100 hover:bg-red-200 text-red-700'
      },
      gray: {
        bg: 'bg-gray-50',
        border: 'border-gray-200',
        text: 'text-gray-800',
        icon: 'text-gray-600',
        button: 'bg-gray-100 hover:bg-gray-200 text-gray-700'
      }
    }
    return colors[color] || colors.gray
  }

  const renderCard = (type, title, icon, value, percentage, target, data, hasBreakdown = false) => {
    const color = getCardColor(percentage, target, type === 'income')
    const colorClasses = getColorClasses(color)
    const isExpanded = expandedCard === type

    return (
      <div className={`rounded-lg border-2 ${colorClasses.bg} ${colorClasses.border} transition-all duration-300`}>
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <div className={`w-10 h-10 rounded-lg ${colorClasses.button} flex items-center justify-center`}>
                {icon}
              </div>
              <div>
                <h3 className={`font-semibold ${colorClasses.text}`}>{title}</h3>
                {type !== 'income' && target && (
                  <p className="text-xs opacity-75">Meta: {target.toFixed(1)}%</p>
                )}
              </div>
            </div>
            
            {hasBreakdown && data && data.breakdown && (
              <button
                onClick={() => setExpandedCard(isExpanded ? null : type)}
                className={`p-2 rounded-lg ${colorClasses.button} transition-colors`}
              >
                <Eye className="h-4 w-4" />
              </button>
            )}
          </div>

          {/* Valor Principal */}
          <div className="mb-4">
            <div className={`text-2xl font-bold ${colorClasses.text}`}>
              {formatCurrency(value)}
            </div>
            {type !== 'income' && (
              <div className={`text-sm ${colorClasses.text} opacity-75`}>
                {percentage.toFixed(1)}% da renda
              </div>
            )}
          </div>

          {/* Indicador de Status */}
          {type !== 'income' && target && (
            <div className="flex items-center gap-2 text-xs">
              <div className={`w-2 h-2 rounded-full ${
                color === 'green' ? 'bg-green-500' :
                color === 'yellow' ? 'bg-yellow-500' :
                color === 'red' ? 'bg-red-500' : 'bg-gray-500'
              }`}></div>
              <span className={colorClasses.text}>
                {color === 'green' ? 'Dentro do limite' :
                 color === 'yellow' ? 'Próximo do limite' :
                 color === 'red' ? 'Acima do limite' : 'Sem meta'}
              </span>
            </div>
          )}
        </div>

        {/* Detalhamento Expandido */}
        {isExpanded && data && data.breakdown && (
          <div className="border-t border-current border-opacity-20 p-4">
            <div className="space-y-4">
              {/* Por Categoria */}
              {Object.keys(data.breakdown.byCategory).length > 0 && (
                <div>
                  <h5 className={`font-medium ${colorClasses.text} mb-2 text-sm`}>Por Categoria</h5>
                  <div className="space-y-2">
                    {Object.entries(data.breakdown.byCategory).map(([name, info]) => (
                      <div key={name} className="flex items-center justify-between text-xs">
                        <span className={colorClasses.text}>{name}</span>
                        <div className="text-right">
                          <div className={`font-medium ${colorClasses.text}`}>
                            {formatCurrency(info.total)}
                          </div>
                          <div className={`${colorClasses.text} opacity-75`}>
                            {info.count} transação{info.count !== 1 ? 'ões' : ''}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Por Tag */}
              {Object.keys(data.breakdown.byTag).length > 0 && (
                <div>
                  <h5 className={`font-medium ${colorClasses.text} mb-2 text-sm`}>Por Tag</h5>
                  <div className="space-y-2">
                    {Object.entries(data.breakdown.byTag).map(([name, info]) => (
                      <div key={name} className="flex items-center justify-between text-xs">
                        <span className={colorClasses.text}>{name}</span>
                        <div className="text-right">
                          <div className={`font-medium ${colorClasses.text}`}>
                            {formatCurrency(info.total)}
                          </div>
                          <div className={`${colorClasses.text} opacity-75`}>
                            {info.count} transação{info.count !== 1 ? 'ões' : ''}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Por Contato */}
              {Object.keys(data.breakdown.byContact).length > 0 && (
                <div>
                  <h5 className={`font-medium ${colorClasses.text} mb-2 text-sm`}>Por Contato</h5>
                  <div className="space-y-2">
                    {Object.entries(data.breakdown.byContact).map(([name, info]) => (
                      <div key={name} className="flex items-center justify-between text-xs">
                        <span className={colorClasses.text}>{name}</span>
                        <div className="text-right">
                          <div className={`font-medium ${colorClasses.text}`}>
                            {formatCurrency(info.total)}
                          </div>
                          <div className={`${colorClasses.text} opacity-75`}>
                            {info.count} transação{info.count !== 1 ? 'ões' : ''}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {[1, 2, 3, 4].map(i => (
          <div key={i} className="bg-gray-100 rounded-lg p-6 animate-pulse">
            <div className="h-4 bg-gray-300 rounded mb-4"></div>
            <div className="h-8 bg-gray-300 rounded mb-2"></div>
            <div className="h-3 bg-gray-300 rounded"></div>
          </div>
        ))}
      </div>
    )
  }

  const monthName = new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2 mb-6">
        <Calendar className="h-5 w-5 text-blue-600" />
        <h2 className="text-xl font-bold text-gray-900">
          Esverdeômetro - {monthName}
        </h2>
      </div>

      {/* Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Renda */}
        {renderCard(
          'income',
          'Renda Mensal',
          <TrendingUp className="h-5 w-5" />,
          data.income?.totalIncome || 0,
          0,
          null,
          data.income,
          false
        )}

        {/* Despesas Essenciais */}
        {renderCard(
          'expenses',
          'Despesas Essenciais',
          <TrendingDown className="h-5 w-5" />,
          data.expenses?.totalExpenses || 0,
          data.expenses?.percentage || 0,
          data.expenses?.targetPercentage,
          data.expenses,
          true
        )}

        {/* Poupança/Investimentos */}
        {renderCard(
          'savings',
          'Poupança/Investimentos',
          <PiggyBank className="h-5 w-5" />,
          data.savings?.totalSavings || 0,
          data.savings?.percentage || 0,
          data.savings?.targetPercentage,
          data.savings,
          true
        )}

        {/* Lazer */}
        {renderCard(
          'leisure',
          'Lazer',
          <Coffee className="h-5 w-5" />,
          data.leisure?.totalLeisure || 0,
          data.leisure?.percentage || 0,
          data.leisure?.targetPercentage,
          data.leisure,
          true
        )}
      </div>

      {/* Resumo Geral */}
      {data.income && (data.expenses || data.savings || data.leisure) && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="font-semibold text-gray-900 mb-4">Resumo Geral</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Total Categorizado:</span>
              <div className="font-medium">
                {formatCurrency(
                  (data.expenses?.totalExpenses || 0) + 
                  (data.savings?.totalSavings || 0) + 
                  (data.leisure?.totalLeisure || 0)
                )}
              </div>
            </div>
            <div>
              <span className="text-gray-600">Renda Restante:</span>
              <div className="font-medium">
                {formatCurrency(
                  (data.income?.totalIncome || 0) - 
                  (data.expenses?.totalExpenses || 0) - 
                  (data.savings?.totalSavings || 0) - 
                  (data.leisure?.totalLeisure || 0)
                )}
              </div>
            </div>
            <div>
              <span className="text-gray-600">% Categorizado:</span>
              <div className="font-medium">
                {data.income?.totalIncome > 0 ? 
                  (((data.expenses?.totalExpenses || 0) + 
                    (data.savings?.totalSavings || 0) + 
                    (data.leisure?.totalLeisure || 0)) / data.income.totalIncome * 100).toFixed(1)
                  : 0
                }%
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default EsverdeometroPanel
