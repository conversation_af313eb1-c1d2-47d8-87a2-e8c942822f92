import React, { useState, useEffect } from 'react'
import { Plus, Edit, Trash2, Copy, ExternalLink, Tag as TagIcon, Play, Pause, Clock, Upload, FileText, Calendar, DollarSign } from 'lucide-react'
import { transactionTemplateService } from '../services/transactionTemplateService'
import { categoryService } from '../services/categoryService'
import { bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import TagSelector from './TagSelector'
import api from '../services/api'
import toast from 'react-hot-toast'

function TemplateManager({ onTemplateExecuted }) {
  const [templates, setTemplates] = useState([])
  const [categories, setCategories] = useState([])
  const [banks, setBanks] = useState([])
  const [tags, setTags] = useState([])
  const [contacts, setContacts] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingTemplate, setEditingTemplate] = useState(null)
  const [showUseModal, setShowUseModal] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [transactionData, setTransactionData] = useState({
    amount: 0,
    description: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5)
  })
  const [selectedFile, setSelectedFile] = useState(null)
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    type: 'EXPENSE',
    categoryId: '',
    bankId: '',
    transactionContactId: '',
    tags: []
  })
  const [selectedTags, setSelectedTags] = useState([])

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      console.log('🔄 Carregando dados para TemplateManager...')
      const [templatesData, categoriesData, banksData, tagsData, contactsData] = await Promise.all([
        transactionTemplateService.getTemplates(),
        categoryService.getCategories(),
        bankService.getBanks(),
        api.get('/tags').then(res => res.data),
        api.get('/contacts-tx').then(res => res.data)
      ])
      console.log('📊 Dados carregados:', {
        templates: templatesData?.length || 0,
        categories: categoriesData?.length || 0,
        banks: banksData?.length || 0,
        tags: tagsData?.length || 0,
        contacts: contactsData?.length || 0
      })
      setTemplates(templatesData)
      setCategories(categoriesData)
      setBanks(banksData)
      setTags(tagsData)
      setContacts(contactsData)
    } catch (error) {
      console.error('❌ Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      const templateData = {
        ...newTemplate,
        tags: selectedTags
      }

      if (editingTemplate) {
        await transactionTemplateService.updateTemplate(editingTemplate.id, templateData)
        toast.success('Template atualizado com sucesso!')
      } else {
        await transactionTemplateService.createTemplate(templateData)
        toast.success('Template criado com sucesso!')
      }
      setShowModal(false)
      setEditingTemplate(null)
      setNewTemplate({
        name: '',
        description: '',
        type: 'EXPENSE',
        categoryId: '',
        bankId: '',
        transactionContactId: '',
        tags: []
      })
      setSelectedTags([])
      fetchData()
    } catch (error) {
      toast.error('Erro ao salvar template')
    }
  }

  const handleEdit = (template) => {
    setEditingTemplate(template)
    setNewTemplate({
      name: template.name,
      description: template.description || '',
      type: template.type,
      categoryId: template.categoryId,
      bankId: template.bankId,
      transactionContactId: template.transactionContactId || '',
      tags: template.tags || []
    })
    setSelectedTags(template.tags?.map(tag => tag.id) || [])
    setShowModal(true)
  }

  const handleTagToggle = (tagId) => {
    setSelectedTags(prev =>
      prev.includes(tagId)
        ? prev.filter(id => id !== tagId)
        : [...prev, tagId]
    )
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja excluir este template?')) {
      try {
        await transactionTemplateService.deleteTemplate(id)
        toast.success('Template excluído com sucesso!')
        fetchData()
      } catch (error) {
        toast.error('Erro ao excluir template')
      }
    }
  }

  const handleUseTemplate = (template) => {
    setSelectedTemplate(template)
    setTransactionData({
      amount: 0,
      description: '',
      date: new Date().toISOString().split('T')[0]
    })
    setShowUseModal(true)
  }

  const handleCreateTransaction = async () => {
    try {
      if (!transactionData.amount || transactionData.amount <= 0) {
        toast.error('Valor é obrigatório e deve ser maior que zero')
        return
      }

      // Criar FormData para enviar arquivo
      const formData = new FormData()
      formData.append('amount', transactionData.amount)
      formData.append('description', transactionData.description)
      formData.append('date', transactionData.date)
      formData.append('time', transactionData.time)

      if (selectedFile) {
        formData.append('receipt', selectedFile)
      }

      await transactionTemplateService.useTemplate(selectedTemplate.id, formData)
      toast.success('Transação criada com sucesso!')
      setShowUseModal(false)
      setSelectedTemplate(null)
      setTransactionData({
        amount: 0,
        description: '',
        date: new Date().toISOString().split('T')[0],
        time: new Date().toTimeString().slice(0, 5)
      })
      setSelectedFile(null)
    } catch (error) {
      console.error('Erro ao criar transação:', error)

      // Chamar callback para recarregar transações na página principal
      if (onTemplateExecuted) {
        onTemplateExecuted()
      }
      toast.error('Erro ao criar transação')
    }
  }

  const handleToggleActive = async (template) => {
    try {
      await transactionTemplateService.updateTemplate(template.id, {
        isActive: !template.isActive
      })
      toast.success(`Template ${template.isActive ? 'desativado' : 'ativado'} com sucesso!`)
      fetchData()
    } catch (error) {
      toast.error('Erro ao alterar status do template')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Templates de Transações</h2>
          <p className="text-gray-600 mt-1">
            Crie templates para transações recorrentes e acelere seu cadastro
          </p>
        </div>
        <button
          onClick={() => {
            setEditingTemplate(null)
            setNewTemplate({
              name: '',
              description: '',
              type: 'EXPENSE',
              categoryId: '',
              bankId: '',
              transactionContactId: '',
              tags: []
            })
            setSelectedTags([])
            setShowModal(true)
          }}
          className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4" />
          Novo Template
        </button>
      </div>

      {/* Lista de Templates */}
      {templates.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <Copy className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum template encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Crie seu primeiro template para acelerar o cadastro de transações
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Criar Template
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div
              key={template.id}
              className={`bg-gradient-to-br from-white to-gray-50 rounded-2xl border-2 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02] ${
                template.isActive
                  ? template.type === 'INCOME'
                    ? 'border-green-200 hover:border-green-300'
                    : 'border-blue-200 hover:border-blue-300'
                  : 'border-gray-200 hover:border-gray-300 opacity-70'
              }`}
            >
              {/* Header do Card */}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`w-3 h-3 rounded-full ${
                        template.isActive
                          ? template.type === 'INCOME' ? 'bg-green-500' : 'bg-blue-500'
                          : 'bg-gray-400'
                      }`}></div>
                      <h3 className="text-xl font-bold text-gray-900">
                        {template.name}
                      </h3>
                    </div>
                    <div className="flex items-center gap-2 mb-3">
                      <span className={`px-3 py-1 text-xs font-semibold rounded-full border ${
                        template.type === 'INCOME'
                          ? 'bg-green-100 text-green-700 border-green-200'
                          : 'bg-blue-100 text-blue-700 border-blue-200'
                      }`}>
                        {template.type === 'INCOME' ? '💰 Receita' : '💸 Despesa'}
                      </span>
                      <span className={`px-3 py-1 text-xs font-medium rounded-full border ${
                        template.isActive
                          ? 'bg-green-100 text-green-700 border-green-200'
                          : 'bg-gray-100 text-gray-600 border-gray-200'
                      }`}>
                        {template.isActive ? '🟢 Ativo' : '⚫ Inativo'}
                      </span>
                    </div>
                    {template.description && (
                      <p className="text-sm text-gray-600 mb-4 leading-relaxed">
                        {template.description}
                      </p>
                    )}
                  </div>
                </div>

                {/* Detalhes do Template */}
                <div className="bg-white rounded-xl p-4 border border-gray-100 mb-4">
                  <h4 className="text-sm font-semibold text-gray-700 mb-3 flex items-center gap-2">
                    📋 Detalhes
                  </h4>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-600">Categoria:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{template.category?.icon}</span>
                        <span className="text-sm font-medium text-gray-900">{template.category?.name}</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                      <span className="text-sm text-gray-600">Banco:</span>
                      <div className="flex items-center gap-2">
                        <span className="text-lg">{template.bank?.icon}</span>
                        <span className="text-sm font-medium text-gray-900">{template.bank?.name}</span>
                      </div>
                    </div>
                    {template.transactionContact && (
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">Contato:</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium text-gray-900">{template.transactionContact.name}</span>
                          {template.transactionContact.description && (
                            <span className="text-xs text-gray-500">({template.transactionContact.description})</span>
                          )}
                        </div>
                      </div>
                    )}
                    {template.tags && (
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded-lg">
                        <span className="text-sm text-gray-600">Tags:</span>
                        <div className="flex items-center gap-1">
                          <TagIcon className="h-3 w-3 text-gray-500" />
                          <span className="text-sm font-medium text-gray-900">{template.tags}</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Ações */}
              <div className="px-6 pb-6">
                <div className="flex items-center gap-2">
                  <button
                    onClick={() => handleUseTemplate(template)}
                    disabled={!template.isActive}
                    className={`flex-1 flex items-center justify-center gap-2 px-4 py-3 rounded-xl font-semibold transition-all duration-200 ${
                      template.isActive
                        ? template.type === 'INCOME'
                          ? 'bg-gradient-to-r from-green-500 to-green-600 text-white hover:from-green-600 hover:to-green-700 shadow-lg hover:shadow-xl'
                          : 'bg-gradient-to-r from-blue-500 to-blue-600 text-white hover:from-blue-600 hover:to-blue-700 shadow-lg hover:shadow-xl'
                        : 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    }`}
                    title="Usar template para criar transação"
                  >
                    <Copy className="h-4 w-4" />
                    Usar Template
                  </button>

                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleToggleActive(template)}
                      className={`p-3 rounded-xl transition-all duration-200 ${
                        template.isActive
                          ? 'bg-orange-100 text-orange-600 hover:bg-orange-200'
                          : 'bg-green-100 text-green-600 hover:bg-green-200'
                      }`}
                      title={template.isActive ? 'Desativar template' : 'Ativar template'}
                    >
                      {template.isActive ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
                    </button>

                    <button
                      onClick={() => handleEdit(template)}
                      className="p-3 bg-blue-100 text-blue-600 hover:bg-blue-200 rounded-xl transition-all duration-200"
                      title="Editar template"
                    >
                      <Edit className="h-4 w-4" />
                    </button>

                    <button
                      onClick={() => handleDelete(template.id)}
                      className="p-3 bg-red-100 text-red-600 hover:bg-red-200 rounded-xl transition-all duration-200"
                      title="Excluir template"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {editingTemplate ? 'Editar Template' : 'Novo Template'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Nome */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Template *
                </label>
                <input
                  type="text"
                  value={newTemplate.name}
                  onChange={(e) => setNewTemplate({ ...newTemplate, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Pizza da Sexta"
                  required
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={newTemplate.description}
                  onChange={(e) => setNewTemplate({ ...newTemplate, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição opcional"
                  rows="2"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Tipo */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tipo *
                  </label>
                  <select
                    value={newTemplate.type}
                    onChange={(e) => setNewTemplate({ ...newTemplate, type: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="EXPENSE">Despesa</option>
                    <option value="INCOME">Receita</option>
                  </select>
                </div>

                {/* Categoria */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Categoria *
                  </label>
                  <select
                    value={newTemplate.categoryId}
                    onChange={(e) => setNewTemplate({ ...newTemplate, categoryId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione uma categoria</option>
                    {categories.map((category) => (
                      <option key={category.id} value={category.id}>
                        {category.icon} {category.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Banco */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Banco *
                </label>
                <select
                  value={newTemplate.bankId}
                  onChange={(e) => setNewTemplate({ ...newTemplate, bankId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="">Selecione um banco</option>
                  {banks.map((bank) => (
                    <option key={bank.id} value={bank.id}>
                      {bank.icon} {bank.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Contato */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contato (Opcional)
                </label>
                <select
                  value={newTemplate.transactionContactId}
                  onChange={(e) => setNewTemplate({ ...newTemplate, transactionContactId: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Selecione um contato</option>
                  {contacts.map((contact) => (
                    <option key={contact.id} value={contact.id}>
                      {contact.name} {contact.description && `- ${contact.description}`}
                    </option>
                  ))}
                </select>
                <p className="mt-1 text-xs text-gray-500">
                  💡 Identifique quem enviou (receita) ou recebeu (despesa/investimento) esta transação
                </p>
              </div>

              {/* Tags */}
              <TagSelector
                tags={tags}
                selectedTags={selectedTags}
                onTagToggle={handleTagToggle}
                label="Tags"
              />

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingTemplate ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Usar Template */}
      {showUseModal && selectedTemplate && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                Usar Template: {selectedTemplate.name}
              </h3>
              <p className="text-gray-600 mt-1">
                Preencha os dados para criar a transação
              </p>
            </div>

            <div className="p-6 space-y-4">
              {/* Informações do Template */}
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center gap-3 mb-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    selectedTemplate.type === 'INCOME'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-blue-100 text-blue-800'
                  }`}>
                    {selectedTemplate.type === 'INCOME' ? '💰 Receita' : '💸 Despesa'}
                  </span>
                </div>
                <div className="text-sm text-gray-600 space-y-1">
                  <div>📂 {selectedTemplate.category?.name}</div>
                  <div>🏦 {selectedTemplate.bank?.name}</div>
                </div>
              </div>

              {/* Valor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor *
                </label>
                <CurrencyInput
                  value={transactionData.amount}
                  onChange={(value) => setTransactionData({ ...transactionData, amount: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              {/* Descrição */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <input
                  type="text"
                  value={transactionData.description}
                  onChange={(e) => setTransactionData({ ...transactionData, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Descrição da transação"
                />
              </div>

              {/* Data e Horário */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Calendar className="inline h-4 w-4 mr-1" />
                    Data
                  </label>
                  <input
                    type="date"
                    value={transactionData.date}
                    onChange={(e) => setTransactionData({ ...transactionData, date: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <Clock className="inline h-4 w-4 mr-1" />
                    Horário
                  </label>
                  <input
                    type="time"
                    value={transactionData.time}
                    onChange={(e) => setTransactionData({ ...transactionData, time: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              {/* Upload de Comprovante */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <Upload className="inline h-4 w-4 mr-1" />
                  Comprovante (opcional)
                </label>
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-lg hover:border-gray-400 transition-colors">
                  <div className="space-y-1 text-center">
                    {selectedFile ? (
                      <div className="flex items-center justify-center space-x-2">
                        <FileText className="h-8 w-8 text-green-500" />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{selectedFile.name}</p>
                          <p className="text-xs text-gray-500">{(selectedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                          <button
                            type="button"
                            onClick={() => setSelectedFile(null)}
                            className="text-red-600 hover:text-red-800 text-xs"
                          >
                            Remover
                          </button>
                        </div>
                      </div>
                    ) : (
                      <>
                        <Upload className="mx-auto h-12 w-12 text-gray-400" />
                        <div className="flex text-sm text-gray-600">
                          <label className="relative cursor-pointer bg-white rounded-md font-medium text-blue-600 hover:text-blue-500">
                            <span>Clique para enviar</span>
                            <input
                              type="file"
                              className="sr-only"
                              accept="image/*,.pdf"
                              onChange={(e) => setSelectedFile(e.target.files[0])}
                            />
                          </label>
                          <p className="pl-1">ou arraste e solte</p>
                        </div>
                        <p className="text-xs text-gray-500">PNG, JPG, PDF até 10MB</p>
                      </>
                    )}
                  </div>
                </div>
              </div>

              {/* Link de Pagamento */}
              {selectedTemplate.paymentLink && (
                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-blue-700">Link de Pagamento:</span>
                    <a
                      href={selectedTemplate.paymentLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 flex items-center gap-1 text-sm font-medium"
                    >
                      <ExternalLink className="h-3 w-3" />
                      Acessar
                    </a>
                  </div>
                </div>
              )}
            </div>

            <div className="p-6 border-t border-gray-200 flex justify-end gap-3">
              <button
                onClick={() => {
                  setShowUseModal(false)
                  setSelectedTemplate(null)
                  setTransactionData({
                    amount: 0,
                    description: '',
                    date: new Date().toISOString().split('T')[0]
                  })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateTransaction}
                className={`px-6 py-2 text-white rounded-lg transition-colors ${
                  selectedTemplate.type === 'INCOME'
                    ? 'bg-green-600 hover:bg-green-700'
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                Criar Transação
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default TemplateManager
