import React, { useState, useEffect } from 'react';
import { Calendar, Clock, RotateCcw, FastForward, Rewind } from 'lucide-react';

const DateSimulator = () => {
  const [dateInfo, setDateInfo] = useState(null);
  const [selectedDate, setSelectedDate] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchDateInfo();
  }, []);

  const fetchDateInfo = async () => {
    try {
      const response = await fetch('/api/date-simulator/info', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      const data = await response.json();
      setDateInfo(data);
      
      // Formatar data para input
      if (data.currentDate) {
        const date = new Date(data.currentDate);
        const formatted = date.toISOString().split('T')[0];
        setSelectedDate(formatted);
      }
    } catch (error) {
      console.error('Erro ao buscar informações da data:', error);
    }
  };

  const setSimulatedDate = async (date) => {
    setLoading(true);
    try {
      const response = await fetch('/api/date-simulator/set', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({ date })
      });
      
      if (response.ok) {
        await fetchDateInfo();
        alert('Data simulada definida com sucesso!');
      } else {
        alert('Erro ao definir data simulada');
      }
    } catch (error) {
      console.error('Erro ao definir data simulada:', error);
      alert('Erro ao definir data simulada');
    } finally {
      setLoading(false);
    }
  };

  const clearSimulatedDate = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/date-simulator/clear', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        await fetchDateInfo();
        alert('Data simulada removida! Usando data real.');
      } else {
        alert('Erro ao remover data simulada');
      }
    } catch (error) {
      console.error('Erro ao remover data simulada:', error);
      alert('Erro ao remover data simulada');
    } finally {
      setLoading(false);
    }
  };

  const quickAction = async (action) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/date-simulator/quick-test/${action}`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });
      
      if (response.ok) {
        await fetchDateInfo();
        alert(`Data alterada: ${action}`);
      } else {
        alert('Erro ao alterar data');
      }
    } catch (error) {
      console.error('Erro ao alterar data:', error);
      alert('Erro ao alterar data');
    } finally {
      setLoading(false);
    }
  };

  const handleDateChange = (e) => {
    setSelectedDate(e.target.value);
  };

  const handleSetDate = () => {
    if (selectedDate) {
      setSimulatedDate(selectedDate);
    }
  };

  if (!dateInfo) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
      <div className="flex items-center gap-3 mb-6">
        <div className="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
          <Calendar className="h-5 w-5 text-blue-600" />
        </div>
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Simulador de Data</h3>
          <p className="text-sm text-gray-600">Para testes do sistema de faturamento</p>
        </div>
      </div>

      {/* Status Atual */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-2 mb-2">
          <Clock className="h-4 w-4 text-gray-600" />
          <span className="text-sm font-medium text-gray-700">Status Atual</span>
        </div>
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Data do Sistema:</span>
            <div className="font-medium text-gray-900">{dateInfo.currentDateString}</div>
          </div>
          <div>
            <span className="text-gray-600">Data Real:</span>
            <div className="font-medium text-gray-900">{dateInfo.realDateString}</div>
          </div>
        </div>
        {dateInfo.isSimulated && (
          <div className="mt-2 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-md inline-block">
            Usando data simulada
          </div>
        )}
      </div>

      {/* Definir Data */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Definir Data Específica
        </label>
        <div className="flex gap-2">
          <input
            type="date"
            value={selectedDate}
            onChange={handleDateChange}
            className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <button
            onClick={handleSetDate}
            disabled={loading || !selectedDate}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {loading ? 'Definindo...' : 'Definir'}
          </button>
        </div>
      </div>

      {/* Ações Rápidas */}
      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Ações Rápidas
        </label>
        <div className="grid grid-cols-2 gap-2">
          <button
            onClick={() => quickAction('tomorrow')}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-green-50 text-green-700 rounded-lg hover:bg-green-100 disabled:opacity-50"
          >
            <FastForward className="h-4 w-4" />
            Amanhã
          </button>
          <button
            onClick={() => quickAction('yesterday')}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-orange-50 text-orange-700 rounded-lg hover:bg-orange-100 disabled:opacity-50"
          >
            <Rewind className="h-4 w-4" />
            Ontem
          </button>
          <button
            onClick={() => quickAction('next-week')}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-lg hover:bg-blue-100 disabled:opacity-50"
          >
            <FastForward className="h-4 w-4" />
            +7 dias
          </button>
          <button
            onClick={() => quickAction('next-month')}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-purple-50 text-purple-700 rounded-lg hover:bg-purple-100 disabled:opacity-50"
          >
            <FastForward className="h-4 w-4" />
            +1 mês
          </button>
        </div>
      </div>

      {/* Resetar */}
      <div className="pt-4 border-t border-gray-200">
        <button
          onClick={clearSimulatedDate}
          disabled={loading || !dateInfo.isSimulated}
          className="flex items-center gap-2 px-4 py-2 text-sm bg-gray-50 text-gray-700 rounded-lg hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <RotateCcw className="h-4 w-4" />
          Usar Data Real
        </button>
      </div>
    </div>
  );
};

export default DateSimulator;
