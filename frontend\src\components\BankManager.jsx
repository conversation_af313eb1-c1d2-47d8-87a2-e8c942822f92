import React, { useState, useEffect } from 'react'
import {
  Plus,
  Edit3,
  Trash2,
  Eye,
  EyeOff,
  ArrowRightLeft,
  Wallet,
  DollarSign,
  Calendar
} from 'lucide-react'
import { bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import { getBankIcon, getBankColor, availableBankIcons } from '../utils/bankIcons'
import toast from 'react-hot-toast'

function BankManager() {
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showBalances, setShowBalances] = useState(true)
  const [showAddModal, setShowAddModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [showTransferModal, setShowTransferModal] = useState(false)
  const [editingBank, setEditingBank] = useState(null)
  const [totalBalance, setTotalBalance] = useState(0)

  const [newBank, setNewBank] = useState({
    name: '',
    icon: '🏦',
    color: '#3B82F6',
    initialBalance: 0,
    billDueDay: null,
    creditLimit: 0,
    isLimitLocked: false
  })

  const bankColors = [
    '#3B82F6', '#22C55E', '#F59E0B', '#EF4444',
    '#8B5CF6', '#EC4899', '#14B8A6', '#6B7280'
  ]

  useEffect(() => {
    fetchBanks()
    fetchTotalBalance()
  }, [])

  const fetchBanks = async () => {
    try {
      const data = await bankService.getBanks()
      setBanks(data)
    } catch (error) {
      toast.error('Erro ao carregar bancos')
    } finally {
      setLoading(false)
    }
  }

  const fetchTotalBalance = async () => {
    try {
      const data = await bankService.getTotalBalance()
      setTotalBalance(data.totalBalance)
    } catch (error) {
      console.error('Erro ao carregar saldo total:', error)
    }
  }

  const handleCreateBank = async () => {
    try {
      if (!newBank.name) {
        toast.error('Nome do banco é obrigatório')
        return
      }

      // Auto-detectar ícone e cor se não foram alterados manualmente
      const bankData = {
        ...newBank,
        icon: newBank.icon === '🏦' ? getBankIcon(newBank.name) : newBank.icon,
        color: newBank.color === '#3B82F6' ? getBankColor(newBank.name) : newBank.color
      }

      await bankService.createBank(bankData)
      toast.success('Banco criado com sucesso!')
      setShowAddModal(false)
      setNewBank({ name: '', icon: '🏦', color: '#3B82F6', initialBalance: 0, billDueDay: null, creditLimit: 0, isLimitLocked: false })
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error('Erro ao criar banco')
    }
  }

  const handleUpdateBank = async () => {
    try {
      await bankService.updateBank(editingBank.id, editingBank)
      toast.success('Banco atualizado com sucesso!')
      setShowEditModal(false)
      setEditingBank(null)
      fetchBanks()
    } catch (error) {
      toast.error('Erro ao atualizar banco')
    }
  }

  const handleDeleteBank = async (bank) => {
    if (!confirm(`Tem certeza que deseja excluir o banco "${bank.name}"?`)) {
      return
    }

    try {
      await bankService.deleteBank(bank.id)
      toast.success('Banco excluído com sucesso!')
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error(error.response?.data?.error || 'Erro ao excluir banco')
    }
  }

  const toggleBankVisibility = async (bank) => {
    try {
      await bankService.updateBank(bank.id, { isVisible: !bank.isVisible })
      fetchBanks()
      fetchTotalBalance()
    } catch (error) {
      toast.error('Erro ao atualizar visibilidade')
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  // Função para auto-detectar ícone e cor quando o nome mudar
  const handleBankNameChange = (name) => {
    const detectedIcon = getBankIcon(name)
    const detectedColor = getBankColor(name)

    setNewBank(prev => ({
      ...prev,
      name,
      // Só atualiza se ainda estiver com valores padrão
      icon: prev.icon === '🏦' ? detectedIcon : prev.icon,
      color: prev.color === '#3B82F6' ? detectedColor : prev.color
    }))
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Bancos e Contas</h1>
          <p className="text-gray-600 mt-1">Gerencie suas contas e acompanhe seus saldos</p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowBalances(!showBalances)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
          >
            {showBalances ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            <span>{showBalances ? 'Ocultar' : 'Mostrar'} Saldos</span>
          </button>
          <button
            onClick={() => setShowTransferModal(true)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-600 rounded-lg hover:bg-slate-700 transition-colors"
          >
            <ArrowRightLeft className="h-4 w-4" />
            <span>Transferir</span>
          </button>
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-white bg-slate-900 rounded-lg hover:bg-slate-800 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Novo Banco</span>
          </button>
        </div>
      </div>

      {/* Saldo Total */}
      <div className="bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl p-8 text-white shadow-xl">
        <div className="flex items-center justify-between">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <DollarSign className="h-5 w-5 text-slate-300" />
              <p className="text-slate-300 text-sm font-medium">Saldo Total Disponível</p>
            </div>
            <p className="text-4xl font-bold tracking-tight">
              {showBalances ? formatCurrency(totalBalance) : '••••••••'}
            </p>
            <p className="text-slate-400 text-sm mt-1">
              {banks.filter(b => b.isVisible).length} conta{banks.filter(b => b.isVisible).length !== 1 ? 's' : ''} ativa{banks.filter(b => b.isVisible).length !== 1 ? 's' : ''}
            </p>
          </div>
          <div className="bg-slate-700 bg-opacity-50 p-4 rounded-2xl">
            <Wallet className="h-10 w-10 text-slate-300" />
          </div>
        </div>
      </div>

      {/* Lista de Bancos */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
        {banks.map((bank) => (
          <div
            key={bank.id}
            className={`bg-white rounded-2xl border shadow-sm p-6 transition-all hover:shadow-md ${
              bank.isVisible
                ? 'border-gray-200 hover:border-gray-300'
                : 'border-gray-100 opacity-60 bg-gray-50'
            }`}
          >
            <div className="flex items-start justify-between mb-6">
              <div className="flex items-center gap-4">
                <div
                  className="w-14 h-14 rounded-2xl flex items-center justify-center text-2xl shadow-sm"
                  style={{ backgroundColor: bank.color + '15', color: bank.color, border: `2px solid ${bank.color}20` }}
                >
                  {bank.icon}
                </div>
                <div>
                  <h3 className="font-bold text-gray-900 text-lg">{bank.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <div className={`w-2 h-2 rounded-full ${bank.isVisible ? 'bg-green-500' : 'bg-gray-400'}`} />
                    <p className="text-sm text-gray-500">
                      {bank.isVisible ? 'Ativo' : 'Oculto'}
                    </p>
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-1">
                <button
                  onClick={() => toggleBankVisibility(bank)}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title={bank.isVisible ? 'Ocultar' : 'Mostrar'}
                >
                  {bank.isVisible ? <Eye className="h-4 w-4 text-gray-600" /> : <EyeOff className="h-4 w-4 text-gray-400" />}
                </button>
                <button
                  onClick={() => {
                    setEditingBank(bank)
                    setShowEditModal(true)
                  }}
                  className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                  title="Editar"
                >
                  <Edit3 className="h-4 w-4 text-gray-600" />
                </button>
                <button
                  onClick={() => handleDeleteBank(bank)}
                  className="p-2 hover:bg-red-50 rounded-lg text-red-600 transition-colors"
                  title="Excluir"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            </div>

            <div className="space-y-4">
              {bank.isVisible ? (
                <>
                  <div className="bg-gray-50 rounded-xl p-4">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-600">Saldo Atual</span>
                      <span className="text-xl font-bold text-gray-900">
                        {showBalances ? formatCurrency(bank.currentBalance) : '••••••••'}
                      </span>
                    </div>
                    {bank.initialBalance !== bank.currentBalance && (
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-gray-500">Saldo Inicial</span>
                        <span className="text-gray-600 font-medium">
                          {showBalances ? formatCurrency(bank.initialBalance) : '••••••'}
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">Variação</span>
                    <span className={`font-medium ${
                      bank.currentBalance >= bank.initialBalance ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {showBalances ? (
                        bank.currentBalance >= bank.initialBalance ? '+' : ''
                      ) + formatCurrency(bank.currentBalance - bank.initialBalance) : '••••••'}
                    </span>
                  </div>

                  {/* Informações de Crédito */}
                  {bank.creditLimit > 0 && (
                    <div className="bg-blue-50 rounded-xl p-4 mt-4">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-blue-700">Limite de Crédito</span>
                        <span className="text-lg font-bold text-blue-900">
                          {showBalances ? formatCurrency(bank.creditLimit) : '••••••••'}
                        </span>
                      </div>
                      <div className="flex justify-between items-center text-sm">
                        <span className="text-blue-600">Disponível</span>
                        <span className="text-blue-800 font-medium">
                          {showBalances ? formatCurrency(bank.availableLimit || bank.creditLimit) : '••••••'}
                        </span>
                      </div>
                      {bank.isLimitLocked && (
                        <div className="mt-2 text-xs text-orange-600 font-medium">
                          🔒 Limite travado
                        </div>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="bg-gray-100 rounded-xl p-4 text-center">
                  <p className="text-gray-500 text-sm">Banco oculto</p>
                  <p className="text-xs text-gray-400 mt-1">Clique no ícone do olho para mostrar</p>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {banks.length === 0 && (
        <div className="text-center py-12">
          <Wallet className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Nenhum banco cadastrado</h3>
          <p className="text-gray-600 mb-4">Comece criando sua primeira conta bancária</p>
          <button
            onClick={() => setShowAddModal(true)}
            className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            <span>Criar Primeiro Banco</span>
          </button>
        </div>
      )}

      {/* Modal Adicionar Banco */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Novo Banco</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome do Banco</label>
                <input
                  type="text"
                  value={newBank.name}
                  onChange={(e) => handleBankNameChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: Nubank, Itaú, Bradesco..."
                />
                <p className="text-xs text-gray-500 mt-1">
                  Ícone e cor serão detectados automaticamente para bancos conhecidos
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableBankIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setNewBank({ ...newBank, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        newBank.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Ícone detectado automaticamente: {getBankIcon(newBank.name)}
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {bankColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setNewBank({ ...newBank, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        newBank.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Saldo Inicial</label>
                <CurrencyInput
                  value={newBank.initialBalance}
                  onChange={(value) => setNewBank({ ...newBank, initialBalance: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Dia de Fechamento da Fatura (Opcional)
                  </div>
                </label>
                <select
                  value={newBank.billDueDay || ''}
                  onChange={(e) => setNewBank({ ...newBank, billDueDay: e.target.value ? parseInt(e.target.value) : null })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Não se aplica</option>
                  {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                    <option key={day} value={day}>Dia {day}</option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Para cartões de crédito, defina o dia de fechamento da fatura
                </p>
              </div>

              {/* Limite de Crédito */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Limite de Crédito (Opcional)
                </label>
                <CurrencyInput
                  value={newBank.creditLimit}
                  onChange={(value) => setNewBank({ ...newBank, creditLimit: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Para cartões de crédito, defina o limite total disponível
                </p>
              </div>

              {/* Travar Limite */}
              {newBank.creditLimit > 0 && (
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="isLimitLocked"
                    checked={newBank.isLimitLocked}
                    onChange={(e) => setNewBank({ ...newBank, isLimitLocked: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="isLimitLocked" className="text-sm text-gray-700">
                    Travar limite (não permitir usar todo o limite)
                  </label>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowAddModal(false)
                  setNewBank({ name: '', icon: '🏦', color: '#3B82F6', initialBalance: 0, billDueDay: null, creditLimit: 0, isLimitLocked: false })
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleCreateBank}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Criar Banco
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Editar Banco */}
      {showEditModal && editingBank && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Editar Banco</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Nome</label>
                <input
                  type="text"
                  value={editingBank.name}
                  onChange={(e) => setEditingBank({ ...editingBank, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Ícone</label>
                <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                  {availableBankIcons.map((icon) => (
                    <button
                      key={icon}
                      onClick={() => setEditingBank({ ...editingBank, icon })}
                      className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                        editingBank.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                      }`}
                    >
                      {icon}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Cor</label>
                <div className="flex flex-wrap gap-2">
                  {bankColors.map((color) => (
                    <button
                      key={color}
                      onClick={() => setEditingBank({ ...editingBank, color })}
                      className={`w-8 h-8 rounded-full border-2 transition-all ${
                        editingBank.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                      }`}
                      style={{ backgroundColor: color }}
                    />
                  ))}
                </div>
              </div>

              {/* Saldo Atual */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Saldo Atual
                  </div>
                </label>
                <CurrencyInput
                  value={editingBank.currentBalance || 0}
                  onChange={(value) => setEditingBank({ ...editingBank, currentBalance: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Ajuste o saldo atual do banco conforme necessário
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Dia de Fechamento da Fatura (Opcional)
                  </div>
                </label>
                <select
                  value={editingBank.billDueDay || ''}
                  onChange={(e) => setEditingBank({ ...editingBank, billDueDay: e.target.value ? parseInt(e.target.value) : null })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Não se aplica</option>
                  {Array.from({ length: 31 }, (_, i) => i + 1).map(day => (
                    <option key={day} value={day}>Dia {day}</option>
                  ))}
                </select>
                <p className="text-xs text-gray-500 mt-1">
                  Para cartões de crédito, defina o dia de fechamento da fatura
                </p>
              </div>

              {/* Limite de Crédito */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Limite de Crédito (Opcional)
                </label>
                <CurrencyInput
                  value={editingBank.creditLimit || 0}
                  onChange={(value) => setEditingBank({ ...editingBank, creditLimit: value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="R$ 0,00"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Para cartões de crédito, defina o limite total disponível
                </p>
              </div>

              {/* Limite Disponível */}
              {editingBank.creditLimit > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Limite Disponível Atual: {formatCurrency(editingBank.availableLimit || 0)}
                  </label>
                  <div className="space-y-2">
                    <input
                      type="range"
                      min="0"
                      max={editingBank.creditLimit}
                      step="50"
                      value={editingBank.availableLimit || 0}
                      onChange={(e) => setEditingBank({ ...editingBank, availableLimit: parseFloat(e.target.value) })}
                      className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                    />
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>R$ 0</span>
                      <span>{formatCurrency(editingBank.creditLimit)}</span>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Use o slider para ajustar o limite disponível
                  </p>
                </div>
              )}

              {/* Travar Limite */}
              {editingBank.creditLimit > 0 && (
                <div className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    id="editIsLimitLocked"
                    checked={editingBank.isLimitLocked || false}
                    onChange={(e) => setEditingBank({ ...editingBank, isLimitLocked: e.target.checked })}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <label htmlFor="editIsLimitLocked" className="text-sm text-gray-700">
                    Travar limite (não permitir usar todo o limite)
                  </label>
                </div>
              )}
            </div>

            <div className="flex justify-end space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(false)
                  setEditingBank(null)
                }}
                className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleUpdateBank}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                Salvar
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default BankManager
