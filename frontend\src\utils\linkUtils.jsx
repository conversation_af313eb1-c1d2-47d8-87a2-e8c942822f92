import React from 'react'

// Função para detectar URLs em texto
const detectUrls = (text) => {
  const urlRegex = /(https?:\/\/[^\s]+)/g
  return text.split(urlRegex)
}

// Componente para renderizar texto com links clicáveis
export const RenderTextWithLinks = ({ text, className = "" }) => {
  if (!text) return null

  const parts = detectUrls(text)
  
  return (
    <span className={className}>
      {parts.map((part, index) => {
        // Verifica se a parte é uma URL
        if (part.match(/^https?:\/\//)) {
          return (
            <a
              key={index}
              href={part}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-600 hover:text-blue-800 underline break-all"
              onClick={(e) => e.stopPropagation()} // Evita que cliques no link ativem outros eventos
            >
              {part}
            </a>
          )
        }
        return part
      })}
    </span>
  )
}

export default RenderTextWithLinks
