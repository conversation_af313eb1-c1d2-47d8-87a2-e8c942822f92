# ⚡ SARA - Quick Start Guide

## 🚀 Deploy em 5 Minutos

### 1. Pré-requisitos
```bash
# Instalar Docker (Ubuntu/Debian)
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Instalar Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. Clonar e Configurar
```bash
# Clonar repositório
git clone https://github.com/seu-usuario/sara.git
cd sara

# Configurar ambiente
cp .env.production.example .env
nano .env  # Configure as variáveis obrigatórias
```

### 3. Deploy Automático
```bash
# Tornar script executável
chmod +x deploy.sh

# Executar deploy
./deploy.sh
```

### 4. Acessar Sistema
- **URL**: http://localhost
- **Login**: <EMAIL>
- **Senha**: 123456

## 🔧 Configurações Mínimas

### Arquivo .env
```env
# OBRIGATÓRIO - Gere uma chave segura
JWT_SECRET=sua_chave_jwt_super_segura_aqui

# OBRIGATÓRIO - Configure sua conta Cloudinary
CLOUDINARY_CLOUD_NAME=seu_cloud_name
CLOUDINARY_API_KEY=sua_api_key
CLOUDINARY_API_SECRET=seu_api_secret

# Para auto-deploy
GIT_REPO=https://github.com/seu-usuario/sara.git
GIT_BRANCH=main
AUTO_DEPLOY=true

# Para SSL (opcional)
DOMAIN=sara.seudominio.com
ACME_EMAIL=<EMAIL>
```

## 📋 Comandos Essenciais

```bash
# Status da aplicação
docker-compose ps

# Logs em tempo real
docker-compose logs -f sara-app

# Reiniciar aplicação
docker-compose restart sara-app

# Parar aplicação
docker-compose down

# Backup manual
docker-compose exec sara-app cp /app/backend/production.db /app/logs/backup_$(date +%Y%m%d_%H%M%S).db

# Forçar novo deploy
docker-compose exec sara-app rm -f /tmp/git-cache/last_commit
```

## 🌐 Opções de Deploy

### Deploy Básico (HTTP)
```bash
./deploy.sh basic
```

### Deploy com SSL (HTTPS)
```bash
./deploy.sh ssl
```

### Deploy Completo (SSL + Backup)
```bash
./deploy.sh deploy
```

## 🔄 Auto-Deploy

O sistema monitora automaticamente seu repositório Git e faz deploy das mudanças:

1. **Push** código para a branch configurada
2. **Aguarde** até 5 minutos (configurável)
3. **Deploy** automático será executado
4. **Verificação** de saúde automática

## 🛡️ Segurança

### Primeiros Passos
1. **Altere a senha padrão** após primeiro login
2. **Configure JWT_SECRET** seguro
3. **Use HTTPS** em produção
4. **Configure firewall** adequadamente

### Firewall Básico
```bash
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 💾 Backup

### Automático
- **Frequência**: 24 horas
- **Retenção**: 7 dias
- **Local**: `data/backups/`

### Manual
```bash
# Backup imediato
docker-compose exec sara-app cp /app/backend/production.db /app/logs/backup_manual_$(date +%Y%m%d_%H%M%S).db

# Listar backups
ls -la data/backups/
```

## 🚨 Troubleshooting

### Aplicação não inicia
```bash
# Verificar logs
docker-compose logs sara-app

# Verificar configuração
docker-compose config

# Reiniciar do zero
docker-compose down
docker-compose up -d --force-recreate
```

### Auto-deploy não funciona
```bash
# Verificar logs
docker-compose logs sara-app | grep auto-deploy

# Verificar configuração Git
docker-compose exec sara-app git ls-remote $GIT_REPO $GIT_BRANCH

# Forçar deploy
docker-compose exec sara-app rm -f /tmp/git-cache/last_commit
```

### SSL não funciona
```bash
# Verificar DNS
nslookup $DOMAIN

# Verificar Traefik
docker-compose logs traefik

# Aguardar propagação DNS (pode levar até 24h)
```

## 📞 Suporte Rápido

### Informações do Sistema
```bash
# Versões
docker --version
docker-compose --version

# Status completo
docker-compose ps
docker stats

# Logs de erro
docker-compose logs sara-app | grep -i error
```

### Reset Completo
```bash
# CUIDADO: Remove todos os dados!
docker-compose down -v
docker system prune -af
rm -rf data/
./deploy.sh
```

## 🎯 Próximos Passos

1. **Alterar senha padrão**
2. **Configurar categorias e tags**
3. **Adicionar bancos e cartões**
4. **Importar transações existentes**
5. **Configurar objetivos financeiros**
6. **Configurar backup externo**

---

## 🎉 Pronto!

Seu sistema SARA está rodando! 

- 📊 **Dashboard**: Visão geral das finanças
- 💰 **Transações**: Controle completo
- 🏦 **Bancos**: Múltiplas contas
- 📈 **Relatórios**: Análises detalhadas
- 🎯 **Objetivos**: Metas financeiras
- 💸 **Empréstimos**: Controle de dívidas

**Lembre-se**: Altere a senha padrão no primeiro acesso!
