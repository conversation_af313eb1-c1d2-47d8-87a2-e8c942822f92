import React from 'react'
import { DollarSign, TrendingUp, TrendingDown, PiggyBank, Coffee, Folder, Tag, Plus, Minus } from 'lucide-react'

const Step3Expectations = ({ formData, setFormData, setupData }) => {
  const handleMainValueChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCategoryExpectationChange = (categoryId, value) => {
    setFormData(prev => ({
      ...prev,
      categoryExpectations: {
        ...prev.categoryExpectations,
        [categoryId]: value ? parseFloat(value) : 0
      }
    }))
  }

  const handleTagExpectationChange = (tagId, value) => {
    setFormData(prev => ({
      ...prev,
      tagExpectations: {
        ...prev.tagExpectations,
        [tagId]: value ? parseFloat(value) : 0
      }
    }))
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const formatCurrencyInput = (value) => {
    if (!value) return ''
    // Remove tudo que não é número
    const numbers = value.toString().replace(/\D/g, '')
    // Converte para centavos
    const amount = parseFloat(numbers) / 100
    // Formata como moeda brasileira
    return amount.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })
  }

  const parseCurrencyInput = (value) => {
    if (!value) return 0
    // Remove tudo que não é número
    const numbers = value.replace(/\D/g, '')
    // Converte para valor real
    return parseFloat(numbers) / 100
  }

  const handleCurrencyChange = (field, value) => {
    const numericValue = parseCurrencyInput(value)
    handleMainValueChange(field, numericValue.toString())
  }

  // Obter categorias e tags selecionadas no passo 2
  const getSelectedItems = () => {
    const selectedCategories = new Set()
    const selectedTags = new Set()

    Object.values(formData.mappings).forEach(mapping => {
      mapping.categories.forEach(id => selectedCategories.add(id))
      mapping.tags.forEach(id => selectedTags.add(id))
    })

    return {
      categories: setupData.categories.filter(cat => selectedCategories.has(cat.id)),
      tags: setupData.tags.filter(tag => selectedTags.has(tag.id))
    }
  }

  const selectedItems = getSelectedItems()

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Valores Esperados
        </h3>
        <p className="text-gray-600">
          Configure os valores esperados para o mês e expectativas específicas por categoria e tag.
        </p>
      </div>

      {/* Valores Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Renda Mensal Esperada */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-900">Renda Mensal Esperada</h4>
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">R$</span>
            <input
              type="text"
              value={formatCurrencyInput(formData.expectedIncome)}
              onChange={(e) => handleCurrencyChange('expectedIncome', e.target.value)}
              placeholder="0,00"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Valor total esperado de receitas</p>
        </div>

        {/* Valor Esperado de Despesas */}
        <div className="bg-red-50 rounded-lg p-4 border border-red-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <TrendingDown className="h-4 w-4 text-red-600" />
            </div>
            <h4 className="font-medium text-gray-900">Despesas Essenciais</h4>
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">R$</span>
            <input
              type="text"
              value={formatCurrencyInput(formData.expectedExpenses)}
              onChange={(e) => handleCurrencyChange('expectedExpenses', e.target.value)}
              placeholder="0,00"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.expensePercentage / 100) : 0)}
          </p>
        </div>

        {/* Valor Esperado de Poupança */}
        <div className="bg-green-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <PiggyBank className="h-4 w-4 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-900">Poupança/Investimentos</h4>
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">R$</span>
            <input
              type="text"
              value={formatCurrencyInput(formData.expectedSavings)}
              onChange={(e) => handleCurrencyChange('expectedSavings', e.target.value)}
              placeholder="0,00"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.savingsPercentage / 100) : 0)}
          </p>
        </div>

        {/* Valor Esperado de Lazer */}
        <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <Coffee className="h-4 w-4 text-purple-600" />
            </div>
            <h4 className="font-medium text-gray-900">Lazer</h4>
          </div>
          <div className="relative">
            <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 text-sm">R$</span>
            <input
              type="text"
              value={formatCurrencyInput(formData.expectedLeisure)}
              onChange={(e) => handleCurrencyChange('expectedLeisure', e.target.value)}
              placeholder="0,00"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.leisurePercentage / 100) : 0)}
          </p>
        </div>
      </div>

      {/* Expectativas por Categoria */}
      {selectedItems.categories.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Folder className="h-5 w-5 text-blue-600" />
              <h4 className="font-semibold text-gray-900">Expectativas por Categoria</h4>
              <span className="text-sm text-blue-600 bg-blue-100 px-2 py-1 rounded-full">opcional</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Defina valores esperados específicos para cada categoria selecionada
            </p>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {selectedItems.categories.map(category => (
                <div key={category.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Folder className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">{category.name}</h5>
                      <p className="text-xs text-gray-500">Valor esperado para esta categoria</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">R$</span>
                    <input
                      type="text"
                      value={formatCurrencyInput(formData.categoryExpectations[category.id] || 0)}
                      onChange={(e) => {
                        const numericValue = parseCurrencyInput(e.target.value)
                        handleCategoryExpectationChange(category.id, numericValue.toString())
                      }}
                      placeholder="0,00"
                      className="w-24 px-3 py-2 border border-gray-300 rounded-lg text-sm text-right focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Expectativas por Tag */}
      {selectedItems.tags.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="bg-gradient-to-r from-purple-50 to-pink-50 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center gap-2">
              <Tag className="h-5 w-5 text-purple-600" />
              <h4 className="font-semibold text-gray-900">Expectativas por Tag</h4>
              <span className="text-sm text-purple-600 bg-purple-100 px-2 py-1 rounded-full">opcional</span>
            </div>
            <p className="text-sm text-gray-600 mt-1">
              Defina valores esperados específicos para cada tag selecionada
            </p>
          </div>

          <div className="p-6">
            <div className="space-y-4">
              {selectedItems.tags.map(tag => (
                <div key={tag.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Tag className="h-4 w-4 text-purple-600" />
                    </div>
                    <div>
                      <h5 className="font-medium text-gray-900">{tag.name}</h5>
                      <p className="text-xs text-gray-500">Valor esperado para esta tag</p>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className="text-sm text-gray-500">R$</span>
                    <input
                      type="text"
                      value={formatCurrencyInput(formData.tagExpectations[tag.id] || 0)}
                      onChange={(e) => {
                        const numericValue = parseCurrencyInput(e.target.value)
                        handleTagExpectationChange(tag.id, numericValue.toString())
                      }}
                      placeholder="0,00"
                      className="w-24 px-3 py-2 border border-gray-300 rounded-lg text-sm text-right focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Resumo */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
        <h5 className="font-medium text-blue-900 mb-3">📊 Resumo das Expectativas</h5>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-blue-700">Renda:</span>
            <div className="font-medium">{formatCurrency(parseFloat(formData.expectedIncome) || 0)}</div>
          </div>
          <div>
            <span className="text-blue-700">Despesas:</span>
            <div className="font-medium">{formatCurrency(parseFloat(formData.expectedExpenses) || 0)}</div>
          </div>
          <div>
            <span className="text-blue-700">Poupança:</span>
            <div className="font-medium">{formatCurrency(parseFloat(formData.expectedSavings) || 0)}</div>
          </div>
          <div>
            <span className="text-blue-700">Lazer:</span>
            <div className="font-medium">{formatCurrency(parseFloat(formData.expectedLeisure) || 0)}</div>
          </div>
        </div>
      </div>

      {/* Dicas */}
      <div className="bg-green-50 rounded-lg p-4 border border-green-100">
        <h5 className="font-medium text-green-900 mb-2">💡 Dicas para valores esperados:</h5>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• Use valores realistas baseados no seu histórico</li>
          <li>• Expectativas por categoria ajudam no controle detalhado</li>
          <li>• Deixe campos opcionais em branco se não souber estimar</li>
          <li>• Você pode ajustar estes valores a qualquer momento</li>
        </ul>
      </div>
    </div>
  )
}

export default Step3Expectations
