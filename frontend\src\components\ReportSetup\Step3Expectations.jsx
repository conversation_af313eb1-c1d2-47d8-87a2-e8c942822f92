import React from 'react'
import { DollarSign, TrendingUp, TrendingDown, PiggyBank, Coffee, Folder, Tag } from 'lucide-react'

const Step3Expectations = ({ formData, setFormData, setupData }) => {
  const handleMainValueChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleCategoryExpectationChange = (categoryId, value) => {
    setFormData(prev => ({
      ...prev,
      categoryExpectations: {
        ...prev.categoryExpectations,
        [categoryId]: value ? parseFloat(value) : 0
      }
    }))
  }

  const handleTagExpectationChange = (tagId, value) => {
    setFormData(prev => ({
      ...prev,
      tagExpectations: {
        ...prev.tagExpectations,
        [tagId]: value ? parseFloat(value) : 0
      }
    }))
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  // Obter categorias e tags selecionadas no passo 2
  const getSelectedItems = () => {
    const selectedCategories = new Set()
    const selectedTags = new Set()

    Object.values(formData.mappings).forEach(mapping => {
      mapping.categories.forEach(id => selectedCategories.add(id))
      mapping.tags.forEach(id => selectedTags.add(id))
    })

    return {
      categories: setupData.categories.filter(cat => selectedCategories.has(cat.id)),
      tags: setupData.tags.filter(tag => selectedTags.has(tag.id))
    }
  }

  const selectedItems = getSelectedItems()

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Valores Esperados
        </h3>
        <p className="text-gray-600">
          Configure os valores esperados para o mês e expectativas específicas por categoria e tag.
        </p>
      </div>

      {/* Valores Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Renda Mensal Esperada */}
        <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-blue-600" />
            </div>
            <h4 className="font-medium text-gray-900">Renda Mensal Esperada</h4>
          </div>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="number"
              value={formData.expectedIncome}
              onChange={(e) => handleMainValueChange('expectedIncome', e.target.value)}
              placeholder="Ex: 5000"
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">Valor total esperado de receitas</p>
        </div>

        {/* Valor Esperado de Despesas */}
        <div className="bg-red-50 rounded-lg p-4 border border-red-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center">
              <TrendingDown className="h-4 w-4 text-red-600" />
            </div>
            <h4 className="font-medium text-gray-900">Despesas Essenciais</h4>
          </div>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="number"
              value={formData.expectedExpenses}
              onChange={(e) => handleMainValueChange('expectedExpenses', e.target.value)}
              placeholder={`Ex: ${formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.expensePercentage / 100).toFixed(0) : '2500'}`}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.expensePercentage / 100) : 0)}
          </p>
        </div>

        {/* Valor Esperado de Poupança */}
        <div className="bg-green-50 rounded-lg p-4 border border-green-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
              <PiggyBank className="h-4 w-4 text-green-600" />
            </div>
            <h4 className="font-medium text-gray-900">Poupança/Investimentos</h4>
          </div>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="number"
              value={formData.expectedSavings}
              onChange={(e) => handleMainValueChange('expectedSavings', e.target.value)}
              placeholder={`Ex: ${formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.savingsPercentage / 100).toFixed(0) : '1000'}`}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.savingsPercentage / 100) : 0)}
          </p>
        </div>

        {/* Valor Esperado de Lazer */}
        <div className="bg-purple-50 rounded-lg p-4 border border-purple-100">
          <div className="flex items-center gap-2 mb-3">
            <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
              <Coffee className="h-4 w-4 text-purple-600" />
            </div>
            <h4 className="font-medium text-gray-900">Lazer</h4>
          </div>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="number"
              value={formData.expectedLeisure}
              onChange={(e) => handleMainValueChange('expectedLeisure', e.target.value)}
              placeholder={`Ex: ${formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.leisurePercentage / 100).toFixed(0) : '1500'}`}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
          </div>
          <p className="text-xs text-gray-500 mt-1">
            Sugerido: {formatCurrency(formData.expectedIncome ? (parseFloat(formData.expectedIncome) * formData.leisurePercentage / 100) : 0)}
          </p>
        </div>
      </div>

      {/* Expectativas por Categoria */}
      {selectedItems.categories.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-2 mb-4">
            <Folder className="h-5 w-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Expectativas por Categoria</h4>
            <span className="text-sm text-gray-500">(opcional)</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {selectedItems.categories.map(category => (
              <div key={category.id} className="bg-gray-50 rounded-lg p-3">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {category.name}
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                  <input
                    type="number"
                    value={formData.categoryExpectations[category.id] || ''}
                    onChange={(e) => handleCategoryExpectationChange(category.id, e.target.value)}
                    placeholder="0"
                    className="w-full pl-7 pr-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Expectativas por Tag */}
      {selectedItems.tags.length > 0 && (
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center gap-2 mb-4">
            <Tag className="h-5 w-5 text-gray-600" />
            <h4 className="font-medium text-gray-900">Expectativas por Tag</h4>
            <span className="text-sm text-gray-500">(opcional)</span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {selectedItems.tags.map(tag => (
              <div key={tag.id} className="bg-gray-50 rounded-lg p-3">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  {tag.name}
                </label>
                <div className="relative">
                  <DollarSign className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
                  <input
                    type="number"
                    value={formData.tagExpectations[tag.id] || ''}
                    onChange={(e) => handleTagExpectationChange(tag.id, e.target.value)}
                    placeholder="0"
                    className="w-full pl-7 pr-3 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Resumo */}
      <div className="bg-blue-50 rounded-lg p-4 border border-blue-100">
        <h5 className="font-medium text-blue-900 mb-3">📊 Resumo das Expectativas</h5>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-blue-700">Renda:</span>
            <div className="font-medium">{formatCurrency(formData.expectedIncome)}</div>
          </div>
          <div>
            <span className="text-blue-700">Despesas:</span>
            <div className="font-medium">{formatCurrency(formData.expectedExpenses)}</div>
          </div>
          <div>
            <span className="text-blue-700">Poupança:</span>
            <div className="font-medium">{formatCurrency(formData.expectedSavings)}</div>
          </div>
          <div>
            <span className="text-blue-700">Lazer:</span>
            <div className="font-medium">{formatCurrency(formData.expectedLeisure)}</div>
          </div>
        </div>
      </div>

      {/* Dicas */}
      <div className="bg-green-50 rounded-lg p-4 border border-green-100">
        <h5 className="font-medium text-green-900 mb-2">💡 Dicas para valores esperados:</h5>
        <ul className="text-sm text-green-800 space-y-1">
          <li>• Use valores realistas baseados no seu histórico</li>
          <li>• Expectativas por categoria ajudam no controle detalhado</li>
          <li>• Deixe campos opcionais em branco se não souber estimar</li>
          <li>• Você pode ajustar estes valores a qualquer momento</li>
        </ul>
      </div>
    </div>
  )
}

export default Step3Expectations
