const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const http = require('http');
const swaggerUi = require('swagger-ui-express');
const swaggerJsdoc = require('swagger-jsdoc');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const transactionRoutes = require('./routes/transactions');
const categoryRoutes = require('./routes/categories');
const dashboardRoutes = require('./routes/dashboard');
const analyticsRoutes = require('./routes/analytics');
const layoutRoutes = require('./routes/layout');
const dashboardCardsRoutes = require('./routes/dashboardCards');
const dashboardProfilesRoutes = require('./routes/dashboardProfiles');
const bankRoutes = require('./routes/banks');
const paymentMethodRoutes = require('./routes/paymentMethods');
const savingsRoutes = require('./routes/savings');
const subscriptionRoutes = require('./routes/subscriptions');
const { router: contactRoutes } = require('./routes/contacts');
const transactionContactsRoutes = require('./routes/transactionContacts');
const loanRoutes = require('./routes/loans');
const resetRoutes = require('../routes/reset');
const tagRoutes = require('./routes/tags');
const routinesRoutes = require('./routes/routines');
const notificationsRoutes = require('./routes/notifications');
const transactionTemplatesRoutes = require('./routes/transactionTemplates');

const dateSimulatorRoutes = require('./routes/dateSimulator');
const billsRoutes = require('./routes/bills');
const financialGoalsRoutes = require('./routes/financialGoals');
const reportsRoutes = require('./routes/reports');
const billUpdateService = require('./services/billUpdateService');
const goalsRoutes = require('./routes/goals');

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware de segurança
app.use(helmet());
app.use(cors({
  origin: process.env.NODE_ENV === 'production'
    ? ['https://seudominio.com', 'http://localhost:3000']
    : ['http://localhost:5173', 'http://localhost:3000'],
  credentials: true
}));

// Rate limiting mais flexível
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutos
  max: 1000, // máximo 1000 requests por IP por janela de tempo (aumentado)
  message: {
    error: 'Muitas requisições. Tente novamente em alguns minutos.',
    retryAfter: '15 minutos'
  },
  standardHeaders: true, // Retorna rate limit info nos headers `RateLimit-*`
  legacyHeaders: false, // Desabilita headers `X-RateLimit-*`
  skip: (req) => {
    // Pular rate limiting para health check
    return req.url === '/api/health'
  }
});
app.use(limiter);

// Middleware de timeout global
app.use((req, res, next) => {
  // Timeout de 60 segundos para todas as requisições
  req.setTimeout(60000, () => {
    console.error(`Timeout na requisição: ${req.method} ${req.url}`);
    if (!res.headersSent) {
      res.status(408).json({ error: 'Timeout da requisição' });
    }
  });

  res.setTimeout(60000, () => {
    console.error(`Timeout na resposta: ${req.method} ${req.url}`);
    if (!res.headersSent) {
      res.status(408).json({ error: 'Timeout da resposta' });
    }
  });

  next();
});

// Middleware para parsing JSON
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Middleware de logging para debug
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
  next();
});

// Rotas
app.use('/api/auth', authRoutes);
app.use('/api/transactions', transactionRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/analytics', analyticsRoutes);
app.use('/api/layout', layoutRoutes);
app.use('/api/dashboard-cards', dashboardCardsRoutes);
app.use('/api/dashboard-profiles', dashboardProfilesRoutes);
app.use('/api/banks', bankRoutes);
app.use('/api/payment-methods', paymentMethodRoutes);
app.use('/api/savings', savingsRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/contacts', contactRoutes);
app.use('/api/contacts-tx', transactionContactsRoutes);
app.use('/api/loans', loanRoutes);
app.use('/api/reset', resetRoutes);
app.use('/api/tags', tagRoutes);
app.use('/api/routines', routinesRoutes);
app.use('/api/notifications', notificationsRoutes);
app.use('/api/transactiontemplates', transactionTemplatesRoutes);
app.use('/api/date-simulator', dateSimulatorRoutes);
app.use('/api/bills', billsRoutes);
app.use('/api/financial-goals', financialGoalsRoutes);
app.use('/api/reports', reportsRoutes);
app.use('/api/goals', goalsRoutes)

// 📚 CONFIGURAÇÃO DO SWAGGER
const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'SARA API',
      version: '1.0.0',
      description: 'Sistema de Acompanhamento de Recursos e Aplicações - API Documentation',
    },
    servers: [
      {
        url: 'http://localhost:3001/api',
        description: 'Servidor de Desenvolvimento',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: ['./src/routes/*.js'], // Caminho para os arquivos de rota
};

const specs = swaggerJsdoc(swaggerOptions);
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs));

// 🔍 LOG DETALHADO DE TODAS AS ROTAS REGISTRADAS
console.log('\n🚀 ===== ROTAS REGISTRADAS NO BACKEND =====')
console.log('✅ /api/auth - Autenticação')
console.log('✅ /api/transactions - Transações')
console.log('✅ /api/categories - Categorias')
console.log('✅ /api/dashboard - Dashboard')
console.log('✅ /api/analytics - Analytics')
console.log('✅ /api/layout - Layout')
console.log('✅ /api/dashboard-cards - Dashboard Cards')
console.log('✅ /api/dashboard-profiles - Dashboard Profiles')
console.log('✅ /api/banks - Bancos')
console.log('✅ /api/payment-methods - Métodos de Pagamento')
console.log('✅ /api/savings - Cofrinhos')
console.log('✅ /api/subscriptions - Assinaturas')
console.log('✅ /api/contacts - Contatos de Empréstimos')
console.log('✅ /api/contacts-tx - Contatos de Transações (NOVA ROTA)')
console.log('✅ /api/loans - Empréstimos')
console.log('✅ /api/reset - Reset')
console.log('✅ /api/tags - Tags')
console.log('✅ /api/routines - Rotinas')
console.log('✅ /api/notifications - Notificações')
console.log('✅ /api/transactiontemplates - Templates de Transação')
console.log('✅ /api/goals - Objetivos Financeiros (NOVA ROTA)')
console.log('✅ /api/installment-controls - Controles de Parcelas (NOVA ROTA)')
console.log('✅ /api/billing-cycles - Ciclos de Faturamento (NOVA ROTA)')
console.log('✅ /api/date-simulator - Simulador de Data para Testes (NOVA ROTA)')
console.log('✅ /api/bills - Gerenciamento de Faturas (NOVA ROTA)')
console.log('📚 Swagger UI disponível em: http://localhost:3001/api-docs')
console.log('==========================================\n')

// Rota de health check
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    documentation: 'http://localhost:3001/api-docs',
    endpoints: {
      auth: '/api/auth',
      transactions: '/api/transactions',
      categories: '/api/categories',
      dashboard: '/api/dashboard',
      analytics: '/api/analytics',
      layout: '/api/layout',
      dashboardCards: '/api/dashboard-cards',
      dashboardProfiles: '/api/dashboard-profiles',
      banks: '/api/banks',
      paymentMethods: '/api/payment-methods',
      savings: '/api/savings',
      subscriptions: '/api/subscriptions',
      loanContacts: '/api/contacts',
      transactionContacts: '/api/contacts-tx',
      loans: '/api/loans',
      reset: '/api/reset',
      tags: '/api/tags',
      routines: '/api/routines',
      notifications: '/api/notifications',
      transactionTemplates: '/api/transactiontemplates',
      installmentControls: '/api/installment-controls',
      billingCycles: '/api/billing-cycles',
      dateSimulator: '/api/date-simulator',
      bills: '/api/bills'
    }
  });
});

// Rota de health check sem /api para compatibilidade com Docker
app.get('/health', (req, res) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Middleware de tratamento de erros
app.use((err, req, res, next) => {
  console.error(`Erro na aplicação: ${err.message}`);
  console.error(err.stack);

  // Não enviar resposta se já foi enviada
  if (res.headersSent) {
    return next(err);
  }

  // Tratar diferentes tipos de erro
  let statusCode = 500;
  let message = 'Erro interno do servidor';

  if (err.name === 'ValidationError') {
    statusCode = 400;
    message = 'Dados inválidos';
  } else if (err.name === 'UnauthorizedError') {
    statusCode = 401;
    message = 'Não autorizado';
  } else if (err.code === 'LIMIT_FILE_SIZE') {
    statusCode = 413;
    message = 'Arquivo muito grande';
  } else if (err.message.includes('timeout')) {
    statusCode = 408;
    message = 'Timeout da operação';
  }

  res.status(statusCode).json({
    error: message,
    details: process.env.NODE_ENV === 'development' ? err.message : undefined
  });
});

// Middleware para rotas não encontradas
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Rota não encontrada' });
});

// Criar servidor HTTP com configurações otimizadas
const server = http.createServer(app);

// Configurar keep-alive e timeouts
server.keepAliveTimeout = 65000; // 65 segundos
server.headersTimeout = 66000; // 66 segundos
server.timeout = 120000; // 2 minutos

// Configurar máximo de conexões
server.maxConnections = 1000;

// Tratar eventos do servidor
server.on('connection', (socket) => {
  socket.setKeepAlive(true, 60000); // Keep-alive de 60 segundos
  socket.setTimeout(120000); // Timeout de 2 minutos
});

server.on('error', (error) => {
  console.error('Erro no servidor:', error);
});

server.on('clientError', (err, socket) => {
  console.error('Erro do cliente:', err);
  if (!socket.destroyed) {
    socket.end('HTTP/1.1 400 Bad Request\r\n\r\n');
  }
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('Recebido SIGTERM, fechando servidor graciosamente...');
  server.close(() => {
    console.log('Servidor fechado.');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('Recebido SIGINT, fechando servidor graciosamente...');
  server.close(() => {
    console.log('Servidor fechado.');
    process.exit(0);
  });
});

// Tratar erros não capturados
process.on('uncaughtException', (error) => {
  console.error('Erro não capturado:', error);
  // Não fechar o processo, apenas logar
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Promise rejeitada não tratada:', reason);
  // Não fechar o processo, apenas logar
});

server.listen(PORT, async () => {
  console.log(`🚀 Servidor rodando na porta ${PORT}`);
  console.log(`📊 Dashboard: http://localhost:5173`);
  console.log(`🔗 API: http://localhost:${PORT}/api`);
  console.log(`⚡ Keep-alive ativado para melhor performance`);

  // Inicializar serviço de atualização de faturas
  try {
    await billUpdateService.initialize();
    console.log(`🔄 Serviço de atualização de faturas inicializado`);
  } catch (error) {
    console.error('❌ Erro ao inicializar serviço de faturas:', error);
  }
});
