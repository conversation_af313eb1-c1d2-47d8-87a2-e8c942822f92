const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const billService = require('../services/billService');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true,
        tags: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar nova assinatura com lógica de status
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      amount,
      billingDay,
      bankId,
      categoryId,
      startDate,
      isPaidThisMonth, // Vem do frontend quando usuário confirma se já foi paga
      tagIds
    } = req.body;

    // Validar dados obrigatórios
    if (!name || !amount || !billingDay || !bankId) {
      return res.status(400).json({
        error: 'Dados obrigatórios: name, amount, billingDay, bankId'
      });
    }

    // Processar tags
    let parsedTagIds = []
    if (tagIds && tagIds.length > 0) {
      try {
        parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds)
        // Verificar se todas as tags pertencem ao usuário
        const tags = await prisma.tag.findMany({
          where: {
            id: { in: parsedTagIds },
            userId: req.user.id
          }
        })
        if (tags.length !== parsedTagIds.length) {
          return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
        }
      } catch (e) {
        return res.status(400).json({ error: 'IDs de tags inválidos' })
      }
    }

    // Verificar se o banco existe
    const bank = await prisma.bank.findFirst({
      where: {
        id: bankId,
        userId: req.user.id
      }
    });

    if (!bank) {
      return res.status(404).json({
        error: 'Banco não encontrado'
      });
    }

    // Buscar método de pagamento padrão do banco (primeiro cartão de crédito ou débito)
    let paymentMethod = await prisma.paymentMethod.findFirst({
      where: {
        bankId: bank.id,
        userId: req.user.id,
        type: 'CREDIT'
      }
    });

    // Se não tem cartão de crédito, buscar débito
    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: {
          bankId: bank.id,
          userId: req.user.id,
          type: 'DEBIT'
        }
      });
    }

    // Se ainda não tem, criar um método padrão
    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.create({
        data: {
          name: `${bank.name} - Padrão`,
          type: bank.creditLimit > 0 ? 'CREDIT' : 'DEBIT',
          bankId: bank.id,
          userId: req.user.id
        }
      });
    }

    // Para cartões de crédito, verificar se tem dia de vencimento
    if (paymentMethod.type === 'CREDIT' && !bank.billDueDay) {
      return res.status(400).json({
        error: 'Banco deve ter dia de vencimento configurado para assinaturas de cartão de crédito'
      });
    }

    // Verificar se pode criar assinatura (fatura não está em atraso)
    if (paymentMethod.type === 'CREDIT') {
      const canCreate = await billService.canCreateTransaction(bank.id, req.user.id);
      if (!canCreate.canCreate) {
        return res.status(400).json({
          error: canCreate.reason,
          billAmount: canCreate.billAmount,
          dueDate: canCreate.dueDate
        });
      }
    }

    // Calcular status da assinatura baseado nas regras
    const subscriptionStartDate = startDate ? new Date(startDate) : new Date();
    const currentDate = new Date();
    let subscriptionStatus = 'RESERVED'; // Status padrão
    let shouldDeductFromBank = false;

    // Criar data de cobrança no contexto do mês atual
    const billingDateThisMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), parseInt(billingDay));

    if (paymentMethod.type === 'CREDIT') {
      // Para cartões de crédito, usar lógica baseada na fatura
      const currentBillDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      // Se já passou do dia de vencimento deste mês, a fatura atual vence no próximo mês
      if (currentDate.getDate() > bank.billDueDay) {
        currentBillDueDate.setMonth(currentBillDueDate.getMonth() + 1);
      }

      // Data limite para BANK_BALANCE (vencimento - 1 mês)
      const bankBalanceLimit = new Date(currentBillDueDate);
      bankBalanceLimit.setMonth(bankBalanceLimit.getMonth() - 1);

      // Data de fechamento da fatura atual
      const currentBillCloseDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      console.log(`📅 Datas de referência para assinatura:`);
      console.log(`   Data de cobrança: ${billingDateThisMonth.toLocaleDateString()}`);
      console.log(`   Vencimento fatura: ${currentBillDueDate.toLocaleDateString()}`);
      console.log(`   Limite BANK_BALANCE: ${bankBalanceLimit.toLocaleDateString()}`);
      console.log(`   Fechamento fatura: ${currentBillCloseDate.toLocaleDateString()}`);

      // Regra 4: Data de início (substituindo dia pelo dia de cobrança) = data atual = CURRENT_BILL
      const billingDateFromStartDate = new Date(subscriptionStartDate);
      billingDateFromStartDate.setDate(parseInt(billingDay));
      
      const today = new Date();
      today.setHours(0, 0, 0, 0); // Zerar horas para comparar apenas a data
      billingDateFromStartDate.setHours(0, 0, 0, 0);
      if (billingDateThisMonth.getTime() === today.getTime()) {
        subscriptionStatus = 'CURRENT_BILL';
        // Descontar do limite de crédito disponível
        if (bank.availableLimit < parseFloat(amount)) {
          return res.status(400).json({
            error: `Limite insuficiente. Disponível: R$ ${bank.availableLimit.toFixed(2)}, Necessário: R$ ${parseFloat(amount).toFixed(2)}`
          });
        }
        await prisma.bank.update({
          where: { id: bank.id },
          data: {
            availableLimit: {
              decrement: parseFloat(amount)
            }
          }
        });
        console.log(`📋 Regra 4: Data de cobrança = data atual (${today.toLocaleDateString()}) = CURRENT_BILL (descontado do limite)`);
      }

      // Regra 1: Data de cobrança > data de vencimento da fatura atual = RESERVED
      else if (parseInt(billingDay) > bank.billDueDay) {
        subscriptionStatus = 'RESERVED';
        console.log(`📋 Regra 1: Dia de cobrança (${billingDay}) > dia vencimento (${bank.billDueDay}) = RESERVED`);
      }
      // Regra 2: Data de início < (vencimento - 1 mês) = Perguntar ao usuário
      else if (billingDateThisMonth < bankBalanceLimit) {
        if (isPaidThisMonth === true) {
          subscriptionStatus = 'BANK_BALANCE';
          shouldDeductFromBank = true;
          console.log(`📋 Regra 2a: Data < limite BANK_BALANCE e usuário confirmou pagamento = BANK_BALANCE`);
        } 
        else if(billingDateThisMonth === today){
          subscriptionStatus = 'CURRENT_BILL';
          shouldDeductFromBank = false;
          console.log(`📋 Regra 2c: Data < limite BANK_BALANCE e usuário não confirmou pagamento e data de cobrança = data atual = CURRENT_BILL`);
        }
        else {
          subscriptionStatus = 'RESERVED';
          console.log(`📋 Regra 2b: Data < limite BANK_BALANCE e usuário não confirmou pagamento = RESERVED`);
        }
      }
      // Regra 3: Data dentro do range da fatura atual = CURRENT_BILL
      //else if (billingDateThisMonth >= bankBalanceLimit && billingDateThisMonth <= currentBillCloseDate && billingDay.getTime() === today.getTime()) {
      else if(billingDateThisMonth === today){
        subscriptionStatus = 'CURRENT_BILL';
        console.log(`📋 Regra 3: Data dentro do range da fatura atual = CURRENT_BILL`);
      }
      // Caso padrão: RESERVED
      else {
        subscriptionStatus = 'RESERVED';
        console.log(`📋 Caso padrão: RESERVED`);
      }
    } else {
      // Para outros métodos de pagamento (débito, PIX, etc.), sempre BANK_BALANCE
      subscriptionStatus = 'BANK_BALANCE';
      shouldDeductFromBank = true;
      console.log(`📋 Método não-crédito: BANK_BALANCE`);
    }

    // Criar assinatura
    const subscription = await prisma.subscription.create({
      data: {
        name,
        description,
        amount: parseFloat(amount),
        billingDay: parseInt(billingDay),
        paymentMethodId: paymentMethod.id,
        categoryId: categoryId || null,
        userId: req.user.id,
        startDate: subscriptionStartDate,
        status: subscriptionStatus,
        isActive: true,
        tags: {
          connect: parsedTagIds.map(id => ({ id }))
        }
      },
      include: {
        paymentMethod: {
          include: { bank: true }
        },
        category: true,
        tags: true
      }
    });

    // Se deve descontar do banco (BANK_BALANCE), fazer o desconto
    if (shouldDeductFromBank) {
      await prisma.bank.update({
        where: { id: bank.id },
        data: {
          currentBalance: {
            decrement: parseFloat(amount)
          }
        }
      });
      console.log(`💰 Descontado R$ ${amount} do saldo do banco ${bank.name}`);
    }

    res.status(201).json({
      success: true,
      subscription,
      message: 'Assinatura criada com sucesso',
      statusInfo: {
        status: subscriptionStatus,
        deductedFromBank: shouldDeductFromBank,
        amount: parseFloat(amount)
      }
    });
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId, tagIds } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    // Processar tags se fornecidas
    let parsedTagIds = []
    if (tagIds !== undefined) {
      if (tagIds && tagIds.length > 0) {
        try {
          parsedTagIds = Array.isArray(tagIds) ? tagIds : JSON.parse(tagIds)
          // Verificar se todas as tags pertencem ao usuário
          const tags = await prisma.tag.findMany({
            where: {
              id: { in: parsedTagIds },
              userId: req.user.id
            }
          })
          if (tags.length !== parsedTagIds.length) {
            return res.status(400).json({ error: 'Uma ou mais tags inválidas' })
          }
        } catch (e) {
          return res.status(400).json({ error: 'IDs de tags inválidos' })
        }
      }
    }

    const updateData = {
      name: name || subscription.name,
      description: description !== undefined ? description : subscription.description,
      amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
      isActive: isActive !== undefined ? isActive : subscription.isActive,
      categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
    };

    // Atualizar tags se fornecidas
    if (tagIds !== undefined) {
      updateData.tags = {
        set: parsedTagIds.map(id => ({ id }))
      };
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: updateData,
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true,
        tags: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    console.log('🗑️ Deletando assinatura:', subscription.name);

    // Deletar a assinatura (básico)
    await prisma.subscription.delete({
      where: { id }
    });

    console.log('✅ Assinatura deletada com sucesso');
    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});



module.exports = router;
