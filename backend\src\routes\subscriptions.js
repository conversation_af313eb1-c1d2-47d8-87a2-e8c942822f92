const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Aplicar middleware de autenticação em todas as rotas
router.use(authenticateToken);

// Listar assinaturas do usuário
router.get('/', async (req, res) => {
  try {
    const subscriptions = await prisma.subscription.findMany({
      where: { userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      },
      orderBy: { name: 'asc' }
    });

    res.json(subscriptions);
  } catch (error) {
    console.error('Erro ao buscar assinaturas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar nova assinatura com lógica de status
router.post('/', async (req, res) => {
  try {
    const {
      name,
      description,
      amount,
      billingDay,
      bankId,
      categoryId,
      startDate,
      isPaidThisMonth // Vem do frontend quando usuário confirma se já foi paga
    } = req.body;

    // Validar dados obrigatórios
    if (!name || !amount || !billingDay || !bankId) {
      return res.status(400).json({
        error: 'Dados obrigatórios: name, amount, billingDay, bankId'
      });
    }

    // Verificar se o banco existe
    const bank = await prisma.bank.findFirst({
      where: {
        id: bankId,
        userId: req.user.id
      }
    });

    if (!bank) {
      return res.status(404).json({
        error: 'Banco não encontrado'
      });
    }

    // Buscar método de pagamento padrão do banco (primeiro cartão de crédito ou débito)
    let paymentMethod = await prisma.paymentMethod.findFirst({
      where: {
        bankId: bank.id,
        userId: req.user.id,
        type: 'CREDIT'
      }
    });

    // Se não tem cartão de crédito, buscar débito
    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.findFirst({
        where: {
          bankId: bank.id,
          userId: req.user.id,
          type: 'DEBIT'
        }
      });
    }

    // Se ainda não tem, criar um método padrão
    if (!paymentMethod) {
      paymentMethod = await prisma.paymentMethod.create({
        data: {
          name: `${bank.name} - Padrão`,
          type: bank.creditLimit > 0 ? 'CREDIT' : 'DEBIT',
          bankId: bank.id,
          userId: req.user.id
        }
      });
    }

    // Para cartões de crédito, verificar se tem dia de vencimento
    if (paymentMethod.type === 'CREDIT' && !bank.billDueDay) {
      return res.status(400).json({
        error: 'Banco deve ter dia de vencimento configurado para assinaturas de cartão de crédito'
      });
    }

    // Calcular status da assinatura baseado nas regras
    const subscriptionStartDate = startDate ? new Date(startDate) : new Date();
    const currentDate = new Date();
    let subscriptionStatus = 'RESERVED'; // Status padrão
    let shouldDeductFromBank = false;

    // Criar data de cobrança no contexto do mês atual
    const billingDateThisMonth = new Date(currentDate.getFullYear(), currentDate.getMonth(), parseInt(billingDay));

    if (paymentMethod.type === 'CREDIT') {
      // Para cartões de crédito, usar lógica baseada na fatura
      const currentBillDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      // Se já passou do dia de vencimento deste mês, a fatura atual vence no próximo mês
      if (currentDate.getDate() > bank.billDueDay) {
        currentBillDueDate.setMonth(currentBillDueDate.getMonth() + 1);
      }

      // Data limite para BANK_BALANCE (vencimento - 1 mês)
      const bankBalanceLimit = new Date(currentBillDueDate);
      bankBalanceLimit.setMonth(bankBalanceLimit.getMonth() - 1);

      // Data de fechamento da fatura atual
      const currentBillCloseDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      console.log(`📅 Datas de referência para assinatura:`);
      console.log(`   Data de cobrança: ${billingDateThisMonth.toLocaleDateString()}`);
      console.log(`   Vencimento fatura: ${currentBillDueDate.toLocaleDateString()}`);
      console.log(`   Limite BANK_BALANCE: ${bankBalanceLimit.toLocaleDateString()}`);
      console.log(`   Fechamento fatura: ${currentBillCloseDate.toLocaleDateString()}`);

      // Regra 1: Data de cobrança > data de vencimento da fatura atual = RESERVED
      if (parseInt(billingDay) > bank.billDueDay) {
        subscriptionStatus = 'RESERVED';
        console.log(`📋 Regra 1: Dia de cobrança (${billingDay}) > dia vencimento (${bank.billDueDay}) = RESERVED`);
      }
      // Regra 2: Data de início < (vencimento - 1 mês) = Perguntar ao usuário
      else if (billingDateThisMonth < bankBalanceLimit) {
        if (isPaidThisMonth === true) {
          subscriptionStatus = 'BANK_BALANCE';
          shouldDeductFromBank = true;
          console.log(`📋 Regra 2a: Data < limite BANK_BALANCE e usuário confirmou pagamento = BANK_BALANCE`);
        } else {
          subscriptionStatus = 'RESERVED';
          console.log(`📋 Regra 2b: Data < limite BANK_BALANCE e usuário não confirmou pagamento = RESERVED`);
        }
      }
      // Regra 3: Data dentro do range da fatura atual = CURRENT_BILL
      else if (billingDateThisMonth >= bankBalanceLimit && billingDateThisMonth <= currentBillCloseDate) {
        subscriptionStatus = 'CURRENT_BILL';
        console.log(`📋 Regra 3: Data dentro do range da fatura atual = CURRENT_BILL`);
      }
      // Caso padrão: RESERVED
      else {
        subscriptionStatus = 'RESERVED';
        console.log(`📋 Caso padrão: RESERVED`);
      }
    } else {
      // Para outros métodos de pagamento (débito, PIX, etc.), sempre BANK_BALANCE
      subscriptionStatus = 'BANK_BALANCE';
      shouldDeductFromBank = true;
      console.log(`📋 Método não-crédito: BANK_BALANCE`);
    }

    // Criar assinatura
    const subscription = await prisma.subscription.create({
      data: {
        name,
        description,
        amount: parseFloat(amount),
        billingDay: parseInt(billingDay),
        paymentMethodId: paymentMethod.id,
        categoryId: categoryId || null,
        userId: req.user.id,
        startDate: subscriptionStartDate,
        status: subscriptionStatus,
        isActive: true
      },
      include: {
        paymentMethod: {
          include: { bank: true }
        },
        category: true
      }
    });

    // Se deve descontar do banco (BANK_BALANCE), fazer o desconto
    if (shouldDeductFromBank) {
      await prisma.bank.update({
        where: { id: bank.id },
        data: {
          currentBalance: {
            decrement: parseFloat(amount)
          }
        }
      });
      console.log(`💰 Descontado R$ ${amount} do saldo do banco ${bank.name}`);
    }

    res.status(201).json({
      success: true,
      subscription,
      message: 'Assinatura criada com sucesso',
      statusInfo: {
        status: subscriptionStatus,
        deductedFromBank: shouldDeductFromBank,
        amount: parseFloat(amount)
      }
    });
  } catch (error) {
    console.error('Erro ao criar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar assinatura
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, amount, isActive, categoryId } = req.body;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    const updatedSubscription = await prisma.subscription.update({
      where: { id },
      data: {
        name: name || subscription.name,
        description: description !== undefined ? description : subscription.description,
        amount: amount !== undefined ? parseFloat(amount) : subscription.amount,
        isActive: isActive !== undefined ? isActive : subscription.isActive,
        categoryId: categoryId !== undefined ? categoryId : subscription.categoryId
      },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        },
        category: true
      }
    });

    res.json(updatedSubscription);
  } catch (error) {
    console.error('Erro ao atualizar assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar assinatura
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const subscription = await prisma.subscription.findFirst({
      where: { id, userId: req.user.id },
      include: {
        paymentMethod: {
          include: {
            bank: true
          }
        }
      }
    });

    if (!subscription) {
      return res.status(404).json({ error: 'Assinatura não encontrada' });
    }

    console.log('🗑️ Deletando assinatura:', subscription.name);

    // Deletar a assinatura (básico)
    await prisma.subscription.delete({
      where: { id }
    });

    console.log('✅ Assinatura deletada com sucesso');
    res.json({ message: 'Assinatura excluída com sucesso' });
  } catch (error) {
    console.error('Erro ao excluir assinatura:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});



module.exports = router;
