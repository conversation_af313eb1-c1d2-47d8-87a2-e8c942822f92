# 🚀 SARA - G<PERSON><PERSON> de Deploy em Produção

## 📋 Pré-requisitos

- **Docker** 20.10+ instalado
- **Docker Compose** 2.0+ instalado
- **Conta Cloudinary** configurada
- **Repositório Git** com o código do SARA
- **Servidor** com pelo menos 2GB RAM e 20GB de espaço

## 🔧 Configuração Inicial

### 1. Preparar o Servidor

```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Instalar Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Adicionar usuário ao grupo docker
sudo usermod -aG docker $USER
```

### 2. Clonar o Projeto

```bash
git clone https://github.com/seu-usuario/sara.git
cd sara
```

### 3. Configurar Variáveis de Ambiente

```bash
# Copiar arquivo de exemplo
cp .env.production.example .env

# Editar configurações
nano .env
```

**Configurações obrigatórias:**
```env
# JWT Secret (gere uma chave segura!)
JWT_SECRET=sua_chave_jwt_super_segura_aqui

# Cloudinary
CLOUDINARY_CLOUD_NAME=seu_cloud_name
CLOUDINARY_API_KEY=sua_api_key
CLOUDINARY_API_SECRET=seu_api_secret

# Git
GIT_REPO=https://github.com/seu-usuario/sara.git
GIT_BRANCH=main

# Domínio (se usar SSL)
DOMAIN=sara.seudominio.com
ACME_EMAIL=<EMAIL>
```

## 🚀 Deploy Básico

### Opção 1: Deploy Simples (HTTP)

```bash
# Subir apenas a aplicação
docker-compose up -d sara-app

# Verificar status
docker-compose ps
docker-compose logs -f sara-app
```

### Opção 2: Deploy com SSL (HTTPS)

```bash
# Subir com Traefik para SSL automático
docker-compose --profile traefik up -d

# Verificar certificados
docker-compose logs traefik
```

### Opção 3: Deploy Completo (com backup)

```bash
# Subir todos os serviços
docker-compose --profile traefik --profile backup up -d
```

## 🔄 Auto-Deploy

O sistema inclui **auto-deploy automático** que:

1. **Monitora** o repositório Git a cada 5 minutos
2. **Detecta** novos commits na branch configurada
3. **Faz deploy** automaticamente das mudanças
4. **Reinicia** os serviços se necessário
5. **Verifica** a saúde da aplicação

### Configuração do Auto-Deploy

```env
# Ativar auto-deploy
AUTO_DEPLOY=true

# Intervalo de verificação (segundos)
DEPLOY_INTERVAL=300

# Repositório e branch
GIT_REPO=https://github.com/seu-usuario/sara.git
GIT_BRANCH=main
```

### Forçar Deploy Manual

```bash
# Remover cache do último commit
docker-compose exec sara-app rm -f /tmp/git-cache/last_commit

# O próximo ciclo fará deploy automaticamente
```

## 📊 Monitoramento

### Verificar Status dos Serviços

```bash
# Status geral
docker-compose ps

# Status interno (supervisor)
docker-compose exec sara-app supervisorctl status

# Logs em tempo real
docker-compose logs -f sara-app
```

### Verificar Saúde da Aplicação

```bash
# Health check
curl http://localhost/health

# API health
curl http://localhost/api/health

# Frontend
curl http://localhost/
```

### Logs Detalhados

```bash
# Logs do backend
docker-compose exec sara-app tail -f /var/log/supervisor/backend.log

# Logs do nginx
docker-compose exec sara-app tail -f /var/log/supervisor/nginx.log

# Logs do auto-deploy
docker-compose exec sara-app tail -f /var/log/supervisor/auto-deploy.log
```

## 💾 Backup e Restauração

### Backup Automático

O sistema faz backup automático:
- **Frequência**: A cada 24 horas (configurável)
- **Retenção**: 7 dias (configurável)
- **Local**: `/app/logs/backup_*.db`

### Backup Manual

```bash
# Fazer backup manual
docker-compose exec sara-app cp /app/backend/production.db /app/logs/backup_manual_$(date +%Y%m%d_%H%M%S).db

# Listar backups
docker-compose exec sara-app ls -la /app/logs/backup_*.db
```

### Restauração

```bash
# Parar aplicação
docker-compose stop sara-app

# Restaurar backup
docker-compose exec sara-app cp /app/logs/backup_YYYYMMDD_HHMMSS.db /app/backend/production.db

# Reiniciar aplicação
docker-compose start sara-app
```

## 🔧 Manutenção

### Atualizar Sistema

```bash
# Parar serviços
docker-compose down

# Atualizar imagens
docker-compose pull

# Reconstruir se necessário
docker-compose build --no-cache

# Subir novamente
docker-compose up -d
```

### Limpar Recursos

```bash
# Limpar containers parados
docker container prune -f

# Limpar imagens não utilizadas
docker image prune -f

# Limpar volumes órfãos
docker volume prune -f

# Limpeza completa (cuidado!)
docker system prune -af
```

### Reiniciar Serviços Específicos

```bash
# Reiniciar apenas backend
docker-compose exec sara-app supervisorctl restart backend

# Reiniciar apenas nginx
docker-compose exec sara-app supervisorctl restart nginx

# Reiniciar auto-deploy
docker-compose exec sara-app supervisorctl restart auto-deploy
```

## 🛡️ Segurança

### Configurações Recomendadas

1. **Firewall**:
```bash
# Permitir apenas portas necessárias
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

2. **SSL/TLS**:
```bash
# Usar sempre HTTPS em produção
docker-compose --profile traefik up -d
```

3. **Backup Seguro**:
```bash
# Configurar backup externo
rsync -av ./data/backups/ user@backup-server:/backups/sara/
```

### Variáveis Sensíveis

**NUNCA** commite no Git:
- `JWT_SECRET`
- `CLOUDINARY_API_SECRET`
- `WEBHOOK_SECRET`
- Senhas de banco de dados

## 🚨 Troubleshooting

### Problemas Comuns

#### 1. Aplicação não inicia
```bash
# Verificar logs
docker-compose logs sara-app

# Verificar configurações
docker-compose config

# Verificar recursos
docker stats
```

#### 2. Auto-deploy não funciona
```bash
# Verificar configuração Git
docker-compose exec sara-app git ls-remote $GIT_REPO $GIT_BRANCH

# Verificar logs
docker-compose exec sara-app tail -f /var/log/supervisor/auto-deploy.log

# Forçar deploy
docker-compose exec sara-app rm -f /tmp/git-cache/last_commit
```

#### 3. SSL não funciona
```bash
# Verificar DNS
nslookup $DOMAIN

# Verificar Traefik
docker-compose logs traefik

# Verificar certificados
docker-compose exec traefik ls -la /letsencrypt/
```

#### 4. Performance baixa
```bash
# Verificar recursos
docker stats

# Verificar logs de erro
docker-compose logs sara-app | grep ERROR

# Ajustar configurações nginx
# Editar docker/nginx.conf
```

### Comandos de Diagnóstico

```bash
# Status completo
docker-compose ps
docker-compose exec sara-app supervisorctl status

# Uso de recursos
docker stats

# Espaço em disco
df -h
docker system df

# Conectividade
curl -I http://localhost/health
curl -I http://localhost/api/health

# Logs de erro
docker-compose logs sara-app | grep -i error
```

## 📞 Suporte

### Logs Importantes

- **Aplicação**: `/var/log/supervisor/backend.log`
- **Nginx**: `/var/log/supervisor/nginx.log`
- **Auto-deploy**: `/var/log/supervisor/auto-deploy.log`
- **Sistema**: `docker-compose logs`

### Informações para Suporte

Ao reportar problemas, inclua:

1. **Versão do Docker**: `docker --version`
2. **Configuração**: `docker-compose config`
3. **Status**: `docker-compose ps`
4. **Logs**: `docker-compose logs sara-app`
5. **Recursos**: `docker stats`

---

## 🎉 Deploy Realizado!

Após o deploy bem-sucedido:

- **Frontend**: http://seu-dominio.com
- **API**: http://seu-dominio.com/api
- **Login**: <EMAIL> / 123456

**Lembre-se de alterar a senha padrão!**
