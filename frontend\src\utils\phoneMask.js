// Utilitário para máscara de telefone brasileiro

/**
 * Aplica máscara de telefone brasileiro: (ddd) 9 0000-0000
 * @param {string} value - Valor do telefone
 * @returns {string} - Telefone formatado
 */
export const formatPhoneNumber = (value) => {
  if (!value) return ''
  
  // Remove tudo que não é número
  const numbers = value.replace(/\D/g, '')
  
  // Aplica a máscara baseada no tamanho
  if (numbers.length <= 2) {
    return `(${numbers}`
  } else if (numbers.length <= 3) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2)}`
  } else if (numbers.length <= 7) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 3)} ${numbers.slice(3)}`
  } else if (numbers.length <= 11) {
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 3)} ${numbers.slice(3, 7)}-${numbers.slice(7)}`
  } else {
    // <PERSON>ita a 11 dígitos
    return `(${numbers.slice(0, 2)}) ${numbers.slice(2, 3)} ${numbers.slice(3, 7)}-${numbers.slice(7, 11)}`
  }
}

/**
 * Remove a máscara do telefone, deixando apenas números
 * @param {string} value - Telefone formatado
 * @returns {string} - Apenas números
 */
export const unformatPhoneNumber = (value) => {
  if (!value) return ''
  return value.replace(/\D/g, '')
}

/**
 * Valida se o telefone está no formato correto
 * @param {string} value - Telefone para validar
 * @returns {boolean} - True se válido
 */
export const isValidPhoneNumber = (value) => {
  const numbers = unformatPhoneNumber(value)
  // Telefone brasileiro deve ter 10 ou 11 dígitos (com DDD)
  return numbers.length === 10 || numbers.length === 11
}

/**
 * Formata telefone durante a digitação
 * @param {string} value - Valor atual
 * @param {string} previousValue - Valor anterior
 * @returns {string} - Valor formatado
 */
export const handlePhoneInput = (value, previousValue = '') => {
  const formatted = formatPhoneNumber(value)
  
  // Se o usuário está apagando, permite
  if (value.length < previousValue.length) {
    return formatted
  }
  
  // Se chegou no limite, não permite mais caracteres
  const numbers = unformatPhoneNumber(value)
  if (numbers.length > 11) {
    return previousValue
  }
  
  return formatted
}
