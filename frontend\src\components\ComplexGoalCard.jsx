import React, { useState } from 'react'
import { 
  Target, 
  TrendingUp, 
  Edit3, 
  Trash2, 
  CheckCircle, 
  Calendar,
  DollarSign,
  MoreVertical,
  Eye
} from 'lucide-react'
import ComplexGoalProjectionModal from './ComplexGoalProjectionModal'
import goalService from '../services/goalService'
import { RenderTextWithLinks } from '../utils/linkUtils'
import toast from 'react-hot-toast'

const ComplexGoalCard = ({ goal, onUpdate }) => {
  const [showProjectionModal, setShowProjectionModal] = useState(false)
  const [showItemActions, setShowItemActions] = useState({})
  const [showCompleteItemModal, setShowCompleteItemModal] = useState(false)
  const [selectedItem, setSelectedItem] = useState(null)
  const [itemCompletionValue, setItemCompletionValue] = useState('')

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getMonthName = (month) => {
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ]
    return months[month - 1]
  }

  const handleCompleteGoal = async () => {
    if (!confirm(`Tem certeza que deseja marcar "${goal.name}" como concluído?`)) {
      return
    }

    try {
      await goalService.completeGoal(goal.id, goal.targetAmount)
      toast.success('Objetivo marcado como concluído!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao marcar objetivo como concluído:', error)
      toast.error('Erro ao marcar objetivo como concluído')
    }
  }

  const handleCompleteItem = async (item) => {
    setSelectedItem(item)
    setItemCompletionValue('')
    setShowCompleteItemModal(true)
  }

  const confirmCompleteItem = async () => {
    if (!selectedItem || !itemCompletionValue) {
      toast.error('Valor gasto é obrigatório')
      return
    }

    try {
      const numericValue = parseFloat(itemCompletionValue.replace(/[^\d,]/g, '').replace(',', '.'))
      await goalService.completeComplexGoalItem(goal.id, selectedItem.id, {
        completedAmount: numericValue
      })
      toast.success('Item marcado como concluído!')
      setShowCompleteItemModal(false)
      setSelectedItem(null)
      setItemCompletionValue('')
      onUpdate()
    } catch (error) {
      console.error('Erro ao marcar item como concluído:', error)
      toast.error('Erro ao marcar item como concluído')
    }
  }

  const handleDeleteItem = async (item) => {
    if (!confirm(`Tem certeza que deseja excluir o item "${item.name}"?`)) {
      return
    }

    try {
      await goalService.deleteComplexGoalItem(goal.id, item.id)
      toast.success('Item excluído com sucesso!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao excluir item:', error)
      toast.error('Erro ao excluir item')
    }
  }

  const handleDeleteGoal = async () => {
    if (!confirm(`Tem certeza que deseja excluir o objetivo "${goal.name}" e todos os seus itens?`)) {
      return
    }

    try {
      await goalService.deleteGoal(goal.id)
      toast.success('Objetivo excluído com sucesso!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao excluir objetivo:', error)
      toast.error('Erro ao excluir objetivo')
    }
  }

  const completedItems = goal.subGoals?.filter(item => item.isCompleted) || []
  const totalItems = goal.subGoals?.length || 0
  const progressPercentage = totalItems > 0 ? (completedItems.length / totalItems) * 100 : 0

  // Calcular valores financeiros
  const totalExpected = goal.subGoals?.reduce((sum, item) => sum + (item.targetAmount || 0), 0) || 0
  const totalSpent = goal.subGoals?.reduce((sum, item) => sum + (item.completedAmount || 0), 0) || 0

  return (
    <>
      <div className={`group relative bg-white rounded-3xl shadow-xl border-2 overflow-hidden hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 ${
        goal.isCompleted
          ? 'border-green-200 ring-4 ring-green-100'
          : 'border-gray-100 hover:border-purple-300'
      }`}>
        {/* Badge de Concluído */}
        {goal.isCompleted && (
          <div className="absolute top-4 left-4 z-10">
            <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1 shadow-lg">
              <CheckCircle className="h-4 w-4" />
              Concluído
            </div>
          </div>
        )}

        {/* Header com Gradiente */}
        <div className={`relative h-48 ${
          goal.isCompleted
            ? 'bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500'
            : 'bg-gradient-to-br from-purple-500 via-violet-500 to-indigo-500'
        }`}>
          {goal.imageUrl ? (
            <>
              <img
                src={goal.imageUrl}
                alt={goal.name}
                className={`w-full h-full object-cover ${goal.isCompleted ? 'opacity-80' : ''}`}
              />
              <div className={`absolute inset-0 ${
                goal.isCompleted
                  ? 'bg-gradient-to-t from-green-900/60 via-green-900/20 to-transparent'
                  : 'bg-gradient-to-t from-purple-900/60 via-purple-900/20 to-transparent'
              }`}></div>
            </>
          ) : (
            <div className="absolute inset-0 opacity-90"></div>
          )}

          {/* Ações no Header */}
          <div className="absolute top-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={() => setShowProjectionModal(true)}
              className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-xl transition-colors"
              title="Ver Projeção"
            >
              <TrendingUp className="h-4 w-4" />
            </button>
            <button
              onClick={handleCompleteGoal}
              className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-green-500/80 rounded-xl transition-colors"
              title="Marcar como Concluído"
            >
              <CheckCircle className="h-4 w-4" />
            </button>
            <button
              onClick={handleDeleteGoal}
              className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-red-500/80 rounded-xl transition-colors"
              title="Excluir Objetivo"
            >
              <Trash2 className="h-4 w-4" />
            </button>
          </div>

          {/* Informações Principais */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
              <div className="grid grid-cols-3 gap-3 mb-3">
                <div>
                  <p className="text-white/80 text-xs font-medium">Meta Total</p>
                  <p className="text-white text-lg font-bold">
                    {formatCurrency(totalExpected)}
                  </p>
                </div>
                <div>
                  <p className="text-white/80 text-xs font-medium">Gasto</p>
                  <p className="text-white text-lg font-bold">
                    {formatCurrency(totalSpent)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-white/80 text-xs font-medium">Prazo</p>
                  <p className="text-white text-sm font-semibold">
                    {getMonthName(goal.targetMonth)}/{goal.targetYear}
                  </p>
                </div>
              </div>
              
              {/* Progresso */}
              <div>
                <div className="flex items-center justify-between text-white/80 text-xs mb-2">
                  <span>Progresso</span>
                  <span>{completedItems.length}/{totalItems} itens</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-white h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conteúdo Principal */}
        <div className="p-6">
          {/* Título e Descrição */}
          <div className="mb-6">
            <h3 className="font-bold text-gray-900 text-xl mb-2 line-clamp-1">{goal.name}</h3>
            {goal.description && (
              <div className="text-gray-600 text-sm line-clamp-2 leading-relaxed">
                <RenderTextWithLinks text={goal.description} />
              </div>
            )}
          </div>

          {/* Lista de Itens com Scroll */}
          <div className="mb-6">
            <div className="max-h-64 overflow-y-auto space-y-3 pr-2">
              {goal.subGoals?.map((item, index) => (
              <div key={index} className={`p-4 rounded-xl transition-all border ${
                item.isCompleted
                  ? 'bg-green-50 border-green-200'
                  : 'bg-gray-50 hover:bg-gray-100 border-gray-200'
              }`}>
                <div className="flex items-start justify-between">
                  <div className="flex items-start gap-3 flex-1">
                    {/* Miniatura da imagem */}
                    {item.imageUrl && (
                      <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-200 flex-shrink-0">
                        <img
                          src={item.imageUrl}
                          alt={item.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}

                    <div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${
                      item.isCompleted
                        ? 'bg-green-500 text-white'
                        : 'bg-yellow-400 text-gray-800'
                    }`}>
                      {item.isCompleted ? '✓' : index + 1}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className={`font-semibold text-sm mb-2 ${
                        item.isCompleted ? 'text-green-800 line-through' : 'text-gray-900'
                      }`}>
                        {item.name}
                      </p>

                      {/* Valores do item */}
                      <div className="space-y-1">
                        <div className="flex items-center justify-between text-xs">
                          <span className="text-gray-600">Meta:</span>
                          <span className="font-medium text-gray-900">
                            {formatCurrency(item.targetAmount)}
                          </span>
                        </div>
                        {item.isCompleted && item.completedAmount && (
                          <div className="flex items-center justify-between text-xs">
                            <span className="text-green-600">Pago:</span>
                            <span className="font-medium text-green-700">
                              {formatCurrency(item.completedAmount)}
                            </span>
                          </div>
                        )}
                        {item.isCompleted && item.completedAmount && (
                          <div className="flex items-center justify-between text-xs">
                            <span className={`${
                              item.completedAmount <= item.targetAmount ? 'text-green-600' : 'text-red-600'
                            }`}>
                              Diferença:
                            </span>
                            <span className={`font-medium ${
                              item.completedAmount <= item.targetAmount ? 'text-green-700' : 'text-red-700'
                            }`}>
                              {item.completedAmount <= item.targetAmount ? '-' : '+'}
                              {formatCurrency(Math.abs(item.targetAmount - item.completedAmount))}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  <div className="relative ml-2">
                    <button
                      onClick={() => setShowItemActions(prev => ({
                        ...prev,
                        [item.id]: !prev[item.id]
                      }))}
                      className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      <MoreVertical className="h-4 w-4" />
                    </button>

                    {showItemActions[item.id] && (
                      <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                        {!item.isCompleted && (
                          <button
                            onClick={() => {
                              handleCompleteItem(item)
                              setShowItemActions(prev => ({ ...prev, [item.id]: false }))
                            }}
                            className="w-full px-3 py-2 text-left text-sm text-green-600 hover:bg-green-50 flex items-center gap-2"
                          >
                            <CheckCircle className="h-3 w-3" />
                            Concluir
                          </button>
                        )}
                        <button
                          onClick={() => {
                            handleDeleteItem(item)
                            setShowItemActions(prev => ({ ...prev, [item.id]: false }))
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                        >
                          <Trash2 className="h-3 w-3" />
                          Excluir
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              </div>
              ))}
            </div>

            {totalItems > goal.subGoals?.length && (
              <div className="text-center py-2">
                <span className="text-sm text-gray-500">
                  {totalItems} itens no total
                </span>
              </div>
            )}
          </div>

          {/* Botões de Ação */}
          <div className="grid grid-cols-2 gap-3">
            {!goal.isCompleted ? (
              <button
                onClick={handleCompleteGoal}
                className="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 flex items-center justify-center gap-2"
              >
                <CheckCircle className="h-4 w-4" />
                Concluir
              </button>
            ) : (
              <div className="bg-gray-100 text-gray-500 font-semibold py-3 px-4 rounded-xl flex items-center justify-center gap-2 cursor-not-allowed">
                <CheckCircle className="h-4 w-4" />
                Concluído
              </div>
            )}
            <button
              onClick={() => setShowProjectionModal(true)}
              className="bg-gradient-to-r from-purple-500 to-violet-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              Projeção
            </button>
          </div>
        </div>
      </div>

      {/* Modal de Projeção */}
      <ComplexGoalProjectionModal
        goal={goal}
        isOpen={showProjectionModal}
        onClose={() => setShowProjectionModal(false)}
        onUpdate={onUpdate}
      />

      {/* Modal de Conclusão de Item */}
      {showCompleteItemModal && selectedItem && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-6 shadow-2xl">
            <div className="text-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-white" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">
                Concluir Item
              </h3>
              <p className="text-gray-600">
                {selectedItem.name}
              </p>
            </div>

            <div className="mb-6">
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Valor esperado:</span>
                  <span className="font-semibold text-gray-900">
                    {formatCurrency(selectedItem.targetAmount)}
                  </span>
                </div>
              </div>

              <label className="block text-sm font-medium text-gray-700 mb-2">
                Valor realmente gasto *
              </label>
              <input
                type="text"
                value={itemCompletionValue}
                onChange={(e) => {
                  // Aplicar máscara de moeda
                  const value = e.target.value.replace(/\D/g, '')
                  const formatted = new Intl.NumberFormat('pt-BR', {
                    style: 'currency',
                    currency: 'BRL'
                  }).format(value / 100)
                  setItemCompletionValue(formatted)
                }}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent text-right"
                placeholder="R$ 0,00"
                required
              />
              <p className="text-xs text-gray-500 mt-2">
                Informe o valor que você realmente gastou para concluir este item
              </p>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowCompleteItemModal(false)
                  setSelectedItem(null)
                  setItemCompletionValue('')
                }}
                className="flex-1 px-4 py-3 text-gray-600 hover:text-gray-800 transition-colors border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancelar
              </button>
              <button
                onClick={confirmCompleteItem}
                className="flex-1 px-4 py-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white rounded-lg hover:from-green-600 hover:to-emerald-600 transition-all duration-300 font-semibold"
              >
                Confirmar Conclusão
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  )
}

export default ComplexGoalCard
