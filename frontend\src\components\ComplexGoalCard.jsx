import React, { useState } from 'react'
import { 
  Target, 
  TrendingUp, 
  Edit3, 
  Trash2, 
  CheckCircle, 
  Calendar,
  DollarSign,
  MoreVertical,
  Eye
} from 'lucide-react'
import ComplexGoalProjectionModal from './ComplexGoalProjectionModal'
import goalService from '../services/goalService'
import toast from 'react-hot-toast'

const ComplexGoalCard = ({ goal, onUpdate }) => {
  const [showProjectionModal, setShowProjectionModal] = useState(false)
  const [showItemActions, setShowItemActions] = useState({})

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const getMonthName = (month) => {
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ]
    return months[month - 1]
  }

  const handleCompleteGoal = async () => {
    if (!confirm(`Tem certeza que deseja marcar "${goal.name}" como concluído?`)) {
      return
    }

    try {
      await goalService.completeGoal(goal.id, goal.targetAmount)
      toast.success('Objetivo marcado como concluído!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao marcar objetivo como concluído:', error)
      toast.error('Erro ao marcar objetivo como concluído')
    }
  }

  const handleCompleteItem = async (item) => {
    const value = prompt(`Informe o valor gasto para concluir "${item.name}":`, formatCurrency(item.targetAmount))
    if (!value) return

    try {
      const numericValue = parseFloat(value.replace(/[^\d,]/g, '').replace(',', '.'))
      await goalService.completeComplexGoalItem(goal.id, item.id, {
        completedAmount: numericValue
      })
      toast.success('Item marcado como concluído!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao marcar item como concluído:', error)
      toast.error('Erro ao marcar item como concluído')
    }
  }

  const handleDeleteItem = async (item) => {
    if (!confirm(`Tem certeza que deseja excluir o item "${item.name}"?`)) {
      return
    }

    try {
      await goalService.deleteComplexGoalItem(goal.id, item.id)
      toast.success('Item excluído com sucesso!')
      onUpdate()
    } catch (error) {
      console.error('Erro ao excluir item:', error)
      toast.error('Erro ao excluir item')
    }
  }

  const completedItems = goal.subGoals?.filter(item => item.isCompleted) || []
  const totalItems = goal.subGoals?.length || 0
  const progressPercentage = totalItems > 0 ? (completedItems.length / totalItems) * 100 : 0

  return (
    <>
      <div className={`group relative bg-white rounded-3xl shadow-xl border-2 overflow-hidden hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 ${
        goal.isCompleted
          ? 'border-green-200 ring-4 ring-green-100'
          : 'border-gray-100 hover:border-purple-300'
      }`}>
        {/* Badge de Concluído */}
        {goal.isCompleted && (
          <div className="absolute top-4 left-4 z-10">
            <div className="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-semibold flex items-center gap-1 shadow-lg">
              <CheckCircle className="h-4 w-4" />
              Concluído
            </div>
          </div>
        )}

        {/* Header com Gradiente */}
        <div className={`relative h-48 ${
          goal.isCompleted
            ? 'bg-gradient-to-br from-green-500 via-emerald-500 to-teal-500'
            : 'bg-gradient-to-br from-purple-500 via-violet-500 to-indigo-500'
        }`}>
          {goal.imageUrl ? (
            <>
              <img
                src={goal.imageUrl}
                alt={goal.name}
                className={`w-full h-full object-cover ${goal.isCompleted ? 'opacity-80' : ''}`}
              />
              <div className={`absolute inset-0 ${
                goal.isCompleted
                  ? 'bg-gradient-to-t from-green-900/60 via-green-900/20 to-transparent'
                  : 'bg-gradient-to-t from-purple-900/60 via-purple-900/20 to-transparent'
              }`}></div>
            </>
          ) : (
            <div className="absolute inset-0 opacity-90"></div>
          )}

          {/* Ações no Header */}
          <div className="absolute top-4 right-4 flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <button
              onClick={() => setShowProjectionModal(true)}
              className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-white/30 rounded-xl transition-colors"
              title="Ver Projeção"
            >
              <TrendingUp className="h-4 w-4" />
            </button>
            <button
              onClick={handleCompleteGoal}
              className="p-2.5 bg-white/20 backdrop-blur-sm text-white hover:bg-green-500/80 rounded-xl transition-colors"
              title="Marcar como Concluído"
            >
              <CheckCircle className="h-4 w-4" />
            </button>
          </div>

          {/* Informações Principais */}
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
              <div className="flex items-center justify-between mb-3">
                <div>
                  <p className="text-white/80 text-sm font-medium">Meta Total</p>
                  <p className="text-white text-2xl font-bold">
                    {formatCurrency(goal.targetAmount)}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-white/80 text-sm font-medium">Prazo</p>
                  <p className="text-white text-lg font-semibold">
                    {getMonthName(goal.targetMonth)}/{goal.targetYear}
                  </p>
                </div>
              </div>
              
              {/* Progresso */}
              <div>
                <div className="flex items-center justify-between text-white/80 text-xs mb-2">
                  <span>Progresso</span>
                  <span>{completedItems.length}/{totalItems} itens</span>
                </div>
                <div className="w-full bg-white/20 rounded-full h-2">
                  <div 
                    className="bg-white h-2 rounded-full transition-all duration-300"
                    style={{ width: `${progressPercentage}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Conteúdo Principal */}
        <div className="p-6">
          {/* Título e Descrição */}
          <div className="mb-6">
            <h3 className="font-bold text-gray-900 text-xl mb-2 line-clamp-1">{goal.name}</h3>
            {goal.description && (
              <p className="text-gray-600 text-sm line-clamp-2 leading-relaxed">{goal.description}</p>
            )}
          </div>

          {/* Lista de Itens */}
          <div className="space-y-3 mb-6">
            {goal.subGoals?.slice(0, 3).map((item, index) => (
              <div key={index} className={`flex items-center justify-between p-3 rounded-xl transition-all ${
                item.isCompleted 
                  ? 'bg-green-50 border border-green-200' 
                  : 'bg-gray-50 hover:bg-gray-100'
              }`}>
                <div className="flex items-center gap-3 flex-1">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    item.isCompleted 
                      ? 'bg-green-500 text-white' 
                      : 'bg-yellow-400 text-gray-800'
                  }`}>
                    {item.isCompleted ? '✓' : index + 1}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className={`font-medium text-sm truncate ${
                      item.isCompleted ? 'text-green-800 line-through' : 'text-gray-900'
                    }`}>
                      {item.name}
                    </p>
                  </div>
                </div>
                
                <div className="relative">
                  <button
                    onClick={() => setShowItemActions(prev => ({
                      ...prev,
                      [item.id]: !prev[item.id]
                    }))}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <MoreVertical className="h-4 w-4" />
                  </button>
                  
                  {showItemActions[item.id] && (
                    <div className="absolute right-0 top-8 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-10 min-w-[120px]">
                      {!item.isCompleted && (
                        <button
                          onClick={() => {
                            handleCompleteItem(item)
                            setShowItemActions(prev => ({ ...prev, [item.id]: false }))
                          }}
                          className="w-full px-3 py-2 text-left text-sm text-green-600 hover:bg-green-50 flex items-center gap-2"
                        >
                          <CheckCircle className="h-3 w-3" />
                          Concluir
                        </button>
                      )}
                      <button
                        onClick={() => {
                          handleDeleteItem(item)
                          setShowItemActions(prev => ({ ...prev, [item.id]: false }))
                        }}
                        className="w-full px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50 flex items-center gap-2"
                      >
                        <Trash2 className="h-3 w-3" />
                        Excluir
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
            
            {totalItems > 3 && (
              <div className="text-center py-2">
                <span className="text-sm text-gray-500">
                  +{totalItems - 3} itens adicionais
                </span>
              </div>
            )}
          </div>

          {/* Botões de Ação */}
          <div className="grid grid-cols-2 gap-3">
            <button
              onClick={handleCompleteGoal}
              className="bg-gradient-to-r from-green-500 to-emerald-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-green-600 hover:to-emerald-600 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <CheckCircle className="h-4 w-4" />
              Concluir
            </button>
            <button
              onClick={() => setShowProjectionModal(true)}
              className="bg-gradient-to-r from-purple-500 to-violet-500 text-white font-semibold py-3 px-4 rounded-xl hover:from-purple-600 hover:to-violet-600 transition-all duration-300 flex items-center justify-center gap-2"
            >
              <TrendingUp className="h-4 w-4" />
              Projeção
            </button>
          </div>
        </div>
      </div>

      {/* Modal de Projeção */}
      <ComplexGoalProjectionModal
        goal={goal}
        isOpen={showProjectionModal}
        onClose={() => setShowProjectionModal(false)}
        onUpdate={onUpdate}
      />
    </>
  )
}

export default ComplexGoalCard
