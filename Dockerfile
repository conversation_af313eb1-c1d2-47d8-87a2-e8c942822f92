# 🐳 SARA - Dockerfile Simplificado
FROM node:18-alpine

# Instalar dependências do sistema
RUN apk add --no-cache \
    git \
    curl \
    bash \
    sqlite \
    supervisor \
    nginx \
    python3 \
    make \
    g++

# Criar diretórios
WORKDIR /app
RUN mkdir -p /app/backend /app/frontend /var/log/supervisor /etc/supervisor/conf.d /app/logs

# Copiar projeto completo
COPY . /app/

# Instalar dependências do backend
WORKDIR /app/backend
RUN npm install

# Gerar Prisma Client
RUN npx prisma generate

# Instalar dependências do frontend
WORKDIR /app/frontend
RUN npm install

# Build do frontend
RUN npm run build

# Voltar para diretório principal
WORKDIR /app

# Copiar configurações
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/entrypoint.sh /entrypoint.sh
COPY docker/auto-deploy.sh /auto-deploy.sh

# Tornar scripts executáveis
RUN chmod +x /entrypoint.sh /auto-deploy.sh

# Configurar variáveis de ambiente
ENV NODE_ENV=production
ENV PORT=3001
ENV DATABASE_URL="file:/app/backend/production.db"

# Expor portas
EXPOSE 80 3001

# Healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/health || exit 1

# Comando de inicialização
ENTRYPOINT ["/entrypoint.sh"]
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
