# 🐳 SARA - Dockerfile para Produção
# Multi-stage build para otimização

# ================================
# Stage 1: Build Frontend
# ================================
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend

# Copiar package files
COPY frontend/package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código fonte
COPY frontend/ ./

# Build da aplicação
RUN npm run build

# ================================
# Stage 2: Build Backend
# ================================
FROM node:18-alpine AS backend-builder

WORKDIR /app/backend

# Instalar dependências do sistema
RUN apk add --no-cache python3 make g++

# Copiar package files
COPY backend/package*.json ./

# Instalar dependências
RUN npm ci --only=production

# Copiar código fonte
COPY backend/ ./

# Gerar Prisma Client
RUN npx prisma generate

# ================================
# Stage 3: Production Runtime
# ================================
FROM node:18-alpine AS production

# Instalar dependências do sistema
RUN apk add --no-cache \
    git \
    curl \
    bash \
    sqlite \
    supervisor \
    nginx

# Criar usuário não-root
RUN addgroup -g 1001 -S nodejs && \
    adduser -S sara -u 1001

# Criar diretórios
WORKDIR /app
RUN mkdir -p /app/backend /app/frontend/dist /var/log/supervisor /etc/supervisor/conf.d

# Copiar backend buildado
COPY --from=backend-builder --chown=sara:nodejs /app/backend /app/backend

# Copiar frontend buildado
COPY --from=frontend-builder --chown=sara:nodejs /app/frontend/dist /app/frontend/dist

# Copiar arquivos de configuração
COPY docker/nginx.conf /etc/nginx/nginx.conf
COPY docker/supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY docker/entrypoint.sh /entrypoint.sh
COPY docker/auto-deploy.sh /auto-deploy.sh

# Tornar scripts executáveis
RUN chmod +x /entrypoint.sh /auto-deploy.sh

# Configurar variáveis de ambiente padrão
ENV NODE_ENV=production
ENV PORT=3001
ENV DATABASE_URL="file:/app/backend/production.db"
ENV FRONTEND_URL="http://localhost"

# Expor portas
EXPOSE 80 3001

# Configurar volumes
VOLUME ["/app/backend/production.db", "/app/logs"]

# Configurar healthcheck
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost/api/health || exit 1

# Usar usuário não-root
USER sara

# Comando de inicialização
ENTRYPOINT ["/entrypoint.sh"]
CMD ["supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
