const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware de autenticação para todas as rotas
router.use(authenticateToken);

// Verificar se existe configuração de relatório para o mês/ano
router.get('/config/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          include: {
            category: true
          }
        },
        tagMappings: {
          include: {
            tag: true
          }
        },
        transactionContactMappings: {
          include: {
            contact: true
          }
        },
        loanContactMappings: {
          include: {
            contact: true
          }
        },
        categoryExpectations: {
          include: {
            category: true
          }
        },
        tagExpectations: {
          include: {
            tag: true
          }
        }
      }
    });

    res.json({
      exists: !!config,
      config: config || null,
      isCompleted: config?.isCompleted || false
    });

  } catch (error) {
    console.error('Erro ao verificar configuração de relatório:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Passo 1: Salvar porcentagens dos limites de gastos
router.post('/config/:year/:month/step1', async (req, res) => {
  try {
    const { year, month } = req.params;
    const { expensePercentage, savingsPercentage, leisurePercentage } = req.body;
    const userId = req.user.id;

    // Validar que as porcentagens somam 100%
    const total = expensePercentage + savingsPercentage + leisurePercentage;
    if (Math.abs(total - 100) > 0.01) {
      return res.status(400).json({ 
        error: 'As porcentagens devem somar 100%',
        currentTotal: total
      });
    }

    // Criar ou atualizar configuração
    const config = await prisma.reportConfig.upsert({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      update: {
        expensePercentage,
        savingsPercentage,
        leisurePercentage
      },
      create: {
        userId,
        year: parseInt(year),
        month: parseInt(month),
        expensePercentage,
        savingsPercentage,
        leisurePercentage
      }
    });

    res.json({ success: true, config });

  } catch (error) {
    console.error('Erro no passo 1:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Passo 2: Salvar mapeamentos de categorias, tags e contatos
router.post('/config/:year/:month/step2', async (req, res) => {
  try {
    const { year, month } = req.params;
    const { mappings } = req.body; // { expense: {categories: [], tags: [], transactionContacts: [], loanContacts: []}, savings: {...}, leisure: {...} }
    const userId = req.user.id;

    // Buscar configuração existente
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      }
    });

    if (!config) {
      return res.status(404).json({ error: 'Configuração não encontrada. Complete o passo 1 primeiro.' });
    }

    // Limpar mapeamentos existentes
    await prisma.reportCategoryMapping.deleteMany({
      where: { reportConfigId: config.id }
    });
    await prisma.reportTagMapping.deleteMany({
      where: { reportConfigId: config.id }
    });
    await prisma.reportTransactionContactMapping.deleteMany({
      where: { reportConfigId: config.id }
    });
    await prisma.reportLoanContactMapping.deleteMany({
      where: { reportConfigId: config.id }
    });

    // Criar novos mapeamentos
    const categoryMappings = [];
    const tagMappings = [];
    const transactionContactMappings = [];
    const loanContactMappings = [];

    for (const [limitType, items] of Object.entries(mappings)) {
      const limitTypeUpper = limitType.toUpperCase();

      // Categorias
      if (items.categories) {
        for (const categoryId of items.categories) {
          categoryMappings.push({
            reportConfigId: config.id,
            categoryId,
            limitType: limitTypeUpper
          });
        }
      }

      // Tags
      if (items.tags) {
        for (const tagId of items.tags) {
          tagMappings.push({
            reportConfigId: config.id,
            tagId,
            limitType: limitTypeUpper
          });
        }
      }

      // Contatos de transações
      if (items.transactionContacts) {
        for (const contactId of items.transactionContacts) {
          transactionContactMappings.push({
            reportConfigId: config.id,
            contactId,
            limitType: limitTypeUpper
          });
        }
      }

      // Contatos de empréstimos
      if (items.loanContacts) {
        for (const contactId of items.loanContacts) {
          loanContactMappings.push({
            reportConfigId: config.id,
            contactId,
            limitType: limitTypeUpper
          });
        }
      }
    }

    // Inserir novos mapeamentos
    if (categoryMappings.length > 0) {
      await prisma.reportCategoryMapping.createMany({
        data: categoryMappings
      });
    }

    if (tagMappings.length > 0) {
      await prisma.reportTagMapping.createMany({
        data: tagMappings
      });
    }

    if (transactionContactMappings.length > 0) {
      await prisma.reportTransactionContactMapping.createMany({
        data: transactionContactMappings
      });
    }

    if (loanContactMappings.length > 0) {
      await prisma.reportLoanContactMapping.createMany({
        data: loanContactMappings
      });
    }

    res.json({ success: true });

  } catch (error) {
    console.error('Erro no passo 2:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Passo 3: Salvar valores esperados
router.post('/config/:year/:month/step3', async (req, res) => {
  try {
    const { year, month } = req.params;
    const { 
      expectedIncome, 
      expectedExpenses, 
      expectedSavings, 
      expectedLeisure,
      categoryExpectations,
      tagExpectations
    } = req.body;
    const userId = req.user.id;

    // Buscar configuração existente
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      }
    });

    if (!config) {
      return res.status(404).json({ error: 'Configuração não encontrada. Complete os passos anteriores primeiro.' });
    }

    // Atualizar configuração com valores esperados
    await prisma.reportConfig.update({
      where: { id: config.id },
      data: {
        expectedIncome: expectedIncome || null,
        expectedExpenses: expectedExpenses || null,
        expectedSavings: expectedSavings || null,
        expectedLeisure: expectedLeisure || null,
        isCompleted: true
      }
    });

    // Limpar expectativas existentes
    await prisma.reportCategoryExpectation.deleteMany({
      where: { reportConfigId: config.id }
    });
    await prisma.reportTagExpectation.deleteMany({
      where: { reportConfigId: config.id }
    });

    // Criar expectativas de categorias
    if (categoryExpectations && Object.keys(categoryExpectations).length > 0) {
      const categoryExpectationData = Object.entries(categoryExpectations)
        .filter(([_, amount]) => amount > 0)
        .map(([categoryId, expectedAmount]) => ({
          reportConfigId: config.id,
          categoryId,
          expectedAmount: parseFloat(expectedAmount)
        }));

      if (categoryExpectationData.length > 0) {
        await prisma.reportCategoryExpectation.createMany({
          data: categoryExpectationData
        });
      }
    }

    // Criar expectativas de tags
    if (tagExpectations && Object.keys(tagExpectations).length > 0) {
      const tagExpectationData = Object.entries(tagExpectations)
        .filter(([_, amount]) => amount > 0)
        .map(([tagId, expectedAmount]) => ({
          reportConfigId: config.id,
          tagId,
          expectedAmount: parseFloat(expectedAmount)
        }));

      if (tagExpectationData.length > 0) {
        await prisma.reportTagExpectation.createMany({
          data: tagExpectationData
        });
      }
    }

    res.json({ success: true, message: 'Configuração de relatório concluída!' });

  } catch (error) {
    console.error('Erro no passo 3:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Buscar dados para o cadastro (categorias, tags, contatos)
router.get('/setup-data', async (req, res) => {
  try {
    const userId = req.user.id;

    // Buscar todas as categorias do usuário
    const categories = await prisma.category.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });

    // Buscar todas as tags do usuário
    const tags = await prisma.tag.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });

    // Buscar contatos de transações
    const transactionContacts = await prisma.transactionContact.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });

    // Buscar contatos de empréstimos
    const loanContacts = await prisma.contact.findMany({
      where: { userId },
      orderBy: { name: 'asc' }
    });

    res.json({
      categories,
      tags,
      transactionContacts,
      loanContacts
    });

  } catch (error) {
    console.error('Erro ao buscar dados para setup:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Gerar análise do relatório baseado na configuração
router.get('/analysis/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar configuração do relatório
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          include: { category: true }
        },
        tagMappings: {
          include: { tag: true }
        },
        transactionContactMappings: {
          include: { contact: true }
        },
        loanContactMappings: {
          include: { contact: true }
        },
        categoryExpectations: {
          include: { category: true }
        },
        tagExpectations: {
          include: { tag: true }
        }
      }
    });

    if (!config || !config.isCompleted) {
      return res.status(404).json({
        error: 'Configuração de relatório não encontrada ou incompleta',
        needsSetup: true
      });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true
      }
    });

    // Calcular valores reais baseados na configuração
    const analysis = calculateReportAnalysis(config, transactions);

    res.json({
      config,
      analysis,
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao gerar análise do relatório:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função auxiliar para calcular análise do relatório
function calculateReportAnalysis(config, transactions) {
  // Organizar mapeamentos por tipo de limite
  const mappings = {
    EXPENSE: {
      categories: config.categoryMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.categoryId),
      tags: config.tagMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.tagId),
      transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.contactId),
      loanContacts: config.loanContactMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.contactId)
    },
    SAVINGS: {
      categories: config.categoryMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.categoryId),
      tags: config.tagMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.tagId),
      transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.contactId),
      loanContacts: config.loanContactMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.contactId)
    },
    LEISURE: {
      categories: config.categoryMappings.filter(m => m.limitType === 'LEISURE').map(m => m.categoryId),
      tags: config.tagMappings.filter(m => m.limitType === 'LEISURE').map(m => m.tagId),
      transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'LEISURE').map(m => m.contactId),
      loanContacts: config.loanContactMappings.filter(m => m.limitType === 'LEISURE').map(m => m.contactId)
    }
  };

  // Calcular totais por categoria
  let totalIncome = 0;
  let expenseAmount = 0;
  let savingsAmount = 0;
  let leisureAmount = 0;

  transactions.forEach(transaction => {
    if (transaction.type === 'INCOME') {
      totalIncome += transaction.amount;
      return;
    }

    // Verificar se a transação pertence a alguma categoria de limite
    const belongsToExpense = checkTransactionBelongs(transaction, mappings.EXPENSE);
    const belongsToSavings = checkTransactionBelongs(transaction, mappings.SAVINGS);
    const belongsToLeisure = checkTransactionBelongs(transaction, mappings.LEISURE);

    if (belongsToExpense) {
      expenseAmount += transaction.amount;
    } else if (belongsToSavings) {
      savingsAmount += transaction.amount;
    } else if (belongsToLeisure) {
      leisureAmount += transaction.amount;
    }
  });

  // Calcular porcentagens reais
  const baseIncome = config.expectedIncome || totalIncome;
  const expensePercentage = baseIncome > 0 ? (expenseAmount / baseIncome) * 100 : 0;
  const savingsPercentage = baseIncome > 0 ? (savingsAmount / baseIncome) * 100 : 0;
  const leisurePercentage = baseIncome > 0 ? (leisureAmount / baseIncome) * 100 : 0;

  // Calcular scores (0-100)
  const expenseScore = Math.max(0, 100 - Math.abs(expensePercentage - config.expensePercentage) * 2);
  const savingsScore = Math.max(0, 100 - Math.abs(savingsPercentage - config.savingsPercentage) * 2);
  const leisureScore = Math.max(0, 100 - Math.abs(leisurePercentage - config.leisurePercentage) * 2);
  const overallScore = (expenseScore + savingsScore + leisureScore) / 3;

  return {
    expected: {
      income: config.expectedIncome,
      expenses: config.expectedExpenses,
      savings: config.expectedSavings,
      leisure: config.expectedLeisure,
      expensePercentage: config.expensePercentage,
      savingsPercentage: config.savingsPercentage,
      leisurePercentage: config.leisurePercentage
    },
    actual: {
      income: totalIncome,
      expenses: expenseAmount,
      savings: savingsAmount,
      leisure: leisureAmount,
      expensePercentage,
      savingsPercentage,
      leisurePercentage
    },
    scores: {
      expense: expenseScore,
      savings: savingsScore,
      leisure: leisureScore,
      overall: overallScore
    },
    categoryExpectations: config.categoryExpectations,
    tagExpectations: config.tagExpectations
  };
}

// Função auxiliar para verificar se transação pertence a um tipo de limite
function checkTransactionBelongs(transaction, mapping) {
  // Verificar categoria
  if (transaction.categoryId && mapping.categories.includes(transaction.categoryId)) {
    return true;
  }

  // Verificar tags
  if (transaction.tags && transaction.tags.length > 0) {
    const transactionTagIds = transaction.tags.map(tag => tag.id);
    if (mapping.tags.some(tagId => transactionTagIds.includes(tagId))) {
      return true;
    }
  }

  // Verificar contato de transação
  if (transaction.transactionContactId && mapping.transactionContacts.includes(transaction.transactionContactId)) {
    return true;
  }

  return false;
}

// Endpoint específico: Renda Mensal
router.get('/income/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar transações de renda do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const incomeTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'INCOME',
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        // Excluir transações de controle de parcelas
        installmentStatus: {
          not: 'CONTROL'
        }
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true,
        bank: true
      },
      orderBy: { date: 'desc' }
    });

    const totalIncome = incomeTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    res.json({
      totalIncome,
      transactions: incomeTransactions,
      count: incomeTransactions.length,
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao buscar renda mensal:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Endpoint específico: Despesas Essenciais
router.get('/expenses/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar configuração do relatório
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          where: { limitType: 'EXPENSE' },
          include: { category: true }
        },
        tagMappings: {
          where: { limitType: 'EXPENSE' },
          include: { tag: true }
        },
        transactionContactMappings: {
          where: { limitType: 'EXPENSE' },
          include: { contact: true }
        },
        loanContactMappings: {
          where: { limitType: 'EXPENSE' },
          include: { contact: true }
        }
      }
    });

    if (!config || !config.isCompleted) {
      return res.status(404).json({
        error: 'Configuração de relatório não encontrada',
        needsSetup: true
      });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const expenseTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'EXPENSE',
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        // Excluir transações de controle de parcelas
        installmentStatus: {
          not: 'CONTROL'
        },
        OR: [
          // Por categoria
          {
            categoryId: {
              in: config.categoryMappings.map(m => m.categoryId)
            }
          },
          // Por tags
          {
            tags: {
              some: {
                id: {
                  in: config.tagMappings.map(m => m.tagId)
                }
              }
            }
          },
          // Por contato de transação
          {
            transactionContactId: {
              in: config.transactionContactMappings.map(m => m.contactId)
            }
          }
        ]
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true,
        bank: true
      },
      orderBy: { date: 'desc' }
    });

    const totalExpenses = expenseTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    // Buscar renda para calcular porcentagem
    const startOfMonthIncome = new Date(year, month - 1, 1);
    const endOfMonthIncome = new Date(year, month, 0, 23, 59, 59);

    const incomeTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'INCOME',
        date: {
          gte: startOfMonthIncome,
          lte: endOfMonthIncome
        },
        installmentStatus: {
          not: 'CONTROL'
        }
      }
    });

    const totalIncome = incomeTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    const percentage = totalIncome > 0 ? (totalExpenses / totalIncome) * 100 : 0;

    // Agrupar por categoria/tag/contato para detalhamento
    const breakdown = {
      byCategory: {},
      byTag: {},
      byContact: {}
    };

    expenseTransactions.forEach(transaction => {
      // Por categoria
      if (transaction.category) {
        const categoryName = transaction.category.name;
        if (!breakdown.byCategory[categoryName]) {
          breakdown.byCategory[categoryName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byCategory[categoryName].total += transaction.amount;
        breakdown.byCategory[categoryName].count += 1;
        breakdown.byCategory[categoryName].transactions.push(transaction);
      }

      // Por tags
      transaction.tags.forEach(tag => {
        const tagName = tag.name;
        if (!breakdown.byTag[tagName]) {
          breakdown.byTag[tagName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byTag[tagName].total += transaction.amount;
        breakdown.byTag[tagName].count += 1;
        breakdown.byTag[tagName].transactions.push(transaction);
      });

      // Por contato
      if (transaction.transactionContact) {
        const contactName = transaction.transactionContact.name;
        if (!breakdown.byContact[contactName]) {
          breakdown.byContact[contactName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byContact[contactName].total += transaction.amount;
        breakdown.byContact[contactName].count += 1;
        breakdown.byContact[contactName].transactions.push(transaction);
      }
    });

    res.json({
      totalExpenses,
      percentage,
      targetPercentage: config.expensePercentage,
      transactions: expenseTransactions,
      breakdown,
      count: expenseTransactions.length,
      config: {
        categories: config.categoryMappings.map(m => m.category),
        tags: config.tagMappings.map(m => m.tag),
        contacts: config.transactionContactMappings.map(m => m.contact)
      },
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao buscar despesas essenciais:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Endpoint específico: Poupança/Investimentos
router.get('/savings/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar configuração do relatório
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          where: { limitType: 'SAVINGS' },
          include: { category: true }
        },
        tagMappings: {
          where: { limitType: 'SAVINGS' },
          include: { tag: true }
        },
        transactionContactMappings: {
          where: { limitType: 'SAVINGS' },
          include: { contact: true }
        },
        loanContactMappings: {
          where: { limitType: 'SAVINGS' },
          include: { contact: true }
        }
      }
    });

    if (!config || !config.isCompleted) {
      return res.status(404).json({
        error: 'Configuração de relatório não encontrada',
        needsSetup: true
      });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    // Buscar INVESTMENT que estejam mapeadas como SAVINGS
    const investimentsTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'INVESTMENT', // Poupança pode ser tanto despesa quanto investiment,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        // Excluir transações de controle de parcelas
        installmentStatus: {
          not: 'CONTROL'
        }
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true,
        bank: true
      },
      orderBy: { date: 'desc' }
    });

    console.log(investimentsTransactions, startOfMonth, endOfMonth);

    // Buscar tanto EXPENSE  que estejam mapeadas como SAVINGS
    savingsTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: {
          in: ['EXPENSE'] // Poupança pode ser tanto despesa quanto investimento
        },
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        // Excluir transações de controle de parcelas
        installmentStatus: {
          not: 'CONTROL'
        },
        OR: [
          // Por categoria
          {
            categoryId: {
              in: config.categoryMappings.map(m => m.categoryId)
            }
          },
          // Por tags
          {
            tags: {
              some: {
                id: {
                  in: config.tagMappings.map(m => m.tagId)
                }
              }
            }
          },
          // Por contato de transação
          {
            transactionContactId: {
              in: config.transactionContactMappings.map(m => m.contactId)
            }
          }
        ]
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true,
        bank: true
      },
      orderBy: { date: 'desc' }
    });

    savingsTransactions = savingsTransactions.concat(investimentsTransactions);

    const totalSavings = savingsTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    // Buscar renda para calcular porcentagem
    const startOfMonthIncome = new Date(year, month - 1, 1);
    const endOfMonthIncome = new Date(year, month, 0, 23, 59, 59);

    const incomeTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'INCOME',
        date: {
          gte: startOfMonthIncome,
          lte: endOfMonthIncome
        },
        installmentStatus: {
          not: 'CONTROL'
        }
      }
    });

    const totalIncome = incomeTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    const percentage = totalIncome > 0 ? (totalSavings / totalIncome) * 100 : 0;

    // Agrupar por categoria/tag/contato para detalhamento
    const breakdown = {
      byCategory: {},
      byTag: {},
      byContact: {}
    };

    savingsTransactions.forEach(transaction => {
      // Por categoria
      if (transaction.category) {
        const categoryName = transaction.category.name;
        if (!breakdown.byCategory[categoryName]) {
          breakdown.byCategory[categoryName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byCategory[categoryName].total += transaction.amount;
        breakdown.byCategory[categoryName].count += 1;
        breakdown.byCategory[categoryName].transactions.push(transaction);
      }

      // Por tags
      transaction.tags.forEach(tag => {
        const tagName = tag.name;
        if (!breakdown.byTag[tagName]) {
          breakdown.byTag[tagName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byTag[tagName].total += transaction.amount;
        breakdown.byTag[tagName].count += 1;
        breakdown.byTag[tagName].transactions.push(transaction);
      });

      // Por contato
      if (transaction.transactionContact) {
        const contactName = transaction.transactionContact.name;
        if (!breakdown.byContact[contactName]) {
          breakdown.byContact[contactName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byContact[contactName].total += transaction.amount;
        breakdown.byContact[contactName].count += 1;
        breakdown.byContact[contactName].transactions.push(transaction);
      }
    });

    res.json({
      totalSavings,
      percentage,
      targetPercentage: config.savingsPercentage,
      transactions: savingsTransactions,
      breakdown,
      count: savingsTransactions.length,
      config: {
        categories: config.categoryMappings.map(m => m.category),
        tags: config.tagMappings.map(m => m.tag),
        contacts: config.transactionContactMappings.map(m => m.contact)
      },
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao buscar poupança/investimentos:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Endpoint específico: Lazer
router.get('/leisure/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar configuração do relatório
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          where: { limitType: 'LEISURE' },
          include: { category: true }
        },
        tagMappings: {
          where: { limitType: 'LEISURE' },
          include: { tag: true }
        },
        transactionContactMappings: {
          where: { limitType: 'LEISURE' },
          include: { contact: true }
        },
        loanContactMappings: {
          where: { limitType: 'LEISURE' },
          include: { contact: true }
        }
      }
    });

    if (!config || !config.isCompleted) {
      return res.status(404).json({
        error: 'Configuração de relatório não encontrada',
        needsSetup: true
      });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const leisureTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'EXPENSE', // Lazer são sempre despesas
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        // Excluir transações de controle de parcelas
        installmentStatus: {
          not: 'CONTROL'
        },
        OR: [
          // Por categoria
          {
            categoryId: {
              in: config.categoryMappings.map(m => m.categoryId)
            }
          },
          // Por tags
          {
            tags: {
              some: {
                id: {
                  in: config.tagMappings.map(m => m.tagId)
                }
              }
            }
          },
          // Por contato de transação
          {
            transactionContactId: {
              in: config.transactionContactMappings.map(m => m.contactId)
            }
          }
        ]
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true,
        bank: true
      },
      orderBy: { date: 'desc' }
    });

    const totalLeisure = leisureTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    // Buscar renda para calcular porcentagem
    const startOfMonthIncome = new Date(year, month - 1, 1);
    const endOfMonthIncome = new Date(year, month, 0, 23, 59, 59);

    const incomeTransactions = await prisma.transaction.findMany({
      where: {
        userId,
        type: 'INCOME',
        date: {
          gte: startOfMonthIncome,
          lte: endOfMonthIncome
        },
        installmentStatus: {
          not: 'CONTROL'
        }
      }
    });

    const totalIncome = incomeTransactions.reduce((sum, transaction) => sum + transaction.amount, 0);

    const percentage = totalIncome > 0 ? (totalLeisure / totalIncome) * 100 : 0;

    // Agrupar por categoria/tag/contato para detalhamento
    const breakdown = {
      byCategory: {},
      byTag: {},
      byContact: {}
    };

    leisureTransactions.forEach(transaction => {
      // Por categoria
      if (transaction.category) {
        const categoryName = transaction.category.name;
        if (!breakdown.byCategory[categoryName]) {
          breakdown.byCategory[categoryName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byCategory[categoryName].total += transaction.amount;
        breakdown.byCategory[categoryName].count += 1;
        breakdown.byCategory[categoryName].transactions.push(transaction);
      }

      // Por tags
      transaction.tags.forEach(tag => {
        const tagName = tag.name;
        if (!breakdown.byTag[tagName]) {
          breakdown.byTag[tagName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byTag[tagName].total += transaction.amount;
        breakdown.byTag[tagName].count += 1;
        breakdown.byTag[tagName].transactions.push(transaction);
      });

      // Por contato
      if (transaction.transactionContact) {
        const contactName = transaction.transactionContact.name;
        if (!breakdown.byContact[contactName]) {
          breakdown.byContact[contactName] = { total: 0, count: 0, transactions: [] };
        }
        breakdown.byContact[contactName].total += transaction.amount;
        breakdown.byContact[contactName].count += 1;
        breakdown.byContact[contactName].transactions.push(transaction);
      }
    });

    res.json({
      totalLeisure,
      percentage,
      targetPercentage: config.leisurePercentage,
      transactions: leisureTransactions,
      breakdown,
      count: leisureTransactions.length,
      config: {
        categories: config.categoryMappings.map(m => m.category),
        tags: config.tagMappings.map(m => m.tag),
        contacts: config.transactionContactMappings.map(m => m.contact)
      },
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao buscar gastos com lazer:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Endpoint para gráfico de gastos esperados vs reais
router.get('/expected-vs-actual/:year/:month', async (req, res) => {
  try {
    const { year, month } = req.params;
    const userId = req.user.id;

    // Buscar configuração do relatório
    const config = await prisma.reportConfig.findUnique({
      where: {
        userId_year_month: {
          userId,
          year: parseInt(year),
          month: parseInt(month)
        }
      },
      include: {
        categoryMappings: {
          include: { category: true }
        },
        tagMappings: {
          include: { tag: true }
        },
        transactionContactMappings: {
          include: { contact: true }
        },
        loanContactMappings: {
          include: { contact: true }
        },
        categoryExpectations: {
          include: { category: true }
        },
        tagExpectations: {
          include: { tag: true }
        }
      }
    });

    if (!config || !config.isCompleted) {
      return res.status(404).json({
        error: 'Configuração de relatório não encontrada',
        needsSetup: true
      });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        },
        installmentStatus: {
          not: 'CONTROL'
        }
      },
      include: {
        category: true,
        tags: true,
        transactionContact: true
      }
    });

    // Calcular valores reais por tipo
    const actualValues = {
      expenses: 0,
      savings: 0,
      leisure: 0,
      categories: {},
      tags: {}
    };

    // Organizar mapeamentos por tipo
    const mappings = {
      EXPENSE: {
        categories: config.categoryMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.categoryId),
        tags: config.tagMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.tagId),
        transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'EXPENSE').map(m => m.contactId)
      },
      SAVINGS: {
        categories: config.categoryMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.categoryId),
        tags: config.tagMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.tagId),
        transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'SAVINGS').map(m => m.contactId)
      },
      LEISURE: {
        categories: config.categoryMappings.filter(m => m.limitType === 'LEISURE').map(m => m.categoryId),
        tags: config.tagMappings.filter(m => m.limitType === 'LEISURE').map(m => m.tagId),
        transactionContacts: config.transactionContactMappings.filter(m => m.limitType === 'LEISURE').map(m => m.contactId)
      }
    };

    // Processar transações
    transactions.forEach(transaction => {
      if (transaction.type === 'INCOME') return;

      // Verificar a qual tipo pertence
      let belongsToType = null;

      // Verificar por categoria
      if (transaction.categoryId) {
        if (mappings.EXPENSE.categories.includes(transaction.categoryId)) {
          belongsToType = 'EXPENSE';
        } else if (mappings.SAVINGS.categories.includes(transaction.categoryId)) {
          belongsToType = 'SAVINGS';
        } else if (mappings.LEISURE.categories.includes(transaction.categoryId)) {
          belongsToType = 'LEISURE';
        }
      }

      // Verificar por tags se não encontrou por categoria
      if (!belongsToType && transaction.tags && transaction.tags.length > 0) {
        const transactionTagIds = transaction.tags.map(tag => tag.id);
        if (mappings.EXPENSE.tags.some(tagId => transactionTagIds.includes(tagId))) {
          belongsToType = 'EXPENSE';
        } else if (mappings.SAVINGS.tags.some(tagId => transactionTagIds.includes(tagId))) {
          belongsToType = 'SAVINGS';
        } else if (mappings.LEISURE.tags.some(tagId => transactionTagIds.includes(tagId))) {
          belongsToType = 'LEISURE';
        }
      }

      // Verificar por contato se não encontrou
      if (!belongsToType && transaction.transactionContactId) {
        if (mappings.EXPENSE.transactionContacts.includes(transaction.transactionContactId)) {
          belongsToType = 'EXPENSE';
        } else if (mappings.SAVINGS.transactionContacts.includes(transaction.transactionContactId)) {
          belongsToType = 'SAVINGS';
        } else if (mappings.LEISURE.transactionContacts.includes(transaction.transactionContactId)) {
          belongsToType = 'LEISURE';
        }
      }

      // Adicionar ao total do tipo
      if (belongsToType === 'EXPENSE') {
        actualValues.expenses += transaction.amount;
      } else if (belongsToType === 'SAVINGS') {
        actualValues.savings += transaction.amount;
      } else if (belongsToType === 'LEISURE') {
        actualValues.leisure += transaction.amount;
      }

      // Adicionar por categoria
      if (transaction.category) {
        const categoryName = transaction.category.name;
        if (!actualValues.categories[categoryName]) {
          actualValues.categories[categoryName] = 0;
        }
        actualValues.categories[categoryName] += transaction.amount;
      }

      // Adicionar por tags
      transaction.tags.forEach(tag => {
        const tagName = tag.name;
        if (!actualValues.tags[tagName]) {
          actualValues.tags[tagName] = 0;
        }
        actualValues.tags[tagName] += transaction.amount;
      });
    });

    // Preparar dados para o gráfico
    const chartData = {
      main: [
        {
          name: 'Despesas Essenciais',
          expected: config.expectedExpenses || 0,
          actual: actualValues.expenses,
          type: 'expenses'
        },
        {
          name: 'Poupança/Investimentos',
          expected: config.expectedSavings || 0,
          actual: actualValues.savings,
          type: 'savings'
        },
        {
          name: 'Lazer',
          expected: config.expectedLeisure || 0,
          actual: actualValues.leisure,
          type: 'leisure'
        }
      ],
      categories: config.categoryExpectations.map(expectation => ({
        name: expectation.category.name,
        expected: expectation.expectedAmount,
        actual: actualValues.categories[expectation.category.name] || 0,
        type: 'category'
      })),
      tags: config.tagExpectations.map(expectation => ({
        name: expectation.tag.name,
        expected: expectation.expectedAmount,
        actual: actualValues.tags[expectation.tag.name] || 0,
        type: 'tag'
      }))
    };

    res.json({
      chartData,
      config,
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao buscar dados esperados vs reais:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Exportar relatório de transações
router.post('/export', async (req, res) => {
  try {
    const { year, month, format } = req.body;
    const userId = req.user.id;

    if (!year || !month || !format) {
      return res.status(400).json({ error: 'Ano, mês e formato são obrigatórios' });
    }

    if (!['PDF', 'XLSX', 'XLS', 'CSV'].includes(format)) {
      return res.status(400).json({ error: 'Formato inválido' });
    }

    // Buscar transações do período
    const startDate = new Date(year, month - 1, 1);
    const endDate = new Date(year, month, 0, 23, 59, 59);

    const transactions = await prisma.transaction.findMany({
      where: {
        userId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        category: true,
        bank: true,
        paymentMethod: true,
        transactionContact: true,
        tags: true
      },
      orderBy: {
        date: 'desc'
      }
    });

    // Preparar dados para exportação
    const exportData = transactions.map(transaction => ({
      data: transaction.date.toLocaleDateString('pt-BR'),
      hora: transaction.date.toLocaleTimeString('pt-BR'),
      descricao: transaction.description,
      tipo: getTransactionTypeLabel(transaction.type),
      valor: transaction.amount,
      categoria: transaction.category?.name || 'Sem categoria',
      banco: transaction.bank?.name || 'Sem banco',
      metodoPagamento: transaction.paymentMethod?.name || 'Sem método',
      contato: transaction.transactionContact?.name || 'Sem contato',
      tags: transaction.tags?.map(tag => tag.name).join(', ') || 'Sem tags',
      parcelas: transaction.installments > 1 ? `${transaction.currentInstallment}/${transaction.installments}` : '1/1',
      status: transaction.isPaid ? 'Pago' : 'Pendente',
      observacoes: transaction.notes || ''
    }));

    // Gerar arquivo baseado no formato
    switch (format) {
      case 'PDF':
        return await generatePDF(res, exportData, year, month);
      case 'XLSX':
      case 'XLS':
        return await generateExcel(res, exportData, year, month, format);
      case 'CSV':
        return await generateCSV(res, exportData, year, month);
      default:
        return res.status(400).json({ error: 'Formato não suportado' });
    }

  } catch (error) {
    console.error('Erro ao exportar relatório:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Função para gerar CSV
async function generateCSV(res, data, year, month) {
  const filename = `relatorio-transacoes-${year}-${month.toString().padStart(2, '0')}.csv`;

  res.setHeader('Content-Type', 'text/csv; charset=utf-8');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

  // BOM para UTF-8
  res.write('\uFEFF');

  // Cabeçalhos
  const headers = [
    'Data', 'Hora', 'Descrição', 'Tipo', 'Valor', 'Categoria',
    'Banco', 'Método Pagamento', 'Contato', 'Tags', 'Parcelas', 'Status', 'Observações'
  ];
  res.write(headers.join(';') + '\n');

  // Dados
  data.forEach(transaction => {
    const row = [
      transaction.data,
      transaction.hora,
      `"${transaction.descricao}"`,
      transaction.tipo,
      transaction.valor.toString().replace('.', ','),
      `"${transaction.categoria}"`,
      `"${transaction.banco}"`,
      `"${transaction.metodoPagamento}"`,
      `"${transaction.contato}"`,
      `"${transaction.tags}"`,
      transaction.parcelas,
      transaction.status,
      `"${transaction.observacoes}"`
    ];
    res.write(row.join(';') + '\n');
  });

  res.end();
}

// Função para gerar Excel (simulação - retorna CSV por enquanto)
async function generateExcel(res, data, year, month, format) {
  // Por enquanto, vamos retornar CSV com extensão Excel
  const filename = `relatorio-transacoes-${year}-${month.toString().padStart(2, '0')}.${format.toLowerCase()}`;

  res.setHeader('Content-Type', 'application/vnd.ms-excel');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

  // BOM para UTF-8
  res.write('\uFEFF');

  // Cabeçalhos
  const headers = [
    'Data', 'Hora', 'Descrição', 'Tipo', 'Valor', 'Categoria',
    'Banco', 'Método Pagamento', 'Contato', 'Tags', 'Parcelas', 'Status', 'Observações'
  ];
  res.write(headers.join('\t') + '\n');

  // Dados
  data.forEach(transaction => {
    const row = [
      transaction.data,
      transaction.hora,
      transaction.descricao,
      transaction.tipo,
      transaction.valor.toString().replace('.', ','),
      transaction.categoria,
      transaction.banco,
      transaction.metodoPagamento,
      transaction.contato,
      transaction.tags,
      transaction.parcelas,
      transaction.status,
      transaction.observacoes
    ];
    res.write(row.join('\t') + '\n');
  });

  res.end();
}

// Função para gerar PDF (simulação - retorna texto por enquanto)
async function generatePDF(res, data, year, month) {
  const filename = `relatorio-transacoes-${year}-${month.toString().padStart(2, '0')}.pdf`;

  res.setHeader('Content-Type', 'application/pdf');
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

  // Simulação de PDF - retorna texto simples por enquanto
  const content = `SARA - Sistema de Acompanhamento de Recursos e Aplicações
Relatório de Transações - ${getMonthName(month)}/${year}

RESUMO FINANCEIRO:
Total de Receitas: R$ ${data.filter(t => t.tipo === 'Receita').reduce((sum, t) => sum + t.valor, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
Total de Despesas: R$ ${data.filter(t => t.tipo === 'Despesa').reduce((sum, t) => sum + t.valor, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}
Total de Investimentos: R$ ${data.filter(t => t.tipo === 'Investimento').reduce((sum, t) => sum + t.valor, 0).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}

DETALHAMENTO DAS TRANSAÇÕES:
${data.map((t, i) => `${i + 1}. ${t.data} - ${t.descricao} - ${t.tipo} - R$ ${t.valor.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`).join('\n')}
`;

  res.write(content);
  res.end();
}

// Funções auxiliares
function getTransactionTypeLabel(type) {
  const types = {
    'INCOME': 'Receita',
    'EXPENSE': 'Despesa',
    'INVESTMENT': 'Investimento',
    'LOAN': 'Empréstimo'
  };
  return types[type] || type;
}

function getMonthName(month) {
  const months = [
    'Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho',
    'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'
  ];
  return months[month - 1];
}

module.exports = router;
