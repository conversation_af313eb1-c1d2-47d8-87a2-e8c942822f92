import api from './api'

const tagService = {
  // Listar todas as tags
  async getTags() {
    const response = await api.get('/tags')
    return response.data
  },

  // Buscar tag específica
  async getTag(id) {
    const response = await api.get(`/tags/${id}`)
    return response.data
  },

  // Criar nova tag
  async createTag(tagData) {
    const response = await api.post('/tags', tagData)
    return response.data
  },

  // Atualizar tag
  async updateTag(id, tagData) {
    const response = await api.put(`/tags/${id}`, tagData)
    return response.data
  },

  // Deletar tag
  async deleteTag(id) {
    const response = await api.delete(`/tags/${id}`)
    return response.data
  }
}

export default tagService
