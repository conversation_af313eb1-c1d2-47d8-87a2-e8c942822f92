# 🎯 SARA - Fluxo de Telas e Funcionalidades

## 📊 Diagrama de Fluxo Principal

```mermaid
graph TD
    A[🔐 Login] --> B[📊 Dashboard]
    
    B --> C[💰 Transações]
    B --> D[📈 Relatórios]
    B --> E[🏦 Bancos]
    B --> F[💸 Empréstimos]
    B --> G[🎯 Objetivos]
    B --> H[⚙️ Configurações]
    
    C --> C1[📝 Nova Transação]
    C --> C2[🔄 Rotinas]
    C --> C3[📋 Templates]
    C --> C4[👥 Contatos]
    
    D --> D1[📊 Relatórios Mensais]
    D --> D2[🎯 Objetivos]
    D --> D3[📤 Import/Export]
    
    E --> E1[➕ Novo Banco]
    E --> E2[💳 Cartões de Crédito]
    E --> E3[🧾 Faturas]
    
    F --> F1[💰 Novo Empréstimo]
    F --> F2[💵 Parcelas]
    F --> F3[👤 Contatos de Empréstimo]
    
    G --> G1[🎯 Objetivo Simples]
    G --> G2[🎯 Objetivo Complexo]
    G --> G3[📈 Projeções]
    
    H --> H1[👤 Perfil]
    H --> H2[🏷️ Categorias]
    H --> H3[🔖 Tags]
    H --> H4[📅 Simulador de Data]
```

## 🔐 1. Tela de Login

### Funcionalidades:
- **Autenticação**: Email e senha
- **Usuário padrão**: <EMAIL> / 123456
- **Validação**: Campos obrigatórios
- **Redirecionamento**: Para Dashboard após login

### Fluxo:
```
Login → Validação → JWT Token → Dashboard
```

---

## 📊 2. Dashboard (Página Inicial)

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 🏠 SARA Dashboard                           👤 Usuário │
├─────────────────────────────────────────────────────────┤
│ 💰 Saldo Total    📈 Receitas    📉 Despesas    💎 Invest│
├─────────────────────────────────────────────────────────┤
│ 📊 Gráfico de Transações (Últimos 6 meses)             │
├─────────────────────────────────────────────────────────┤
│ 🏦 Bancos         💸 Empréstimos    🎯 Objetivos        │
├─────────────────────────────────────────────────────────┤
│ 📋 Transações Recentes                                 │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Cards de Resumo**: Saldos, receitas, despesas, investimentos
- **Gráfico Temporal**: Evolução financeira dos últimos 6 meses
- **Resumo de Bancos**: Saldos e limites disponíveis
- **Empréstimos Ativos**: Status e valores pendentes
- **Objetivos**: Progresso das metas financeiras
- **Transações Recentes**: Últimas 10 transações

---

## 💰 3. Transações

### 3.1 Lista de Transações

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 💰 Transações                    🔍 Filtros  ➕ Nova   │
├─────────────────────────────────────────────────────────┤
│ 📅 Jun/2024    💰 R$ 15.000    📊 150 transações       │
├─────────────────────────────────────────────────────────┤
│ 🔍 [Busca] 📅[Data] 🏷️[Categoria] 🔖[Tag] 🏦[Banco]   │
├─────────────────────────────────────────────────────────┤
│ Data    │ Descrição      │ Categoria │ Valor    │ Ações │
│ 15/06   │ Salário        │ 💼 Renda  │ +5.000   │ ⚙️    │
│ 14/06   │ Supermercado   │ 🛒 Compras│ -250     │ ⚙️    │
│ 13/06   │ Investimento   │ 📈 Invest │ -1.000   │ ⚙️    │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Filtros Avançados**: Data, categoria, tag, banco, tipo, valor
- **Busca Textual**: Por descrição
- **Paginação**: Carregamento otimizado
- **Ações**: Editar, excluir, duplicar
- **Exportação**: PDF, Excel, CSV
- **Importação**: Planilhas com validação

### 3.2 Nova Transação

### Modal Layout:
```
┌─────────────────────────────────────────────────────────┐
│ ➕ Nova Transação                               ✖️      │
├─────────────────────────────────────────────────────────┤
│ 🔘 Receita  🔘 Despesa  🔘 Investimento                │
├─────────────────────────────────────────────────────────┤
│ 📝 Descrição: [_________________________]              │
│ 💰 Valor: [R$ _______]  📅 Data: [__/__/____]          │
│ 🏦 Banco: [Dropdown ▼]  💳 Forma Pgto: [Dropdown ▼]   │
│ 🏷️ Categoria: [Dropdown ▼]  🔖 Tags: [Seleção ▼]      │
│ 👤 Contato: [Dropdown ▼]  📎 Comprovante: [Upload]     │
│ 💳 Parcelamento: [1x ▼]  📝 Observações: [_______]     │
├─────────────────────────────────────────────────────────┤
│                                    [Cancelar] [Salvar] │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Tipos**: Receita, Despesa, Investimento
- **Parcelamento**: Cartão de crédito com ciclo de faturamento
- **Upload**: Comprovantes via Cloudinary
- **Contatos**: Integração com sistema de contatos
- **Validações**: Campos obrigatórios e formatos
- **Duplicação**: Criar transação similar

### 3.3 Rotinas de Transação

### Funcionalidades:
- **Agendamento**: Transações recorrentes
- **Frequência**: Diária, semanal, mensal, anual
- **Pausar/Retomar**: Controle de execução
- **Histórico**: Transações geradas
- **Notificações**: Lembretes de vencimento

### 3.4 Templates de Transação

### Funcionalidades:
- **Modelos**: Transações pré-configuradas
- **Execução Rápida**: Um clique para criar
- **Personalização**: Editar antes de salvar
- **Categorização**: Organização por tipo

### 3.5 Contatos de Transação

### Funcionalidades:
- **CRUD Completo**: Criar, editar, excluir
- **Informações**: Nome, email, telefone, PIX
- **Foto**: Upload via Cloudinary
- **Estatísticas**: Transações por contato
- **Filtros**: Busca e organização

---

## 📈 4. Relatórios

### 4.1 Aba Relatórios

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 📈 Relatórios    🎯 Objetivos    📤 Import/Export       │
├─────────────────────────────────────────────────────────┤
│ 📅 Jun/2024                              ⚙️ Configurar │
├─────────────────────────────────────────────────────────┤
│ 💰 Receitas: R$ 5.000  📉 Despesas: R$ 3.500  💎 Inv.. │
├─────────────────────────────────────────────────────────┤
│ 🟢 Esverdeômetro: 85% - Muito Bom!                     │
├─────────────────────────────────────────────────────────┤
│ 📊 [Gráfico de Pizza - Categorias]                     │
│ 📈 [Gráfico de Linha - Evolução Temporal]              │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Configuração**: Metas percentuais (50% despesas, 20% investimentos, 30% lazer)
- **Esverdeômetro**: Indicador visual de saúde financeira
- **Gráficos**: Pizza (categorias), linha (evolução), barras (comparativo)
- **Filtros Inteligentes**: Por categoria, tag, período
- **Exportação**: Relatórios em PDF

### 4.2 Aba Objetivos

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 🎯 Objetivos Financeiros                    ➕ Novo    │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐        │
│ │ 🏠 Casa     │ │ 🚗 Carro    │ │ 🏖️ Viagem   │        │
│ │ R$ 200.000  │ │ R$ 50.000   │ │ R$ 10.000   │        │
│ │ ████████░░  │ │ ██████░░░░  │ │ ███░░░░░░░  │        │
│ │ 80% - Dez/25│ │ 60% - Jun/25│ │ 30% - Mar/25│        │
│ │ [📈] [✅]   │ │ [📈] [✅]   │ │ [📈] [✅]   │        │
│ └─────────────┘ └─────────────┘ └─────────────┘        │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Objetivos Simples**: Meta única com valor e data
- **Objetivos Complexos**: Múltiplos sub-objetivos
- **Projeções**: Análise de viabilidade baseada no histórico
- **Progresso Visual**: Barras de progresso e percentuais
- **Imagens**: Upload personalizado para cada objetivo

### 4.3 Aba Import/Export

### Funcionalidades:
- **Importação**: Excel/CSV com validação e preview
- **Exportação**: Relatórios completos em múltiplos formatos
- **Templates**: Modelos para importação
- **Validação**: Verificação de dados antes da importação

---

## 🏦 5. Bancos

### 5.1 Lista de Bancos

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 🏦 Bancos                                    ➕ Novo   │
├─────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐                │
│ │ 🟣 Nubank       │ │ 🟠 Inter        │                │
│ │ Conta Corrente  │ │ Conta Poupança  │                │
│ │ R$ 2.500,00     │ │ R$ 15.000,00    │                │
│ │ Limite: R$ 5.000│ │ Sem limite      │                │
│ │ [💳] [📊] [⚙️] │ │ [💳] [📊] [⚙️] │                │
│ └─────────────────┘ └─────────────────┘                │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Tipos**: Conta corrente, poupança, investimento
- **Saldos**: Atualizados em tempo real
- **Limites**: Controle de crédito
- **Ícones**: Personalizados por banco
- **Cores**: Identidade visual por instituição

### 5.2 Cartões de Crédito

### Funcionalidades:
- **Faturamento**: Ciclo automático
- **Limites**: Disponível e utilizado
- **Parcelas**: Controle inteligente
- **Faturas**: Histórico e pagamento

### 5.3 Gestão de Faturas

### Funcionalidades:
- **Status**: Pendente, paga, vencida
- **Processamento**: Automático por data
- **Parcelas**: Movimentação entre faturas
- **Pagamento**: Integração com saldo bancário

---

## 💸 6. Empréstimos

### 6.1 Lista de Empréstimos

### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 💸 Empréstimos                              ➕ Novo    │
├─────────────────────────────────────────────────────────┤
│ 📤 Emprestado    📥 Recebido                            │
├─────────────────────────────────────────────────────────┤
│ 👤 João Silva                                           │
│ 💰 R$ 5.000,00 → R$ 2.000,00 restante                  │
│ ████████████░░░░░░░░ 60%                                │
│ 📅 Vence: 15/07/2024  💵 Parcela: R$ 500,00            │
│ [💰] [📋] [⚙️]                                          │
└─────────────────────────────────────────────────────────┘
```

### Funcionalidades:
- **Tipos**: Empréstimo dado, empréstimo recebido
- **Parcelas**: Dinâmicas com controle de pagamento
- **Progresso**: Visual baseado em valores pagos
- **Contatos**: Integração com sistema de contatos
- **Transações**: Automáticas para cada operação

### 6.2 Contatos de Empréstimo

### Funcionalidades:
- **Status**: Bom, neutro, ruim (baseado em atrasos)
- **Histórico**: Todos os empréstimos por contato
- **Estatísticas**: Em dia vs atrasados
- **Filtros**: Por status e período

---

## 🎯 7. Objetivos (Detalhado)

### 7.1 Objetivos Simples

### Funcionalidades:
- **Meta única**: Valor e data específicos
- **Categorias/Tags**: Relacionamento opcional
- **Projeção**: Análise de viabilidade
- **Validação**: Mínimo 3 meses no futuro

### 7.2 Objetivos Complexos

### Funcionalidades:
- **Sub-objetivos**: Múltiplos itens
- **Sem restrições**: Data flexível
- **Progresso**: Baseado em conclusão de itens
- **Gerenciamento**: Adicionar/editar/remover itens pós-criação

### 7.3 Projeções

### Funcionalidades:
- **Análise histórica**: Baseada em dados reais
- **Capacidade de poupança**: Cálculo automático
- **Recomendações**: Valor mensal sugerido
- **Cenários**: Otimista e conservador

---

## ⚙️ 8. Configurações

### 8.1 Perfil do Usuário

### Funcionalidades:
- **Dados pessoais**: Nome, email, foto
- **Preferências**: Tema, idioma
- **Segurança**: Alterar senha

### 8.2 Categorias

### Funcionalidades:
- **CRUD**: Criar, editar, excluir
- **Cores**: Personalização visual
- **Ícones**: Biblioteca de ícones
- **Hierarquia**: Categorias e subcategorias

### 8.3 Tags

### Funcionalidades:
- **Sistema flexível**: Etiquetas livres
- **Cores**: Personalização
- **Filtros**: Busca e organização

### 8.4 Simulador de Data

### Funcionalidades:
- **Testes**: Simular datas para desenvolvimento
- **Atalhos**: Amanhã, próxima semana, próximo mês
- **Faturamento**: Testar ciclos de cobrança

---

## 🔄 Fluxos de Integração

### Transação → Banco
```
Nova Transação → Atualiza Saldo → Verifica Limites → Notifica
```

### Parcelamento → Fatura
```
Compra Parcelada → Primeira Parcela (Fatura Atual) → Demais (Reservadas) → Processamento Mensal
```

### Empréstimo → Transação
```
Novo Empréstimo → Transação Automática → Atualiza Saldo → Cria Parcelas
```

### Objetivo → Projeção
```
Meta Definida → Análise Histórica → Cálculo de Viabilidade → Recomendações
```

---

## 📱 Responsividade

Todas as telas são **100% responsivas** com:
- **Desktop**: Layout completo com sidebars
- **Tablet**: Layout adaptado com navegação otimizada
- **Mobile**: Interface touch-friendly com menus colapsáveis

---

## 🎨 Design System

- **Cores**: Paleta consistente com identidade visual
- **Tipografia**: Hierarquia clara e legível
- **Ícones**: Lucide React para consistência
- **Componentes**: Reutilizáveis e modulares
- **Animações**: Transições suaves e feedback visual
