const { PrismaClient } = require('@prisma/client');
const cron = require('node-cron');

const prisma = new PrismaClient();

class BillUpdateService {
  constructor() {
    this.isRunning = false;
    this.lastRunDate = null;
    this.scheduledTask = null;
  }

  /**
   * Inicializar o serviço de atualização de faturas
   */
  async initialize() {
    console.log('🔄 Inicializando serviço de atualização de faturas...');
    
    // Executar imediatamente na inicialização
    await this.runDailyBillUpdate();
    
    // Agendar execução diária às 00:00
    this.scheduledTask = cron.schedule('0 0 * * *', async () => {
      console.log('⏰ Executando atualização automática de faturas (00:00)');
      await this.runDailyBillUpdate();
    }, {
      scheduled: true,
      timezone: "America/Sao_Paulo"
    });

    console.log('✅ Serviço de atualização de faturas inicializado');
  }

  /**
   * Método principal de atualização diária de faturas
   */
  async runDailyBillUpdate() {
    if (this.isRunning) {
      console.log('⚠️ Atualização de faturas já está em execução, pulando...');
      return;
    }

    try {
      this.isRunning = true;
      const startTime = new Date();
      console.log(`🚀 Iniciando atualização de faturas - ${startTime.toLocaleString()}`);

      // Buscar todos os bancos com limite de crédito
      const banks = await prisma.bank.findMany({
        where: {
          creditLimit: { gt: 0 },
          billDueDay: { not: null }
        },
        include: {
          user: true,
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      console.log(`📊 Processando ${banks.length} bancos com cartão de crédito`);

      for (const bank of banks) {
        try {
          await this.processBankBills(bank);
        } catch (error) {
          console.error(`❌ Erro ao processar banco ${bank.name} (${bank.id}):`, error);
        }
      }

      // Processar assinaturas separadamente (Etapa 3)
      await this.processSubscriptionBilling();

      this.lastRunDate = new Date();
      const endTime = new Date();
      const duration = endTime - startTime;
      
      console.log(`✅ Atualização de faturas concluída em ${duration}ms`);
      console.log(`📅 Última execução: ${this.lastRunDate.toLocaleString()}`);

    } catch (error) {
      console.error('❌ Erro na atualização de faturas:', error);
    } finally {
      this.isRunning = false;
    }
  }

  /**
   * Processar faturas de um banco específico
   */
  async processBankBills(bank) {
    console.log(`\n🏦 Processando banco: ${bank.name} (${bank.id})`);
    
    const currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0); // Zerar horas para comparação precisa

    // Buscar fatura atual do banco
    const currentBill = await this.getCurrentBill(bank.id, bank.userId);
    
    if (!currentBill) {
      console.log(`   ⚠️ Nenhuma fatura encontrada para o banco ${bank.name}`);
      return;
    }

    const billDueDate = new Date(currentBill.dueDate);
    billDueDate.setHours(0, 0, 0, 0);

    console.log(`   📋 Fatura atual: ${currentBill.status}, Vencimento: ${billDueDate.toLocaleDateString()}`);
    console.log(`   📅 Data atual: ${currentDate.toLocaleDateString()}`);

    // Calcular valor atual da fatura
    const billAmount = await this.calculateCurrentBillAmount(bank.id, bank.userId);
    
    // Atualizar valor da fatura
    await prisma.bill.update({
      where: { id: currentBill.id },
      data: { amount: billAmount }
    });

    console.log(`   💰 Valor da fatura: R$ ${billAmount.toFixed(2)}`);

    // Etapa 1: Verificar se fatura venceu e tem valor > 0
    if (currentDate > billDueDate && currentBill.status === 'PENDING' && billAmount > 0) {
      console.log(`   🚨 Fatura vencida com valor > 0 - Alterando para OVERDUE`);
      
      await prisma.bill.update({
        where: { id: currentBill.id },
        data: { status: 'OVERDUE' }
      });

      // Criar notificação de fatura vencida
      await this.createOverdueNotification(bank.id, bank.userId, billAmount, bank.name);
      
      console.log(`   ✅ Fatura marcada como OVERDUE`);
    }
    
    // Etapa 2: Verificar se fatura venceu e tem valor <= 0
    else if (currentDate >= billDueDate && currentBill.status === 'PENDING' && billAmount <= 0) {
      console.log(`   ✅ Fatura vencida com valor <= 0 - Encerrando ciclo`);
      
      await this.closeBillCycle(bank, currentBill, billAmount);
    }
  }

  /**
   * Encerrar ciclo de fatura e criar nova
   */
  async closeBillCycle(bank, currentBill, finalAmount) {
    await prisma.$transaction(async (tx) => {
      console.log(`   📝 Salvando fatura no histórico...`);
      
      // Salvar no histórico (mesmo com valor 0)
      await tx.billHistory.create({
        data: {
          bankId: bank.id,
          amount: finalAmount,
          dueDate: currentBill.dueDate,
          paidDate: new Date(),
          paymentBankId: bank.id, // Considerando como "pago automaticamente"
          userId: bank.userId
        }
      });

      // Marcar fatura atual como PAID
      await tx.bill.update({
        where: { id: currentBill.id },
        data: {
          status: 'PAID',
          paidDate: new Date(),
          paymentBankId: bank.id
        }
      });

      // Criar nova fatura para o próximo mês
      const nextDueDate = new Date(currentBill.dueDate);
      nextDueDate.setMonth(nextDueDate.getMonth() + 1);

      const newBill = await tx.bill.create({
        data: {
          bankId: bank.id,
          userId: bank.userId,
          dueDate: nextDueDate,
          amount: 0,
          status: 'PENDING'
        }
      });

      console.log(`   ✅ Nova fatura criada para ${nextDueDate.toLocaleDateString()}`);

      // Processar impactos nos parcelamentos
      await this.processInstallmentImpacts(bank, nextDueDate, tx);

      // Processar impactos nas assinaturas (BILLED → RESERVED)
      await this.processSubscriptionImpacts(bank.id, bank.userId, tx);
    });
  }

  /**
   * Processar impactos nos parcelamentos após criação de nova fatura
   */
  async processInstallmentImpacts(bank, newBillDueDate, tx) {
    console.log(`   🔄 Processando impactos nos parcelamentos...`);

    // Calcular range de datas para CURRENT_BILL
    const billCloseDate = new Date(newBillDueDate);
    billCloseDate.setDate(billCloseDate.getDate() - 1); // Dia anterior ao vencimento

    const rangeStart = new Date(billCloseDate);
    rangeStart.setMonth(rangeStart.getMonth() - 1);
    rangeStart.setDate(rangeStart.getDate() + 1); // Dia seguinte ao fechamento anterior

    const rangeEnd = billCloseDate;

    console.log(`   📅 Range para CURRENT_BILL: ${rangeStart.toLocaleDateString()} a ${rangeEnd.toLocaleDateString()}`);

    // Buscar transações RESERVED que devem virar CURRENT_BILL
    const reservedTransactions = await tx.transaction.findMany({
      where: {
        paymentMethod: {
          bankId: bank.id,
          type: 'CREDIT'
        },
        installmentStatus: 'RESERVED',
        date: {
          gte: rangeStart,
          lte: rangeEnd
        },
        userId: bank.userId
      }
    });

    console.log(`   📊 ${reservedTransactions.length} transações RESERVED encontradas no range`);

    // Atualizar status para CURRENT_BILL e descontar do limite
    let totalToDeduct = 0;
    for (const transaction of reservedTransactions) {
      await tx.transaction.update({
        where: { id: transaction.id },
        data: { installmentStatus: 'CURRENT_BILL' }
      });
      //totalToDeduct += transaction.amount;
      console.log(`     ✅ ${transaction.description}: R$ ${transaction.amount} (RESERVED → CURRENT_BILL)`);
    }

    // Descontar do limite disponível
    if (totalToDeduct > 0) {
      await tx.bank.update({
        where: { id: bank.id },
        data: {
          availableLimit: {
            decrement: totalToDeduct
          }
        }
      });
      console.log(`   💳 Limite reduzido em R$ ${totalToDeduct.toFixed(2)}`);
    }
  }

  /**
   * Processar impactos nas assinaturas (BILLED → RESERVED)
   */
  async processSubscriptionImpacts(bankId, userId, tx) {
    console.log(`   🔄 Processando impactos nas assinaturas...`);

    // Buscar assinaturas BILLED para alterar para RESERVED
    const billedSubscriptions = await tx.subscription.findMany({
      where: {
        paymentMethod: {
          bankId,
          type: 'CREDIT'
        },
        status: 'BILLED',
        isActive: true,
        userId
      }
    });

    console.log(`   📊 ${billedSubscriptions.length} assinaturas BILLED encontradas`);

    // Alterar status para RESERVED
    for (const subscription of billedSubscriptions) {
      await tx.subscription.update({
        where: { id: subscription.id },
        data: { status: 'RESERVED' }
      });
      console.log(`     ✅ ${subscription.name}: BILLED → RESERVED`);
    }
  }

  /**
   * Processar cobrança de assinaturas (Etapa 3)
   */
  async processSubscriptionBilling() {
    console.log(`\n💳 Processando cobrança de assinaturas...`);

    const currentDate = new Date();
    const currentDay = currentDate.getDate();

    // Buscar assinaturas RESERVED que devem ser cobradas hoje
    const subscriptionsToCharge = await prisma.subscription.findMany({
      where: {
        status: 'RESERVED',
        isActive: true,
        billingDay: { lte: currentDay }
      },
      include: {
        paymentMethod: {
          include: { bank: true }
        }
      }
    });

    console.log(`   📊 ${subscriptionsToCharge.length} assinaturas para cobrança encontradas`);

    for (const subscription of subscriptionsToCharge) {
      try {
        await this.chargeSubscription(subscription);
      } catch (error) {
        console.error(`   ❌ Erro ao cobrar assinatura ${subscription.name}:`, error);
      }
    }
  }

  /**
   * Cobrar uma assinatura específica
   */
  async chargeSubscription(subscription) {
    const bank = subscription.paymentMethod.bank;

    console.log(`   💰 Cobrando assinatura: ${subscription.name} (R$ ${subscription.amount})`);

    // Verificar se há limite disponível
    if (bank.availableLimit < subscription.amount) {
      console.log(`   ⚠️ Limite insuficiente para ${subscription.name} (Disponível: R$ ${bank.availableLimit}, Necessário: R$ ${subscription.amount})`);
      return;
    }

    await prisma.$transaction(async (tx) => {
      // Alterar status para CURRENT_BILL
      await tx.subscription.update({
        where: { id: subscription.id },
        data: { status: 'CURRENT_BILL' }
      });

      // Descontar do limite disponível
      await tx.bank.update({
        where: { id: bank.id },
        data: {
          availableLimit: {
            decrement: subscription.amount
          }
        }
      });

      console.log(`   ✅ ${subscription.name}: RESERVED → CURRENT_BILL (R$ ${subscription.amount} descontado do limite)`);
    });
  }

  /**
   * Obter fatura atual de um banco
   */
  async getCurrentBill(bankId, userId) {
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();

    // Buscar banco para obter dia de vencimento
    const bank = await prisma.bank.findFirst({
      where: { id: bankId, userId }
    });

    if (!bank || !bank.billDueDay) {
      return null;
    }

    // Calcular data de vencimento da fatura atual
    let billDueDate = new Date(currentYear, currentMonth, bank.billDueDay);

    // Se já passou do dia de vencimento deste mês, a fatura atual vence no próximo mês
    if (currentDate.getDate() > bank.billDueDay) {
      billDueDate.setMonth(billDueDate.getMonth() + 1);
    }

    // Buscar fatura existente
    const bill = await prisma.bill.findFirst({
      where: {
        bankId,
        userId,
        dueDate: {
          gte: new Date(billDueDate.getFullYear(), billDueDate.getMonth(), billDueDate.getDate()),
          lt: new Date(billDueDate.getFullYear(), billDueDate.getMonth(), billDueDate.getDate() + 1)
        }
      }
    });

    return bill;
  }

  /**
   * Calcular valor atual da fatura
   */
  async calculateCurrentBillAmount(bankId, userId) {
    try {
      // Buscar transações CURRENT_BILL
      const transactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: 'CURRENT_BILL',
          userId
        }
      });

      const transactionsTotal = transactions.reduce((sum, t) => sum + t.amount, 0);

      // Buscar assinaturas CURRENT_BILL
      const subscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: 'CURRENT_BILL',
          isActive: true,
          userId
        }
      });

      const subscriptionsTotal = subscriptions.reduce((sum, s) => sum + s.amount, 0);

      return transactionsTotal + subscriptionsTotal;
    } catch (error) {
      console.error('❌ Erro ao calcular valor da fatura:', error);
      return 0;
    }
  }

  /**
   * Criar notificação de fatura vencida
   */
  async createOverdueNotification(bankId, userId, amount, bankName) {
    try {
      await prisma.notification.create({
        data: {
          title: 'Fatura em Atraso',
          message: `A fatura do ${bankName} está em atraso. Valor: R$ ${amount.toFixed(2)}`,
          type: 'OVERDUE_BILL',
          priority: 'HIGH',
          amount: amount,
          entityId: bankId,
          entityType: 'bill',
          userId
        }
      });
    } catch (error) {
      console.error('❌ Erro ao criar notificação:', error);
    }
  }

  /**
   * Executar atualização manual (para chamadas da interface)
   */
  async runManualUpdate() {
    console.log('🔄 Executando atualização manual de faturas...');
    await this.runDailyBillUpdate();
  }

  /**
   * Obter status do serviço
   */
  getStatus() {
    return {
      isRunning: this.isRunning,
      lastRunDate: this.lastRunDate,
      isScheduled: this.scheduledTask ? this.scheduledTask.running : false
    };
  }

  /**
   * Parar o serviço
   */
  stop() {
    if (this.scheduledTask) {
      this.scheduledTask.stop();
      console.log('⏹️ Serviço de atualização de faturas parado');
    }
  }
}

// Criar instância única do serviço
const billUpdateService = new BillUpdateService();

module.exports = billUpdateService;
