import React, { useState, useEffect } from 'react'
import { Target, DollarSign, Save, Settings, X } from 'lucide-react'
import toast from 'react-hot-toast'
import api from '../services/api'

const GoalSettings = ({ isOpen, onClose, onGoalUpdated }) => {
  const [goal, setGoal] = useState({
    expensePercentage: 50,
    savingsPercentage: 20,
    leisurePercentage: 30,
    monthlyIncome: ''
  })
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  // Carregar meta atual
  useEffect(() => {
    if (isOpen) {
      loadCurrentGoal()
    }
  }, [isOpen])

  const loadCurrentGoal = async () => {
    try {
      setLoading(true)
      const response = await api.get('/financial-goals')
      setGoal({
        expensePercentage: response.data.expensePercentage,
        savingsPercentage: response.data.savingsPercentage,
        leisurePercentage: response.data.leisurePercentage,
        monthlyIncome: response.data.monthlyIncome || ''
      })
    } catch (error) {
      console.error('Erro ao carregar meta:', error)
      toast.error('Erro ao carregar meta financeira')
    } finally {
      setLoading(false)
    }
  }

  const handlePercentageChange = (field, value) => {
    const numValue = parseFloat(value) || 0
    
    setGoal(prev => {
      const newGoal = { ...prev, [field]: numValue }
      
      // Ajustar automaticamente as outras porcentagens para somar 100%
      const total = newGoal.expensePercentage + newGoal.savingsPercentage + newGoal.leisurePercentage
      
      if (total !== 100) {
        // Distribuir a diferença proporcionalmente entre os outros campos
        const otherFields = Object.keys(newGoal).filter(key => 
          key.includes('Percentage') && key !== field
        )
        
        const remaining = 100 - numValue
        const otherTotal = otherFields.reduce((sum, key) => sum + newGoal[key], 0)
        
        if (otherTotal > 0) {
          otherFields.forEach(key => {
            newGoal[key] = (newGoal[key] / otherTotal) * remaining
          })
        } else {
          // Se outros campos são 0, distribuir igualmente
          const valuePerField = remaining / otherFields.length
          otherFields.forEach(key => {
            newGoal[key] = valuePerField
          })
        }
      }
      
      return newGoal
    })
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      // Validar que soma 100%
      const total = goal.expensePercentage + goal.savingsPercentage + goal.leisurePercentage
      if (Math.abs(total - 100) > 0.01) {
        toast.error('As porcentagens devem somar 100%')
        return
      }

      const payload = {
        expensePercentage: goal.expensePercentage,
        savingsPercentage: goal.savingsPercentage,
        leisurePercentage: goal.leisurePercentage,
        monthlyIncome: goal.monthlyIncome ? parseFloat(goal.monthlyIncome) : null
      }

      await api.post('/financial-goals', payload)
      
      toast.success('Meta financeira salva com sucesso!')
      onGoalUpdated?.()
      onClose()
      
    } catch (error) {
      console.error('Erro ao salvar meta:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar meta financeira')
    } finally {
      setSaving(false)
    }
  }

  const totalPercentage = goal.expensePercentage + goal.savingsPercentage + goal.leisurePercentage

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5 text-blue-600" />
            <h2 className="text-xl font-semibold text-gray-800">Configurar Metas</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Carregando...</p>
          </div>
        ) : (
          <>
            {/* Renda Mensal */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Renda Mensal Esperada (opcional)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="number"
                  value={goal.monthlyIncome}
                  onChange={(e) => setGoal(prev => ({ ...prev, monthlyIncome: e.target.value }))}
                  placeholder="Ex: 5000"
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Se não informado, usaremos sua renda real do mês
              </p>
            </div>

            {/* Porcentagens */}
            <div className="space-y-4 mb-6">
              {/* Despesas Essenciais */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Despesas Essenciais: {goal.expensePercentage.toFixed(1)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="0.1"
                  value={goal.expensePercentage}
                  onChange={(e) => handlePercentageChange('expensePercentage', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #ef4444 0%, #ef4444 ${goal.expensePercentage}%, #e5e7eb ${goal.expensePercentage}%, #e5e7eb 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Moradia, alimentação, transporte</span>
                </div>
              </div>

              {/* Poupança/Investimentos */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Poupança/Investimentos: {goal.savingsPercentage.toFixed(1)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="0.1"
                  value={goal.savingsPercentage}
                  onChange={(e) => handlePercentageChange('savingsPercentage', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #10b981 0%, #10b981 ${goal.savingsPercentage}%, #e5e7eb ${goal.savingsPercentage}%, #e5e7eb 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Reserva de emergência, investimentos</span>
                </div>
              </div>

              {/* Lazer */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Lazer: {goal.leisurePercentage.toFixed(1)}%
                </label>
                <input
                  type="range"
                  min="0"
                  max="100"
                  step="0.1"
                  value={goal.leisurePercentage}
                  onChange={(e) => handlePercentageChange('leisurePercentage', e.target.value)}
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  style={{
                    background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${goal.leisurePercentage}%, #e5e7eb ${goal.leisurePercentage}%, #e5e7eb 100%)`
                  }}
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Entretenimento, viagens, restaurantes</span>
                </div>
              </div>
            </div>

            {/* Total */}
            <div className={`p-3 rounded-lg mb-6 ${
              Math.abs(totalPercentage - 100) < 0.01 
                ? 'bg-green-50 border border-green-200' 
                : 'bg-yellow-50 border border-yellow-200'
            }`}>
              <div className="flex items-center justify-between">
                <span className="font-medium text-gray-700">Total:</span>
                <span className={`font-bold ${
                  Math.abs(totalPercentage - 100) < 0.01 
                    ? 'text-green-600' 
                    : 'text-yellow-600'
                }`}>
                  {totalPercentage.toFixed(1)}%
                </span>
              </div>
              {Math.abs(totalPercentage - 100) > 0.01 && (
                <p className="text-xs text-yellow-600 mt-1">
                  Ajuste as porcentagens para somar 100%
                </p>
              )}
            </div>

            {/* Botões */}
            <div className="flex gap-3">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
              >
                Cancelar
              </button>
              <button
                onClick={handleSave}
                disabled={saving || Math.abs(totalPercentage - 100) > 0.01}
                className="flex-1 flex items-center justify-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {saving ? 'Salvando...' : 'Salvar Meta'}
              </button>
            </div>
          </>
        )}
      </div>
    </div>
  )
}

export default GoalSettings
