import React from 'react'
import { X, CreditCard, Calendar, DollarSign, CheckCircle, Clock } from 'lucide-react'

function InstallmentModal({ isOpen, onClose, transaction, installments = [] }) {
  if (!isOpen || !transaction) return null

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR')
  }

  const paidInstallments = installments.filter(inst => 
    new Date(inst.date) <= new Date()
  ).length

  const remainingInstallments = transaction.installments - paidInstallments

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-12 h-12 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                <CreditCard className="h-6 w-6" />
              </div>
              <div>
                <h2 className="text-xl font-bold">Detalhes do Parcelamento</h2>
                <p className="text-blue-100">{transaction.description}</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white hover:bg-opacity-20 rounded-lg transition-colors"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 max-h-[calc(90vh-200px)] overflow-y-auto">
          {/* Resumo Geral */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-green-50 border border-green-200 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-green-600 font-medium">Valor Total</p>
                  <p className="text-lg font-bold text-green-800">
                    {formatCurrency(transaction.amount)}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-blue-600 font-medium">Parcelas Pagas</p>
                  <p className="text-lg font-bold text-blue-800">
                    {paidInstallments} de {transaction.installments}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-orange-50 border border-orange-200 rounded-xl p-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                  <Clock className="h-5 w-5 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-orange-600 font-medium">Restantes</p>
                  <p className="text-lg font-bold text-orange-800">
                    {remainingInstallments} parcelas
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Informações da Transação */}
          <div className="bg-gray-50 rounded-xl p-4 mb-6">
            <h3 className="font-semibold text-gray-900 mb-3">Informações da Transação</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Data de Criação:</span>
                <span className="ml-2 font-medium">{formatDate(transaction.createdAt || transaction.date)}</span>
              </div>
              <div>
                <span className="text-gray-600">Valor por Parcela:</span>
                <span className="ml-2 font-medium">{formatCurrency(transaction.amount)}</span>
              </div>
              {transaction.category && (
                <div>
                  <span className="text-gray-600">Categoria:</span>
                  <span className="ml-2 font-medium">
                    {transaction.category.icon} {transaction.category.name}
                  </span>
                </div>
              )}
              {transaction.paymentMethod && (
                <div>
                  <span className="text-gray-600">Cartão:</span>
                  <span className="ml-2 font-medium">
                    {transaction.paymentMethod.icon} {transaction.paymentMethod.name}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Lista de Parcelas */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">Cronograma de Parcelas</h3>
            <div className="space-y-3">
              {installments.map((installment, index) => {
                const instalmentStatus = installment.installmentStatus
                const isPaid = instalmentStatus === 'BANK_BALANCE' || instalmentStatus === 'PAID' || instalmentStatus === 'BILLED'
                const isCurrentMonth = instalmentStatus === 'CURRENT_BILL'
                
                return (
                  <div
                    key={installment.id || index}
                    className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all ${
                     isPaid
                        ? 'bg-green-50 border-green-200' 
                        : isCurrentMonth
                        ? 'bg-blue-50 border-blue-200'
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                        isPaid 
                          ? 'bg-green-500 text-white' 
                          : isCurrentMonth
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-300 text-gray-600'
                      }`}>
                        {isPaid ? (
                          <CheckCircle className="h-4 w-4" />
                        ) : (
                          <span className="text-xs font-bold">{index + 1}</span>
                        )}
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">
                          Parcela {index + 1} de {transaction.installments}
                        </p>
                        <p className="text-sm text-gray-600">
                          Vencimento: {formatDate(installment.date)}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(installment.amount)}
                      </p>
                      <p className={`text-xs font-medium ${
                        isPaid 
                          ? 'text-green-600' 
                          : isCurrentMonth
                          ? 'text-blue-600'
                          : 'text-gray-500'
                      }`}>
                        {isPaid ? 'Paga' : isCurrentMonth ? 'Vence este mês' : 'Pendente'}
                      </p>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Progresso */}
          <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-green-50 rounded-xl">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Progresso do Pagamento</span>
              <span className="text-sm font-bold text-gray-900">
                {Math.round((paidInstallments / transaction.installments) * 100)}%
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div
                className="bg-gradient-to-r from-blue-500 to-green-500 h-3 rounded-full transition-all duration-500"
                style={{ width: `${(paidInstallments / transaction.installments) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-xs text-gray-600 mt-1">
              <span>{paidInstallments} pagas</span>
              <span>{remainingInstallments} restantes</span>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <div className="flex justify-end">
            <button
              onClick={onClose}
              className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              Fechar
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default InstallmentModal
