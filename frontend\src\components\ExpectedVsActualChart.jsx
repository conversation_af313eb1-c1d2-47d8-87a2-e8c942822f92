import React, { useState, useEffect } from 'react'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
  Cell
} from 'recharts'
import { TrendingUp, TrendingDown, PiggyBank, Coffee, Folder, Tag, BarChart3, Target, Award, AlertTriangle } from 'lucide-react'
import api from '../services/api'
import toast from 'react-hot-toast'

const ExpectedVsActualChart = ({ year, month }) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('main')

  useEffect(() => {
    if (year && month) {
      fetchData()
    }
  }, [year, month])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/reports/expected-vs-actual/${year}/${month}`)
      setData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados esperados vs reais:', error)
      if (error.response?.data?.needsSetup) {
        // Não mostrar erro se precisar de setup
        setData(null)
      } else {
        toast.error('Erro ao carregar gráfico de comparação')
      }
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      const expected = payload.find(p => p.dataKey === 'expected')?.value || 0
      const actual = payload.find(p => p.dataKey === 'actual')?.value || 0
      const difference = expected - actual 
      const percentage = expected > 0 ? (difference / expected) * 100 : 0

      return (
        <div className="bg-white p-4 border border-gray-200 rounded-xl shadow-xl">
          <p className="font-semibold text-gray-900 mb-3 text-center">{label}</p>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Esperado:</span>
              <span className="font-medium text-blue-600">{formatCurrency(expected)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600">Real:</span>
              <span className="font-medium text-green-600">{formatCurrency(actual)}</span>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-gray-500">Diferença:</span>
                <span className={`text-xs font-medium ${
                  difference < 0 ? 'text-red-600' : 'text-blue-600'
                }`}>
                  {difference >= 0 ? '+' : ''}{formatCurrency(difference)}
                </span>
              </div>
              {expected > 0 && (
                <div className="flex justify-between items-center mt-1">
                  <span className="text-xs text-gray-500">Variação:</span>
                  <span className={`text-xs font-medium ${
                    Math.abs(percentage) <= 10 ? 'text-green-600' :
                    percentage < 0 ? 'text-red-600' : 'text-blue-600'
                  }`}>
                    {percentage >= 0 ? '+' : ''}{percentage.toFixed(1)}%
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )
    }
    return null
  }

  const getCardData = (item) => {
    const difference = item.expected - item.actual
    const actualPercentage = item.expected > 0 ? (item.actual / item.expected) * 100 : 0
    const variationPercentage = item.expected > 0 ? (difference / item.expected) * 100 : 0

    let status = 'neutral'
    let statusText = 'Dentro do esperado'
    let statusColor = 'text-gray-600'
    let message = ''

    // Lógica específica por tipo
    if (item.type === 'expenses') {
      // Despesas Essenciais: menos é melhor
      if (actualPercentage < 70) {
        status = 'excellent'
        statusText = 'Gastos controlados'
        statusColor = 'text-green-600'
        message = '🎉 Excelente controle de gastos!'
      } else if (actualPercentage >= 70 && actualPercentage < 90) {
        status = 'warning'
        statusText = 'Atenção aos gastos'
        statusColor = 'text-orange-600'
        message = '⚠️ Cuidado para não ultrapassar o orçamento'
      } else if (actualPercentage >= 90) {
        status = 'danger'
        statusText = 'Orçamento comprometido'
        statusColor = 'text-red-600'
        message = '🚨 Gastos muito altos! Revise seu orçamento'
      }
    } else if (item.type === 'savings') {
      // Poupança/Investimentos: mais é melhor
      if (actualPercentage >= 100) {
        status = 'excellent'
        statusText = 'Meta superada!'
        statusColor = 'text-green-600'
        message = '🎉 Parabéns! Você superou sua meta de poupança!'
      } else if (actualPercentage >= 90) {
        status = 'good'
        statusText = 'Quase lá!'
        statusColor = 'text-green-600'
        message = '✨ Muito bem! Você está quase concluindo a meta'
      } else if (actualPercentage >= 50 && actualPercentage < 70) {
        status = 'warning'
        statusText = 'Progresso moderado'
        statusColor = 'text-yellow-600'
        message = '💪 Você está quase concluindo a meta, continue assim!'
      } else if (actualPercentage >= 20 && actualPercentage < 50) {
        status = 'attention'
        statusText = 'Precisa melhorar'
        statusColor = 'text-orange-600'
        message = '📈 Aumente seus investimentos para atingir a meta'
      } else {
        status = 'danger'
        statusText = 'Meta distante'
        statusColor = 'text-red-600'
        message = '🎯 Foque mais em poupança e investimentos'
      }
    } else if (item.type === 'leisure') {
      // Lazer: equilíbrio é importante
      if (actualPercentage >= 80 && actualPercentage <= 120) {
        status = 'good'
        statusText = 'Equilíbrio perfeito'
        statusColor = 'text-green-600'
        message = '🎯 Ótimo equilíbrio entre lazer e responsabilidade'
      } else if (actualPercentage < 30) {
        status = 'attention'
        statusText = 'Pouco lazer'
        statusColor = 'text-orange-600'
        message = '😊 Que tal gastar um pouco mais consigo mesmo? Você merece!'
      } else if (actualPercentage > 120) {
        status = 'warning'
        statusText = 'Gastos altos'
        statusColor = 'text-orange-600'
        message = '⚖️ Cuidado para não comprometer outras metas'
      } else {
        status = 'neutral'
        statusText = `${actualPercentage.toFixed(1)}% da meta`
        statusColor = 'text-gray-600'
      }
    } else {
      // Lógica padrão para outros tipos
      if (Math.abs(variationPercentage) <= 10) {
        status = 'good'
        statusText = 'Dentro da meta'
        statusColor = 'text-green-600'
      } else if (difference > 0) {
        status = 'over'
        statusText = `${Math.abs(variationPercentage).toFixed(1)}% acima`
        statusColor = 'text-red-600'
      } else {
        status = 'under'
        statusText = `${Math.abs(variationPercentage).toFixed(1)}% abaixo`
        statusColor = 'text-blue-600'
      }
    }

    return {
      difference,
      variationPercentage,
      actualPercentage,
      status,
      statusText,
      statusColor,
      message
    }
  }

  const renderCards = (items, type) => {
    if (!items || items.length === 0) return null

    const getIcon = (type, itemType) => {
      if (type === 'main') {
        switch (itemType) {
          case 'expenses': return <TrendingDown className="h-6 w-6 text-red-500" />
          case 'savings': return <PiggyBank className="h-6 w-6 text-green-500" />
          case 'leisure': return <Coffee className="h-6 w-6 text-blue-500" />
          default: return <BarChart3 className="h-6 w-6 text-gray-500" />
        }
      } else if (type === 'categories') {
        return <Folder className="h-5 w-5 text-purple-500" />
      } else {
        return <Tag className="h-5 w-5 text-indigo-500" />
      }
    }

    const getStatusIcon = (status) => {
      switch (status) {
        case 'excellent': return <Award className="h-4 w-4 text-green-600" />
        case 'good': return <Award className="h-4 w-4 text-green-600" />
        case 'warning': return <AlertTriangle className="h-4 w-4 text-orange-600" />
        case 'attention': return <AlertTriangle className="h-4 w-4 text-orange-600" />
        case 'danger': return <AlertTriangle className="h-4 w-4 text-red-600" />
        case 'over': return <AlertTriangle className="h-4 w-4 text-red-600" />
        case 'under': return <Target className="h-4 w-4 text-blue-600" />
        default: return null
      }
    }

    return (
      <div className={`grid gap-6 ${type === 'main' ? 'grid-cols-1 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
        {items.map((item, index) => {
          const cardData = getCardData(item)

          return (
            <div key={index} className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 hover:shadow-lg ${
              cardData.status === 'excellent' ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' :
              cardData.status === 'good' ? 'border-green-200 bg-gradient-to-br from-green-50 to-emerald-50' :
              cardData.status === 'warning' ? 'border-orange-200 bg-gradient-to-br from-orange-50 to-amber-50' :
              cardData.status === 'attention' ? 'border-orange-200 bg-gradient-to-br from-orange-50 to-amber-50' :
              cardData.status === 'danger' ? 'border-red-200 bg-gradient-to-br from-red-50 to-pink-50' :
              cardData.status === 'over' ? 'border-red-200 bg-gradient-to-br from-red-50 to-pink-50' :
              cardData.status === 'under' ? 'border-blue-200 bg-gradient-to-br from-blue-50 to-indigo-50' :
              'border-gray-200 bg-gradient-to-br from-gray-50 to-slate-50'
            }`}>
              {/* Header */}
              <div className="p-6 pb-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-3 rounded-xl ${
                      cardData.status === 'excellent' ? 'bg-green-100' :
                      cardData.status === 'good' ? 'bg-green-100' :
                      cardData.status === 'warning' ? 'bg-orange-100' :
                      cardData.status === 'attention' ? 'bg-orange-100' :
                      cardData.status === 'danger' ? 'bg-red-100' :
                      cardData.status === 'over' ? 'bg-red-100' :
                      cardData.status === 'under' ? 'bg-blue-100' :
                      'bg-gray-100'
                    }`}>
                      {getIcon(type, item.type)}
                    </div>
                    <div>
                      <h4 className={`font-semibold ${type === 'main' ? 'text-lg' : 'text-base'} text-gray-900`}>
                        {item.name}
                      </h4>
                      <div className="flex items-center gap-2 mt-1">
                        {getStatusIcon(cardData.status)}
                        <span className={`text-sm font-medium ${
                          cardData.status === 'excellent' ? 'text-green-700' :
                          cardData.status === 'good' ? 'text-green-700' :
                          cardData.status === 'warning' ? 'text-orange-700' :
                          cardData.status === 'attention' ? 'text-orange-700' :
                          cardData.status === 'danger' ? 'text-red-700' :
                          cardData.status === 'over' ? 'text-red-700' :
                          cardData.status === 'under' ? 'text-blue-700' :
                          'text-gray-700'
                        }`}>
                          {cardData.statusText}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Mensagem personalizada */}
                {cardData.message && (
                  <div className={`p-3 rounded-lg mb-4 ${
                    cardData.status === 'excellent' ? 'bg-green-100 border border-green-200' :
                    cardData.status === 'good' ? 'bg-green-100 border border-green-200' :
                    cardData.status === 'warning' ? 'bg-orange-100 border border-orange-200' :
                    cardData.status === 'attention' ? 'bg-orange-100 border border-orange-200' :
                    cardData.status === 'danger' ? 'bg-red-100 border border-red-200' :
                    'bg-blue-100 border border-blue-200'
                  }`}>
                    <p className={`text-sm font-medium ${
                      cardData.status === 'excellent' ? 'text-green-800' :
                      cardData.status === 'good' ? 'text-green-800' :
                      cardData.status === 'warning' ? 'text-orange-800' :
                      cardData.status === 'attention' ? 'text-orange-800' :
                      cardData.status === 'danger' ? 'text-red-800' :
                      'text-blue-800'
                    }`}>
                      {cardData.message}
                    </p>
                  </div>
                )}

                {/* Valores */}
                <div className="space-y-3">
                  <div className="flex justify-between items-center p-3 bg-white/70 rounded-lg">
                    <span className="text-sm font-medium text-gray-600">Meta</span>
                    <span className="font-bold text-gray-900">{formatCurrency(item.expected)}</span>
                  </div>

                  <div className="flex justify-between items-center p-3 bg-white/70 rounded-lg">
                    <span className="text-sm font-medium text-gray-600">Realizado</span>
                    <span className={`font-bold ${
                      cardData.status === 'excellent' ? 'text-green-700' :
                      cardData.status === 'good' ? 'text-green-700' :
                      cardData.status === 'warning' ? 'text-orange-700' :
                      cardData.status === 'attention' ? 'text-orange-700' :
                      cardData.status === 'danger' ? 'text-red-700' :
                      cardData.status === 'over' ? 'text-red-700' :
                      cardData.status === 'under' ? 'text-blue-700' :
                      'text-gray-700'
                    }`}>
                      {formatCurrency(item.actual)}
                    </span>
                  </div>

                  {item.expected > 0 && (
                    <div className="flex justify-between items-center p-3 bg-white/70 rounded-lg border-t">
                      <span className="text-sm font-medium text-gray-600">Diferença</span>
                      <div className="text-right">
                        <div className={`font-bold ${
                          cardData.status === 'excellent' ? 'text-green-700' :
                          cardData.status === 'good' ? 'text-green-700' :
                          cardData.status === 'warning' ? 'text-orange-700' :
                          cardData.status === 'attention' ? 'text-orange-700' :
                          cardData.status === 'danger' ? 'text-red-700' :
                          cardData.status === 'over' ? 'text-red-700' :
                          cardData.status === 'under' ? 'text-blue-700' :
                          'text-gray-700'
                        }`}>
                          {cardData.difference >= 0 ? '+' : ''}{formatCurrency(cardData.difference)}
                        </div>
                        <div className="text-xs text-gray-500">
                          {cardData.percentage >= 0 ? '+' : ''}{cardData.percentage}%
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              {item.expected > 0 && (
                <div className="px-6 pb-6">
                  <div className="w-full bg-white/50 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        cardData.status === 'excellent' ? 'bg-green-500' :
                        cardData.status === 'good' ? 'bg-green-500' :
                        cardData.status === 'warning' ? 'bg-orange-500' :
                        cardData.status === 'attention' ? 'bg-orange-500' :
                        cardData.status === 'danger' ? 'bg-red-500' :
                        cardData.status === 'over' ? 'bg-red-500' :
                        cardData.status === 'under' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`}
                      style={{
                        width: `${Math.min(100, Math.max(0, (item.actual / item.expected) * 100))}%`
                      }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-600 mt-1">
                    <span>0%</span>
                    <span>{((item.actual / item.expected) * 100).toFixed(0)}%</span>
                    <span>100%</span>
                  </div>
                </div>
              )}
            </div>
          )
        })}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-300 rounded"></div>
        </div>
      </div>
    )
  }

  if (!data) {
    return null // Não mostrar nada se não há dados
  }

  const monthName = new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })

  const tabs = [
    { key: 'main', label: 'Principais', count: data.chartData.main.length },
    { key: 'categories', label: 'Categorias', count: data.chartData.categories.length },
    { key: 'tags', label: 'Tags', count: data.chartData.tags.length }
  ].filter(tab => tab.count > 0)

  const activeData = data.chartData[activeTab] || []

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-2">
          <BarChart3 className="h-5 w-5 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Esperado vs Real - {monthName}
          </h3>
        </div>
        <p className="text-sm text-gray-600">
          Compare seus gastos planejados com os valores reais
        </p>
      </div>

      {/* Tabs */}
      {tabs.length > 1 && (
        <div className="flex border-b border-gray-200">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'text-indigo-600 border-b-2 border-indigo-600 bg-indigo-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {activeData.length > 0 ? (
          <div className="space-y-6">
            {/* Gráfico */}
            <div className="bg-white rounded-xl p-6 border border-gray-100 shadow-sm">
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={activeData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 60 }}
                    barCategoryGap="20%"
                  >
                    <defs>
                      <linearGradient id="expectedGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#6366f1" stopOpacity={0.8}/>
                        <stop offset="100%" stopColor="#6366f1" stopOpacity={0.6}/>
                      </linearGradient>
                      <linearGradient id="actualGradient" x1="0" y1="0" x2="0" y2="1">
                        <stop offset="0%" stopColor="#10b981" stopOpacity={0.8}/>
                        <stop offset="100%" stopColor="#10b981" stopOpacity={0.6}/>
                      </linearGradient>
                    </defs>
                    <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                    <XAxis
                      dataKey="name"
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      interval={0}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                      axisLine={{ stroke: '#e2e8f0' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12, fill: '#64748b' }}
                      tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                      axisLine={{ stroke: '#e2e8f0' }}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend
                      wrapperStyle={{ paddingTop: '20px' }}
                      iconType="rect"
                    />
                    <Bar
                      dataKey="expected"
                      fill="url(#expectedGradient)"
                      name="Meta"
                      radius={[4, 4, 0, 0]}
                      stroke="#6366f1"
                      strokeWidth={1}
                    />
                    <Bar
                      dataKey="actual"
                      fill="url(#actualGradient)"
                      name="Realizado"
                      radius={[4, 4, 0, 0]}
                      stroke="#10b981"
                      strokeWidth={1}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Cards */}
            {renderCards(activeData, activeTab)}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum dado encontrado para esta categoria</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ExpectedVsActualChart
