import React, { useState, useEffect } from 'react'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Legend
} from 'recharts'
import { TrendingUp, TrendingDown, PiggyBank, Coffee, Folder, Tag, BarChart3 } from 'lucide-react'
import api from '../services/api'
import toast from 'react-hot-toast'

const ExpectedVsActualChart = ({ year, month }) => {
  const [data, setData] = useState(null)
  const [loading, setLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('main')

  useEffect(() => {
    if (year && month) {
      fetchData()
    }
  }, [year, month])

  const fetchData = async () => {
    try {
      setLoading(true)
      const response = await api.get(`/reports/expected-vs-actual/${year}/${month}`)
      setData(response.data)
    } catch (error) {
      console.error('Erro ao buscar dados esperados vs reais:', error)
      if (error.response?.data?.needsSetup) {
        // Não mostrar erro se precisar de setup
        setData(null)
      } else {
        toast.error('Erro ao carregar gráfico de comparação')
      }
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border border-gray-200 rounded-lg shadow-lg">
          <p className="font-medium text-gray-900 mb-2">{label}</p>
          {payload.map((entry, index) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {entry.dataKey === 'expected' ? 'Esperado' : 'Real'}: {formatCurrency(entry.value)}
            </p>
          ))}
          {payload.length === 2 && (
            <p className="text-xs text-gray-500 mt-1 pt-1 border-t">
              Diferença: {formatCurrency(Math.abs(payload[1].value - payload[0].value))}
            </p>
          )}
        </div>
      )
    }
    return null
  }

  const getCardData = (item) => {
    const difference = item.actual - item.expected
    const percentage = item.expected > 0 ? (difference / item.expected) * 100 : 0
    
    let status = 'neutral'
    let statusText = 'Dentro do esperado'
    let statusColor = 'text-gray-600'
    
    if (Math.abs(percentage) <= 10) {
      status = 'good'
      statusText = 'Dentro da meta'
      statusColor = 'text-green-600'
    } else if (difference > 0) {
      status = 'over'
      statusText = `${Math.abs(percentage).toFixed(1)}% acima`
      statusColor = 'text-red-600'
    } else {
      status = 'under'
      statusText = `${Math.abs(percentage).toFixed(1)}% abaixo`
      statusColor = 'text-blue-600'
    }

    return { difference, percentage, status, statusText, statusColor }
  }

  const renderCards = (items, type) => {
    if (!items || items.length === 0) return null

    const getIcon = (type, itemType) => {
      if (type === 'main') {
        switch (itemType) {
          case 'expenses': return <TrendingDown className="h-5 w-5 text-red-600" />
          case 'savings': return <PiggyBank className="h-5 w-5 text-green-600" />
          case 'leisure': return <Coffee className="h-5 w-5 text-blue-600" />
          default: return <BarChart3 className="h-5 w-5 text-gray-600" />
        }
      } else if (type === 'categories') {
        return <Folder className="h-4 w-4 text-purple-600" />
      } else {
        return <Tag className="h-4 w-4 text-indigo-600" />
      }
    }

    return (
      <div className={`grid gap-4 ${type === 'main' ? 'grid-cols-1 md:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3'}`}>
        {items.map((item, index) => {
          const cardData = getCardData(item)
          
          return (
            <div key={index} className="bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  {getIcon(type, item.type)}
                  <h4 className={`font-medium ${type === 'main' ? 'text-base' : 'text-sm'} text-gray-900`}>
                    {item.name}
                  </h4>
                </div>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  cardData.status === 'good' ? 'bg-green-100 text-green-700' :
                  cardData.status === 'over' ? 'bg-red-100 text-red-700' :
                  cardData.status === 'under' ? 'bg-blue-100 text-blue-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {cardData.statusText}
                </span>
              </div>

              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Esperado:</span>
                  <span className="font-medium text-gray-900">{formatCurrency(item.expected)}</span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Real:</span>
                  <span className={`font-medium ${cardData.statusColor}`}>
                    {formatCurrency(item.actual)}
                  </span>
                </div>
                
                {item.expected > 0 && (
                  <div className="pt-2 border-t border-gray-100">
                    <div className="flex justify-between items-center">
                      <span className="text-xs text-gray-500">Diferença:</span>
                      <span className={`text-xs font-medium ${cardData.statusColor}`}>
                        {cardData.difference >= 0 ? '+' : ''}{formatCurrency(cardData.difference)}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )
        })}
      </div>
    )
  }

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-300 rounded mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-300 rounded"></div>
        </div>
      </div>
    )
  }

  if (!data) {
    return null // Não mostrar nada se não há dados
  }

  const monthName = new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long', year: 'numeric' })

  const tabs = [
    { key: 'main', label: 'Principais', count: data.chartData.main.length },
    { key: 'categories', label: 'Categorias', count: data.chartData.categories.length },
    { key: 'tags', label: 'Tags', count: data.chartData.tags.length }
  ].filter(tab => tab.count > 0)

  const activeData = data.chartData[activeTab] || []

  return (
    <div className="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center gap-2 mb-2">
          <BarChart3 className="h-5 w-5 text-indigo-600" />
          <h3 className="text-lg font-semibold text-gray-900">
            Esperado vs Real - {monthName}
          </h3>
        </div>
        <p className="text-sm text-gray-600">
          Compare seus gastos planejados com os valores reais
        </p>
      </div>

      {/* Tabs */}
      {tabs.length > 1 && (
        <div className="flex border-b border-gray-200">
          {tabs.map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key)}
              className={`flex-1 px-4 py-3 text-sm font-medium transition-colors ${
                activeTab === tab.key
                  ? 'text-indigo-600 border-b-2 border-indigo-600 bg-indigo-50'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              {tab.label} ({tab.count})
            </button>
          ))}
        </div>
      )}

      {/* Content */}
      <div className="p-6">
        {activeData.length > 0 ? (
          <div className="space-y-6">
            {/* Gráfico */}
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={activeData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    tick={{ fontSize: 12 }}
                    interval={0}
                    angle={-45}
                    textAnchor="end"
                    height={80}
                  />
                  <YAxis 
                    tick={{ fontSize: 12 }}
                    tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Bar dataKey="expected" fill="#6366f1" name="Esperado" radius={[2, 2, 0, 0]} />
                  <Bar dataKey="actual" fill="#10b981" name="Real" radius={[2, 2, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>

            {/* Cards */}
            {renderCards(activeData, activeTab)}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>Nenhum dado encontrado para esta categoria</p>
          </div>
        )}
      </div>
    </div>
  )
}

export default ExpectedVsActualChart
