import React, { useState, useEffect } from 'react'
import { 
  PiggyBank, 
  Plus, 
  Edit, 
  Trash2, 
  TrendingUp, 
  Target, 
  DollarSign,
  Percent,
  Calendar,
  ArrowUp,
  ArrowDown
} from 'lucide-react'
import { piggyBankService } from '../services/piggyBankService'
import { bankService } from '../services/bankService'
import CurrencyInput from './CurrencyInput'
import toast from 'react-hot-toast'

function PiggyBankManager() {
  const [piggyBanks, setPiggyBanks] = useState([])
  const [banks, setBanks] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [showAmountModal, setShowAmountModal] = useState(false)
  const [showCdiModal, setShowCdiModal] = useState(false)
  const [editingPiggyBank, setEditingPiggyBank] = useState(null)
  const [selectedPiggyBank, setSelectedPiggyBank] = useState(null)
  const [amountAction, setAmountAction] = useState('add') // 'add' or 'withdraw'
  const [amount, setAmount] = useState(0)
  const [cdiRate, setCdiRate] = useState(0)
  
  const [newPiggyBank, setNewPiggyBank] = useState({
    name: '',
    description: '',
    currentAmount: 0,
    targetAmount: 0,
    bankId: '',
    cdiRate: 0,
    icon: '🐷',
    color: '#EC4899'
  })

  const piggyBankIcons = ['🐷', '💰', '💎', '🏆', '🎯', '🌟', '💝', '🎁', '🏠', '🚗', '✈️', '🎓', '💍', '🎪', '🎨', '🎵']
  const piggyBankColors = [
    '#EC4899', '#8B5CF6', '#3B82F6', '#10B981',
    '#F59E0B', '#EF4444', '#6B7280', '#14B8A6'
  ]

  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      setLoading(true)
      const [piggyBanksData, banksData] = await Promise.all([
        piggyBankService.getPiggyBanks(),
        bankService.getBanks()
      ])
      setPiggyBanks(piggyBanksData)
      setBanks(banksData)
    } catch (error) {
      console.error('Erro ao buscar dados:', error)
      toast.error('Erro ao carregar dados')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingPiggyBank) {
        await piggyBankService.updatePiggyBank(editingPiggyBank.id, newPiggyBank)
        toast.success('Cofrinho atualizado com sucesso!')
      } else {
        await piggyBankService.createPiggyBank(newPiggyBank)
        toast.success('Cofrinho criado com sucesso!')
      }
      setShowModal(false)
      setEditingPiggyBank(null)
      resetForm()
      await fetchData() // Aguardar a atualização
    } catch (error) {
      console.error('Erro ao salvar cofrinho:', error)
      toast.error(error.response?.data?.error || 'Erro ao salvar cofrinho')
    }
  }

  const handleEdit = (piggyBank) => {
    setEditingPiggyBank(piggyBank)
    setNewPiggyBank({
      name: piggyBank.name,
      description: piggyBank.description || '',
      currentAmount: piggyBank.currentAmount,
      targetAmount: piggyBank.targetAmount || 0,
      bankId: piggyBank.bankId,
      cdiRate: piggyBank.cdiRate,
      icon: piggyBank.icon || '🐷',
      color: piggyBank.color || '#EC4899'
    })
    setShowModal(true)
  }

  const handleDelete = async (id) => {
    if (window.confirm('Tem certeza que deseja excluir este cofrinho?')) {
      try {
        await piggyBankService.deletePiggyBank(id)
        toast.success('Cofrinho excluído com sucesso!')
        fetchData()
      } catch (error) {
        toast.error('Erro ao excluir cofrinho')
      }
    }
  }

  const handleAmountAction = async () => {
    try {
      if (amountAction === 'add') {
        await piggyBankService.addAmount(selectedPiggyBank.id, amount)
        toast.success('Valor adicionado com sucesso!')
      } else {
        await piggyBankService.withdrawAmount(selectedPiggyBank.id, amount)
        toast.success('Valor retirado com sucesso!')
      }
      setShowAmountModal(false)
      setAmount(0)
      await fetchData() // Aguardar a atualização
    } catch (error) {
      console.error('Erro ao processar operação:', error)
      toast.error(error.response?.data?.error || 'Erro ao processar operação')
    }
  }

  const handleUpdateCdi = async () => {
    try {
      const result = await piggyBankService.updateCdi(cdiRate)
      toast.success(`CDI atualizado com sucesso! ${result.updatedCount} cofrinhos atualizados.`)
      setShowCdiModal(false)
      setCdiRate(0)
      await fetchData() // Aguardar a atualização
    } catch (error) {
      console.error('Erro ao atualizar CDI:', error)
      toast.error(error.response?.data?.error || 'Erro ao atualizar CDI')
    }
  }

  const handleApplyCdi = async () => {
    try {
      const result = await piggyBankService.applyCdi()
      toast.success(`Rendimento aplicado com sucesso! ${result.updatedCount} cofrinhos atualizados.`)
      await fetchData() // Aguardar a atualização
    } catch (error) {
      console.error('Erro ao aplicar rendimento:', error)
      toast.error(error.response?.data?.error || 'Erro ao aplicar rendimento')
    }
  }

  const resetForm = () => {
    setNewPiggyBank({
      name: '',
      description: '',
      currentAmount: 0,
      targetAmount: 0,
      bankId: '',
      cdiRate: 0,
      icon: '🐷',
      color: '#EC4899'
    })
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value)
  }

  const getProgressPercentage = (current, target) => {
    if (!target || target === 0) return 0
    return Math.min((current / target) * 100, 100)
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Cofrinhos</h2>
          <p className="text-gray-600 mt-1">
            Gerencie suas economias com rendimento baseado no CDI
          </p>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setShowCdiModal(true)}
            className="flex items-center gap-2 px-4 py-2 text-blue-600 border border-blue-600 rounded-lg hover:bg-blue-50 transition-colors"
          >
            <Percent className="h-4 w-4" />
            Atualizar CDI
          </button>
          <button
            onClick={handleApplyCdi}
            className="flex items-center gap-2 px-4 py-2 text-green-600 border border-green-600 rounded-lg hover:bg-green-50 transition-colors"
          >
            <TrendingUp className="h-4 w-4" />
            Aplicar Rendimento
          </button>
          <button
            onClick={() => {
              setEditingPiggyBank(null)
              resetForm()
              setShowModal(true)
            }}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4" />
            Novo Cofrinho
          </button>
        </div>
      </div>

      {/* Lista de Cofrinhos */}
      {piggyBanks.length === 0 ? (
        <div className="text-center py-12 bg-white rounded-xl border border-gray-200">
          <PiggyBank className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Nenhum cofrinho encontrado
          </h3>
          <p className="text-gray-600 mb-6">
            Crie seu primeiro cofrinho para começar a economizar
          </p>
          <button
            onClick={() => setShowModal(true)}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            Criar Cofrinho
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {piggyBanks.map((piggyBank) => (
            <div
              key={piggyBank.id}
              className="bg-white rounded-xl border shadow-sm hover:shadow-md transition-all duration-200"
            >
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-12 h-12 rounded-xl flex items-center justify-center text-2xl"
                      style={{
                        backgroundColor: piggyBank.color + '20',
                        border: `2px solid ${piggyBank.color}40`
                      }}
                    >
                      {piggyBank.icon || '🐷'}
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        {piggyBank.name}
                      </h3>
                      <p className="text-sm text-gray-600">
                        {piggyBank.bank?.name} • CDI {piggyBank.cdiRate}%
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-1">
                    <button
                      onClick={() => handleEdit(piggyBank)}
                      className="p-2 text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      <Edit className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => handleDelete(piggyBank.id)}
                      className="p-2 text-gray-400 hover:text-red-600 transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {piggyBank.description && (
                  <p className="text-sm text-gray-600 mb-4">
                    {piggyBank.description}
                  </p>
                )}

                <div className="space-y-3 mb-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Valor Atual</span>
                    <span className="text-lg font-bold text-gray-900">
                      {formatCurrency(piggyBank.currentAmount)}
                    </span>
                  </div>

                  {piggyBank.targetAmount && (
                    <>
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Meta</span>
                        <span className="text-sm font-medium text-gray-700">
                          {formatCurrency(piggyBank.targetAmount)}
                        </span>
                      </div>
                      
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-300"
                          style={{
                            width: `${getProgressPercentage(piggyBank.currentAmount, piggyBank.targetAmount)}%`,
                            backgroundColor: piggyBank.color || '#EC4899'
                          }}
                        ></div>
                      </div>
                      
                      <div className="text-center">
                        <span className="text-sm font-medium text-gray-700">
                          {getProgressPercentage(piggyBank.currentAmount, piggyBank.targetAmount).toFixed(1)}% da meta
                        </span>
                      </div>
                    </>
                  )}
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setSelectedPiggyBank(piggyBank)
                      setAmountAction('add')
                      setShowAmountModal(true)
                    }}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-green-600 border border-green-600 rounded-lg hover:bg-green-50 transition-colors"
                  >
                    <ArrowUp className="h-4 w-4" />
                    Adicionar
                  </button>
                  <button
                    onClick={() => {
                      setSelectedPiggyBank(piggyBank)
                      setAmountAction('withdraw')
                      setShowAmountModal(true)
                    }}
                    className="flex-1 flex items-center justify-center gap-2 px-3 py-2 text-red-600 border border-red-600 rounded-lg hover:bg-red-50 transition-colors"
                  >
                    <ArrowDown className="h-4 w-4" />
                    Retirar
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Modal de Criação/Edição */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {editingPiggyBank ? 'Editar Cofrinho' : 'Novo Cofrinho'}
              </h3>
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Nome do Cofrinho *
                </label>
                <input
                  type="text"
                  value={newPiggyBank.name}
                  onChange={(e) => setNewPiggyBank({ ...newPiggyBank, name: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Descrição
                </label>
                <textarea
                  value={newPiggyBank.description}
                  onChange={(e) => setNewPiggyBank({ ...newPiggyBank, description: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="2"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Ícone
                  </label>
                  <div className="flex flex-wrap gap-2 max-h-32 overflow-y-auto">
                    {piggyBankIcons.map((icon) => (
                      <button
                        key={icon}
                        type="button"
                        onClick={() => setNewPiggyBank({ ...newPiggyBank, icon })}
                        className={`w-10 h-10 rounded-lg border-2 flex items-center justify-center text-lg transition-colors ${
                          newPiggyBank.icon === icon ? 'border-blue-500 bg-blue-50' : 'border-gray-300 hover:border-gray-400'
                        }`}
                      >
                        {icon}
                      </button>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Cor
                  </label>
                  <div className="flex flex-wrap gap-2">
                    {piggyBankColors.map((color) => (
                      <button
                        key={color}
                        type="button"
                        onClick={() => setNewPiggyBank({ ...newPiggyBank, color })}
                        className={`w-8 h-8 rounded-full border-2 transition-all ${
                          newPiggyBank.color === color ? 'border-gray-800 scale-110' : 'border-gray-300'
                        }`}
                        style={{ backgroundColor: color }}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Valor Inicial
                  </label>
                  <CurrencyInput
                    value={newPiggyBank.currentAmount}
                    onChange={(value) => setNewPiggyBank({ ...newPiggyBank, currentAmount: value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Meta (Opcional)
                  </label>
                  <CurrencyInput
                    value={newPiggyBank.targetAmount}
                    onChange={(value) => setNewPiggyBank({ ...newPiggyBank, targetAmount: value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Banco Vinculado *
                  </label>
                  <select
                    value={newPiggyBank.bankId}
                    onChange={(e) => setNewPiggyBank({ ...newPiggyBank, bankId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    <option value="">Selecione um banco</option>
                    {banks.filter(bank => bank.isVisible !== false).map((bank) => (
                      <option key={bank.id} value={bank.id}>
                        {bank.icon} {bank.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Taxa CDI (%)
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={newPiggyBank.cdiRate || ''}
                    onChange={(e) => setNewPiggyBank({ ...newPiggyBank, cdiRate: e.target.value ? parseFloat(e.target.value) : 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Ex: 13.75"
                  />
                </div>
              </div>

              <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  type="submit"
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  {editingPiggyBank ? 'Atualizar' : 'Criar'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal de Adicionar/Retirar Valor */}
      {showAmountModal && selectedPiggyBank && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                {amountAction === 'add' ? 'Gostei muito, faça só mais algumas alterações:
Nos Objetivos Financeiros
1 - No card de projeção, insira uma opção para marca-la como conluída e remova a barra de progresso. Ao marcar como concluída, quero inserir a data de conclusão e o card deve possuir uma coloração ou flag, quero algo que me mostre que aquele objetivo foi concluído.

Na aba de cofrinhos na tela de Bancos: 
1 - No modal de adicionar valor, quero que Habilite um input para que eu vincule um banco. o que eu quero é poder selecionar o banco do qual aquele valor a ser adicionado no cofrinho virá. Para isso o banco deve ter o saldo disponível para que eu guarde no cofrinho.
Lembre-se que o banco que eu selecionei para a inserção do valor pode ser diferente do banco vinculado ao cofrinho. Essa seleção de banco é obrigatória.
2 - Quando eu clicar em adicionar, o sistema deve gerar uma transação de Investimento com o valor que cadastrei no modal. Além disso o sistema deve decrementar o valor do saldo do banco que selecionei no modal de adicionar valor.
3 - se eu Clicar em retirar valor, a mesma opção de seleção de bancos deve aparecer(Ele não precisa ter saldo disponível) e ao confirmar, o sistema deve gerar uma transação de receita e decrementar o valor que inseri do valor atual do cofrinho e inseri-lo no banco que selecionei.
4 - Quero que melhore visualmente esse modal de Adicionar valor... está muito feio quero algo mais elegante e profissional' : 'Retirar Valor'}
              </h3>
              <p className="text-gray-600 mt-1">
                {selectedPiggyBank.name}
              </p>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Valor
                </label>
                <CurrencyInput
                  value={amount}
                  onChange={setAmount}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowAmountModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleAmountAction}
                  className={`px-6 py-2 text-white rounded-lg transition-colors ${
                    amountAction === 'add'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-red-600 hover:bg-red-700'
                  }`}
                >
                  {amountAction === 'add' ? 'Adicionar' : 'Retirar'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Atualizar CDI */}
      {showCdiModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900">
                Atualizar Taxa CDI
              </h3>
              <p className="text-gray-600 mt-1">
                Aplicar nova taxa para todos os cofrinhos
              </p>
            </div>

            <div className="p-6">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Taxa CDI (% ao ano)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  value={cdiRate || ''}
                  onChange={(e) => setCdiRate(e.target.value ? parseFloat(e.target.value) : 0)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Ex: 13.75"
                />
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowCdiModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancelar
                </button>
                <button
                  onClick={handleUpdateCdi}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  Atualizar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PiggyBankManager
