const { PrismaClient } = require('@prisma/client');
const { BILL_STATUS } = require('../constants/status');

const prisma = new PrismaClient();

class BillService {
  /**
   * Obter ou criar fatura atual do banco
   */
  async getCurrentBill(bankId, userId) {
    try {
      const bank = await prisma.bank.findFirst({
        where: { id: bankId, userId }
      });

      if (!bank || !bank.billDueDay) {
        throw new Error('Banco não encontrado ou sem dia de vencimento configurado');
      }

      // Calcular data de vencimento da fatura atual
      const currentDate = new Date();
      const currentBillDueDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), bank.billDueDay);

      // Se já passou do dia de vencimento deste mês, a fatura atual vence no próximo mês
      if (currentDate.getDate() > bank.billDueDay) {
        currentBillDueDate.setMonth(currentBillDueDate.getMonth() + 1);
      }

      // Buscar fatura existente para esta data de vencimento
      let bill = await prisma.bill.findFirst({
        where: {
          bankId,
          userId,
          dueDate: {
            gte: new Date(currentBillDueDate.getFullYear(), currentBillDueDate.getMonth(), currentBillDueDate.getDate()),
            lt: new Date(currentBillDueDate.getFullYear(), currentBillDueDate.getMonth(), currentBillDueDate.getDate() + 1)
          }
        }
      });

      // Se não existe, criar nova fatura
      if (!bill) {
        bill = await prisma.bill.create({
          data: {
            bankId,
            userId,
            dueDate: currentBillDueDate,
            amount: 0,
            status: 'PENDING'
          }
        });
      }

      return bill;
    } catch (error) {
      console.error('❌ Erro ao obter fatura atual:', error);
      throw error;
    }
  }

  /**
   * Calcular valor da fatura baseado nas regras especificadas
   */
  async calculateBillAmount(bankId, userId) {
    try {
      // Buscar transações de cartão de crédito com status CURRENT_BILL
      const transactions = await prisma.transaction.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          installmentStatus: 'CURRENT_BILL',
          userId
        },
        include: {
          category: true,
          paymentMethod: true
        }
      });

      const transactionsTotal = transactions.reduce((sum, transaction) => sum + transaction.amount, 0);

      // Buscar assinaturas com status CURRENT_BILL
      const subscriptions = await prisma.subscription.findMany({
        where: {
          paymentMethod: {
            bankId,
            type: 'CREDIT'
          },
          status: 'CURRENT_BILL',
          isActive: true,
          userId
        },
        include: {
          category: true,
          paymentMethod: true
        }
      });

      const subscriptionsTotal = subscriptions.reduce((sum, subscription) => sum + subscription.amount, 0);
      const total = transactionsTotal + subscriptionsTotal;

      console.log(`💳 Cálculo da fatura do banco ${bankId}:`);
      console.log(`   Transações CURRENT_BILL: R$ ${transactionsTotal.toFixed(2)} (${transactions.length} itens)`);
      console.log(`   Assinaturas CURRENT_BILL: R$ ${subscriptionsTotal.toFixed(2)} (${subscriptions.length} itens)`);
      console.log(`   Total da fatura: R$ ${total.toFixed(2)}`);

      return {
        transactionsTotal,
        subscriptionsTotal,
        total,
        transactions,
        subscriptions
      };
    } catch (error) {
      console.error('❌ Erro ao calcular valor da fatura:', error);
      return {
        transactionsTotal: 0,
        subscriptionsTotal: 0,
        total: 0,
        transactions: [],
        subscriptions: []
      };
    }
  }

  /**
   * Atualizar status da fatura baseado na data atual
   */
  async updateBillStatus(bankId, userId) {
    try {
      const bill = await this.getCurrentBill(bankId, userId);
      const currentDate = new Date();

      let newStatus = bill.status;

      // Regra 2: Se data atual > vencimento e status ≠ PAID e valor > 0 = OVERDUE
      if (currentDate > bill.dueDate && bill.status !== 'PAID' && bill.amount > 0) {
        newStatus = 'OVERDUE';
      }
      // Regra 2: Se data atual < vencimento = PENDING
      else if (currentDate < bill.dueDate) {
        newStatus = 'PENDING';
      }

      // Atualizar status se mudou
      if (newStatus !== bill.status) {
        await prisma.bill.update({
          where: { id: bill.id },
          data: { status: newStatus }
        });

        console.log(`📋 Status da fatura atualizado: ${bill.status} → ${newStatus}`);

        // Se ficou OVERDUE, criar notificação
        if (newStatus === 'OVERDUE') {
          await this.createOverdueNotification(bankId, userId, bill.amount);
        }
      }

      return { ...bill, status: newStatus };
    } catch (error) {
      console.error('❌ Erro ao atualizar status da fatura:', error);
      throw error;
    }
  }

  /**
   * Verificar se pode criar transação/assinatura (Regra 5)
   */
  async canCreateTransaction(bankId, userId) {
    try {
      const bill = await this.getCurrentBill(bankId, userId);
      await this.updateBillStatus(bankId, userId);

      // Buscar fatura atualizada
      const updatedBill = await prisma.bill.findUnique({
        where: { id: bill.id }
      });

      if (updatedBill.status === 'OVERDUE') {
        return {
          canCreate: false,
          reason: 'Fatura em atraso. Pague a fatura antes de criar novas transações.',
          billAmount: updatedBill.amount,
          dueDate: updatedBill.dueDate
        };
      }

      return {
        canCreate: true,
        reason: null,
        billAmount: updatedBill.amount
      };
    } catch (error) {
      console.error('❌ Erro ao verificar se pode criar transação:', error);
      return {
        canCreate: false,
        reason: 'Erro interno do sistema',
        billAmount: 0
      };
    }
  }

  /**
   * Criar notificação de fatura em atraso
   */
  async createOverdueNotification(bankId, userId, amount) {
    try {
      const bank = await prisma.bank.findUnique({
        where: { id: bankId }
      });

      await prisma.notification.create({
        data: {
          title: 'Fatura em Atraso',
          message: `A fatura do ${bank.name} está em atraso. Valor: R$ ${amount.toFixed(2)}`,
          type: 'OVERDUE_BILL',
          priority: 'HIGH',
          amount: amount,
          entityId: bankId,
          entityType: 'bill',
          userId
        }
      });

      console.log(`🔔 Notificação de fatura em atraso criada para ${bank.name}`);
    } catch (error) {
      console.error('❌ Erro ao criar notificação:', error);
    }
  }

  /**
   * Pagar fatura
   */
  async payBill(bankId, paymentBankId, userId) {
    try {
      // Obter fatura atual
      const bill = await this.getCurrentBill(bankId, userId);

      // Calcular valor atual da fatura
      const billData = await this.calculateBillAmount(bankId, userId);

      if (billData.total <= 0) {
        throw new Error('Não há valor a ser pago na fatura');
      }

      const paymentBank = await prisma.bank.findFirst({
        where: { id: paymentBankId, userId }
      });

      if (!paymentBank || paymentBank.currentBalance < billData.total) {
        throw new Error('Saldo insuficiente para pagamento');
      }

      // Processar pagamento
      await prisma.$transaction(async (tx) => {
        // 1. Reduzir saldo do banco pagador
        await tx.bank.update({
          where: { id: paymentBankId },
          data: {
            currentBalance: {
              decrement: billData.total
            }
          }
        });

        // 2. Marcar transações como pagas e alterar status para BILLED
        for (const transaction of billData.transactions) {
          await tx.transaction.update({
            where: { id: transaction.id },
            data: {
              isPaid: true,
              installmentStatus: 'BILLED'
            }
          });
        }

        // 3. Marcar assinaturas como BILLED
        for (const subscription of billData.subscriptions) {
          await tx.subscription.update({
            where: { id: subscription.id },
            data: {
              status: 'BILLED'
            }
          });
        }

        // 4. Atualizar fatura como paga
        await tx.bill.update({
          where: { id: bill.id },
          data: {
            status: 'PAID',
            amount: billData.total,
            paidDate: new Date(),
            paymentBankId: paymentBankId
          }
        });

        // 5. Restaurar limite de crédito do banco da fatura
        const billBank = await tx.bank.findUnique({
          where: { id: bankId }
        });

        if (billBank && billBank.creditLimit > 0) {
          await tx.bank.update({
            where: { id: bankId },
            data: {
              availableLimit: {
                increment: billData.total
              }
            }
          });
        }

        // 6. Criar histórico de pagamento
        await tx.billHistory.create({
          data: {
            bankId,
            amount: billData.total,
            dueDate: bill.dueDate,
            paidDate: new Date(),
            paymentBankId,
            userId
          }
        });
      });

      console.log(`✅ Fatura paga com sucesso:`);
      console.log(`   Banco da fatura: ${bankId}`);
      console.log(`   Valor pago: R$ ${billData.total.toFixed(2)}`);
      console.log(`   Banco pagador: ${paymentBankId}`);
      console.log(`   Transações marcadas: ${billData.transactions.length}`);
      console.log(`   Assinaturas marcadas: ${billData.subscriptions.length}`);

      return {
        success: true,
        amount: billData.total,
        transactionsMarkedAsPaid: billData.transactions.length,
        subscriptionsMarkedAsPaid: billData.subscriptions.length,
        paymentBank: paymentBank.name,
        message: 'Fatura paga com sucesso'
      };

    } catch (error) {
      console.error('❌ Erro ao pagar fatura:', error);
      throw error;
    }
  }

  /**
   * Obter dados completos da fatura para exibição
   */
  async getBillData(bankId, userId) {
    try {
      // Atualizar status da fatura
      const bill = await this.updateBillStatus(bankId, userId);

      // Calcular valor atual
      const billData = await this.calculateBillAmount(bankId, userId);

      // Atualizar valor na fatura
      await prisma.bill.update({
        where: { id: bill.id },
        data: { amount: billData.total }
      });

      const bank = await prisma.bank.findUnique({
        where: { id: bankId },
        include: {
          paymentMethods: {
            where: { type: 'CREDIT' }
          }
        }
      });

      return {
        bill: {
          ...bill,
          amount: billData.total
        },
        bank,
        transactions: billData.transactions,
        subscriptions: billData.subscriptions,
        totals: {
          transactions: billData.transactionsTotal,
          subscriptions: billData.subscriptionsTotal,
          total: billData.total
        }
      };
    } catch (error) {
      console.error('❌ Erro ao obter dados da fatura:', error);
      throw error;
    }
  }
}

const billService = new BillService();
module.exports = billService;
