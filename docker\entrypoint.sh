#!/bin/bash
# 🚀 SARA - Script de Inicialização

set -e

echo "🚀 Iniciando SARA - Sistema de Acompanhamento de Recursos e Aplicações"
echo "=================================================="

# ================================
# Funções Auxiliares
# ================================
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

error() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] ERROR: $1" >&2
}

# ================================
# Verificar Variáveis de Ambiente
# ================================
log "🔍 Verificando variáveis de ambiente..."

if [ -z "$JWT_SECRET" ] || [ "$JWT_SECRET" = "seu_jwt_secret_super_seguro_aqui_mude_em_producao" ]; then
    error "JWT_SECRET não configurado ou usando valor padrão!"
    error "Configure uma chave segura para produção."
    exit 1
fi

if [ -z "$CLOUDINARY_CLOUD_NAME" ] || [ -z "$CLOUDINARY_API_KEY" ] || [ -z "$CLOUDINARY_API_SECRET" ]; then
    error "Configurações do Cloudinary não encontradas!"
    error "Configure CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY e CLOUDINARY_API_SECRET"
    exit 1
fi

log "✅ Variáveis de ambiente verificadas"

# ================================
# Criar Diretórios Necessários
# ================================
log "📁 Criando diretórios necessários..."

mkdir -p /app/logs
mkdir -p /var/log/supervisor
mkdir -p /var/log/nginx
mkdir -p /tmp/git-cache

# Ajustar permissões
chown -R sara:nodejs /app/backend
chown -R sara:nodejs /app/logs
chmod 755 /app/logs

log "✅ Diretórios criados"

# ================================
# Configurar Banco de Dados
# ================================
log "🗄️ Configurando banco de dados..."

cd /app/backend

# Verificar se o banco existe
if [ ! -f "production.db" ]; then
    log "📊 Criando banco de dados inicial..."
    
    # Executar migrações
    npx prisma db push --force-reset
    
    # Criar usuário padrão
    node -e "
        const { PrismaClient } = require('@prisma/client');
        const bcrypt = require('bcrypt');
        
        async function createDefaultUser() {
            const prisma = new PrismaClient();
            try {
                const hashedPassword = await bcrypt.hash('123456', 10);
                await prisma.user.create({
                    data: {
                        email: '<EMAIL>',
                        password: hashedPassword,
                        name: 'Administrador'
                    }
                });
                console.log('✅ Usuário padrão criado: <EMAIL> / 123456');
            } catch (error) {
                if (error.code !== 'P2002') {
                    console.error('Erro ao criar usuário:', error);
                }
            } finally {
                await prisma.\$disconnect();
            }
        }
        
        createDefaultUser();
    "
else
    log "📊 Banco de dados existente encontrado"
    
    # Executar migrações pendentes
    npx prisma db push
fi

log "✅ Banco de dados configurado"

# ================================
# Configurar Git (se auto-deploy ativado)
# ================================
if [ "$AUTO_DEPLOY" = "true" ]; then
    log "🔄 Configurando auto-deploy..."
    
    if [ -n "$GIT_REPO" ]; then
        log "📦 Repositório: $GIT_REPO"
        log "🌿 Branch: ${GIT_BRANCH:-main}"
        log "⏱️ Intervalo: ${DEPLOY_INTERVAL:-300}s"
    else
        log "⚠️ GIT_REPO não configurado, auto-deploy desabilitado"
        export AUTO_DEPLOY=false
    fi
fi

# ================================
# Verificar Saúde dos Serviços
# ================================
log "🏥 Verificando dependências..."

# Testar conexão com banco
node -e "
    const { PrismaClient } = require('@prisma/client');
    const prisma = new PrismaClient();
    prisma.\$connect()
        .then(() => console.log('✅ Conexão com banco OK'))
        .catch(err => {
            console.error('❌ Erro na conexão com banco:', err);
            process.exit(1);
        })
        .finally(() => prisma.\$disconnect());
"

log "✅ Verificações concluídas"

# ================================
# Configurar Nginx
# ================================
log "🌐 Configurando Nginx..."

# Testar configuração
nginx -t

if [ $? -eq 0 ]; then
    log "✅ Configuração do Nginx válida"
else
    error "❌ Configuração do Nginx inválida"
    exit 1
fi

# ================================
# Informações do Sistema
# ================================
log "📋 Informações do sistema:"
log "   - Node.js: $(node --version)"
log "   - NPM: $(npm --version)"
log "   - Nginx: $(nginx -v 2>&1)"
log "   - Ambiente: $NODE_ENV"
log "   - Porta Backend: $PORT"
log "   - Auto-deploy: $AUTO_DEPLOY"

# ================================
# Hooks de Inicialização
# ================================
if [ -f "/app/hooks/pre-start.sh" ]; then
    log "🪝 Executando hook pre-start..."
    bash /app/hooks/pre-start.sh
fi

# ================================
# Finalização
# ================================
log "🎉 Inicialização concluída com sucesso!"
log "🌐 Frontend disponível em: http://localhost"
log "🔌 Backend disponível em: http://localhost:3001"
log "👤 Login padrão: <EMAIL> / 123456"
log "=================================================="

# Executar comando passado como argumento
exec "$@"
