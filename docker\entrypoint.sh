#!/bin/bash
# 🚀 SARA - Script de Inicialização Simples

set -e

echo "🚀 Iniciando SARA..."

# Criar diretórios
mkdir -p /app/data /app/logs /var/log/supervisor /var/log/nginx /tmp/git-cache

# Verificar variáveis obrigatórias
if [ -z "$JWT_SECRET" ] || [ -z "$CLOUDINARY_CLOUD_NAME" ]; then
    echo "❌ Configure JWT_SECRET e CLOUDINARY_* no arquivo .env"
    exit 1
fi

# Configurar banco de dados
echo "🗄️ Configurando banco..."
cd /app/backend

if [ ! -f "production.db" ]; then
    echo "📊 Criando banco inicial..."
    npx prisma db push --force-reset

    # Criar usuário padrão
    node -e "
        const { PrismaClient } = require('@prisma/client');
        const bcrypt = require('bcrypt');

        async function createUser() {
            const prisma = new PrismaClient();
            try {
                const hash = await bcrypt.hash('123456', 10);
                await prisma.user.create({
                    data: {
                        email: '<EMAIL>',
                        password: hash,
                        name: 'Admin'
                    }
                });
                console.log('✅ Usuário criado: <EMAIL> / 123456');
            } catch (e) {
                if (e.code !== 'P2002') console.error(e);
            } finally {
                await prisma.\$disconnect();
            }
        }
        createUser();
    "
else
    npx prisma db push
fi

echo "✅ SARA pronto!"
echo "🌐 Acesse: http://localhost"
echo "👤 Login: <EMAIL> / 123456"

exec "$@"
