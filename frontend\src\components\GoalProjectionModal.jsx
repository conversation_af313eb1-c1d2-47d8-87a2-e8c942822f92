import React, { useState, useEffect } from 'react'
import { 
  X, 
  TrendingUp, 
  Calendar, 
  DollarSign, 
  Target,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Clock
} from 'lucide-react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend
} from 'recharts'
import goalService from '../services/goalService'
import toast from 'react-hot-toast'

const GoalProjectionModal = ({ goal, isOpen, onClose }) => {
  const [projectionData, setProjectionData] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (isOpen && goal) {
      fetchProjection()
    }
  }, [isOpen, goal])

  const fetchProjection = async () => {
    try {
      setLoading(true)
      const data = await goalService.getGoalProjection(goal.id)
      setProjectionData(data)
    } catch (error) {
      console.error('Erro ao buscar projeção:', error)
      toast.error(error.response?.data?.error || 'Erro ao calcular projeção')
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR', {
      month: 'short',
      year: 'numeric'
    })
  }

  const getMonthName = (month) => {
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ]
    return months[month - 1]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h3 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                <Target className="h-6 w-6 text-blue-600" />
                Projeção: {goal?.name}
              </h3>
              <p className="text-gray-600 mt-1">
                Meta de {formatCurrency(goal?.targetAmount)} para {getMonthName(goal?.targetMonth)} de {goal?.targetYear}
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : projectionData ? (
            <div className="space-y-8">
              {/* Resumo da Projeção */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Renda Média</h4>
                      <p className="text-sm text-gray-600">Últimos 3 meses</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(projectionData.projection.avgIncome)}
                  </p>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Poupança Mensal</h4>
                      <p className="text-sm text-gray-600">Capacidade projetada</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(projectionData.projection.monthlySavingsCapacity)}
                  </p>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Clock className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Tempo Ótimo</h4>
                      <p className="text-sm text-gray-600">Baseado na capacidade</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-purple-600">
                    {projectionData.projection.monthsToGoalOptimal ?
                      `${projectionData.projection.monthsToGoalOptimal} meses` :
                      'Impossível'
                    }
                  </p>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Target className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Meta Escolhida</h4>
                      <p className="text-sm text-gray-600">Valor mensal necessário</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(projectionData.projection.monthlyAmountNeeded)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {projectionData.projection.monthsToTargetDate} meses restantes
                  </p>
                </div>

                <div className={`rounded-xl p-6 border ${
                  projectionData.projection.canAchieveTargetDate
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
                    : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
                }`}>
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      projectionData.projection.canAchieveTargetDate
                        ? 'bg-green-100'
                        : 'bg-red-100'
                    }`}>
                      {projectionData.projection.canAchieveTargetDate ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Viabilidade</h4>
                      <p className="text-sm text-gray-600">Meta escolhida</p>
                    </div>
                  </div>
                  <p className={`text-lg font-bold ${
                    projectionData.projection.canAchieveTargetDate
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {projectionData.projection.canAchieveTargetDate ? 'Viável' : 'Desafiador'}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {formatDate(projectionData.projection.targetDate)}
                  </p>
                </div>
              </div>

              {/* Gráfico de Projeção */}
              {projectionData.projection.monthlyProjection.length > 0 && (
                <div className="bg-white rounded-xl border border-gray-200 p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Projeção de Acúmulo Mensal - Comparativo
                  </h4>
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-green-500 rounded"></div>
                        <span><strong>Linha Verde:</strong> Projeção baseada na sua capacidade atual de poupança</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-orange-500 rounded"></div>
                        <span><strong>Linha Laranja:</strong> Projeção para atingir a meta na data escolhida</span>
                      </div>
                    </div>
                  </div>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={projectionData.projection.monthlyProjection}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                        <XAxis
                          dataKey="month"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `Mês ${value}`}
                        />
                        <YAxis
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                        />
                        <Tooltip
                          formatter={(value, name) => {
                            const labels = {
                              'optimalAccumulatedAmount': 'Acúmulo Ótimo',
                              'targetAccumulatedAmount': 'Acúmulo Meta',
                              'optimalMonthlyAmount': 'Valor Mensal Ótimo',
                              'targetMonthlyAmount': 'Valor Mensal Meta'
                            };
                            return [formatCurrency(value), labels[name] || name];
                          }}
                          labelFormatter={(value) => `Mês ${value}`}
                        />
                        <Line
                          type="monotone"
                          dataKey="optimalAccumulatedAmount"
                          stroke="#10b981"
                          strokeWidth={3}
                          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                          name="Acúmulo Ótimo"
                        />
                        <Line
                          type="monotone"
                          dataKey="targetAccumulatedAmount"
                          stroke="#f97316"
                          strokeWidth={3}
                          dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                          name="Acúmulo Meta"
                        />
                        {/* Linha vertical para marcar a data da meta */}
                        {projectionData.projection.monthlyProjection.map((item, index) =>
                          item.isTargetMonth ? (
                            <Line
                              key={`target-line-${index}`}
                              type="monotone"
                              dataKey={() => goal.targetAmount}
                              stroke="#dc2626"
                              strokeWidth={2}
                              strokeDasharray="10 5"
                              dot={false}
                              name="Data Meta"
                            />
                          ) : null
                        )}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Histórico do Ano Atual */}
              <div className="bg-white rounded-xl border border-gray-200 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Histórico do Ano {projectionData.projection.analysisYear}
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Mês</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Renda</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Despesas</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Investimentos</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Lazer</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Despesas</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Investimentos</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Lazer</th>
                      </tr>
                    </thead>
                    <tbody>
                      {projectionData.historicalData.map((data, index) => {
                        const adherence = projectionData.adherenceAnalysis[index]
                        return (
                          <tr key={`${data.year}-${data.month}`} className="border-b border-gray-100">
                            <td className="py-3 px-4">
                              {getMonthName(data.month)} {data.year}
                            </td>
                            <td className="py-3 px-4 text-right font-medium">
                              {formatCurrency(data.income)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.expenses)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.investments)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.leisure || 0)}
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.expenseAdherence.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.expenseAdherence.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.investmentAdherence.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.investmentAdherence.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.leisureAdherence?.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.leisureAdherence?.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Resumo da Análise */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Resumo da Análise
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Aderência aos Limites</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Despesas dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgExpenseAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Investimentos dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgInvestmentAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Lazer dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgLeisureAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Projeção Futura</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Despesas projetadas:</span>
                        <span className="font-medium">
                          {formatCurrency(projectionData.projection.projectedExpenses)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Investimentos projetados:</span>
                        <span className="font-medium">
                          {formatCurrency(projectionData.projection.projectedInvestments)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <AlertTriangle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Configuração necessária</h3>
              <p className="text-gray-600 mb-4">
                Para calcular a projeção, é necessário configurar pelo menos um relatório mensal do ano atual.
              </p>
              <p className="text-sm text-gray-500">
                Vá para a aba "Relatórios" e configure os limites percentuais para o mês atual.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GoalProjectionModal
