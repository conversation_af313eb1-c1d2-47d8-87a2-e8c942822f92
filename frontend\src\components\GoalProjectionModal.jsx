import React, { useState, useEffect } from 'react'
import { 
  X, 
  TrendingUp, 
  Calendar, 
  DollarSign, 
  Target,
  CheckCircle,
  AlertTriangle,
  BarChart3,
  Clock
} from 'lucide-react'
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  BarChart,
  Bar,
  Legend
} from 'recharts'
import goalService from '../services/goalService'
import toast from 'react-hot-toast'

const GoalProjectionModal = ({ goal, isOpen, onClose }) => {
  const [projectionData, setProjectionData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [optimalAccumulatedAmount, setTotalValueRecomended ] = useState(0)
  const [targetAccumulatedAmount, setTotalValueChosen] = useState(0)

  useEffect(() => {
    if (isOpen && goal) {
      fetchProjection()
    }
  }, [isOpen, goal, selectedYear, selectedMonth])

  const fetchProjection = async () => {
    try {
      setLoading(true)
      const data = await goalService.getGoalProjection(goal.id, selectedYear, selectedMonth)


      setProjectionData(data)
    } catch (error) {
      console.error('Erro ao buscar projeção:', error)
      toast.error(error.response?.data?.error || 'Erro ao calcular projeção')
    } finally {
      setLoading(false)
    }
  }

  // Função para calcular valores acumulados baseados no mês selecionado
  const getAccumulatedValuesForMonth = () => {
    if (!projectionData?.projection?.monthlyProjection) {
      return {
        optimalAccumulatedAmount: 0,
        targetAccumulatedAmount: 0
      }
    }

    // Calcular quantos meses se passaram desde o início da projeção até o mês selecionado
    const currentDate = new Date()
    const selectedDate = new Date(selectedYear, selectedMonth - 1, 1)
    const monthsDiff = Math.max(1, Math.ceil((selectedDate - currentDate) / (1000 * 60 * 60 * 24 * 30.44)))

    // Encontrar o valor acumulado para o mês correspondente na projeção
    const monthData = projectionData.projection.monthlyProjection.find(item => item.month === monthsDiff)

    if (monthData) {
      return {
        optimalAccumulatedAmount: monthData.optimalAccumulatedAmount || 0,
        targetAccumulatedAmount: monthData.targetAccumulatedAmount || 0
      }
    }

    // Se não encontrar o mês exato, calcular baseado nos valores mensais
    const optimalMonthly = projectionData.projection.monthlySavingsCapacity || 0
    const targetMonthly = projectionData.projection.monthlyAmountNeeded || 0

    return {
      optimalAccumulatedAmount: optimalMonthly * monthsDiff,
      targetAccumulatedAmount: targetMonthly * monthsDiff
    }
  }

  // Função para obter valores acumulados baseados no mês/ano selecionado
  const getCurrentMonthValues = () => {
    if (!projectionData?.projection?.monthlyProjection) {
      return {
        optimalAccumulatedAmount: 0,
        targetAccumulatedAmount: 0
      }
    }

    // Calcular quantos meses se passaram desde o início da projeção
    const currentDate = new Date()
    const selectedDate = new Date(selectedYear, selectedMonth - 1, 1)

    // Calcular diferença em meses
    const monthsDiff = Math.max(1,
      (selectedDate.getFullYear() - currentDate.getFullYear()) * 12 +
      (selectedDate.getMonth() - currentDate.getMonth()) + 1
    )

    // Buscar dados do mês correspondente na projeção
    const monthData = projectionData.projection.monthlyProjection.find(item => item.month === monthsDiff)

    if (monthData) {
      return {
        optimalAccumulatedAmount: monthData.optimalAccumulatedAmount || 0,
        targetAccumulatedAmount: monthData.targetAccumulatedAmount || 0
      }
    }

    // Se não encontrar, calcular baseado nos valores mensais (não alterar)
    const optimalMonthly = projectionData.projection.monthlySavingsCapacity || 0
    const targetMonthly = projectionData.projection.monthlyAmountNeeded || 0

    return {
      //não alterar
      optimalAccumulatedAmount: projectionData.goal.targetAmount,
      targetAccumulatedAmount: projectionData.goal.targetAmount
     // optimalAccumulatedAmount: Math.max(0, optimalMonthly * monthsDiff),
     // targetAccumulatedAmount: Math.max(0, targetMonthly * monthsDiff)
    }
  }
  const formatCurrency = (value) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(value || 0)
  }

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('pt-BR', {
      month: 'short',
      year: 'numeric'
    })
  }

  const getMonthName = (month) => {
    const months = [
      'Jan', 'Fev', 'Mar', 'Abr', 'Mai', 'Jun',
      'Jul', 'Ago', 'Set', 'Out', 'Nov', 'Dez'
    ]

    return months[month-1]
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-6xl max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          {/* Header com Imagem */}
          <div className="flex items-start justify-between mb-6">
            <div className="flex items-start gap-4 flex-1">
              {/* Imagem do Objetivo */}
              {goal?.imageUrl && (
                <div className="flex-shrink-0">
                  <img
                    src={goal.imageUrl}
                    alt={goal.name}
                    className="w-20 h-20 object-cover rounded-xl border-2 border-gray-200 shadow-sm"
                  />
                </div>
              )}

              {/* Informações do Objetivo */}
              <div className="flex-1">
                <h3 className="text-2xl font-semibold text-gray-900 flex items-center gap-2">
                  <Target className="h-6 w-6 text-blue-600" />
                  Projeção: {goal?.name}
                </h3>
                <p className="text-gray-600 mt-1">
                  Meta de {formatCurrency(goal?.targetAmount)} para {getMonthName(goal?.targetMonth)} de {goal?.targetYear}
                </p>
                {goal?.description && (
                  <p className="text-sm text-gray-500 mt-2 line-clamp-2">
                    {goal.description}
                  </p>
                )}
              </div>
            </div>

            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors flex-shrink-0"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Filtros de Análise - Melhorados */}
          <div className="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 overflow-hidden">
            <div className="p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
              <h4 className="font-semibold flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Período de Análise
              </h4>
              <p className="text-blue-100 text-sm mt-1">
                Selecione o período para calcular as recomendações mensais
              </p>
            </div>

            <div className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    📅 Ano de Referência
                  </label>
                  <div className="flex gap-2">
                    <select
                      value={selectedYear}
                      onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                      className="flex-1 px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
                    >
                      {Array.from({ length: 5 }, (_, i) => {
                        const year = new Date().getFullYear() - 2 + i
                        return (
                          <option key={year} value={year}>{year}</option>
                        )
                      })}
                    </select>
                    <input
                      type="number"
                      value={selectedYear}
                      onChange={(e) => {
                        const year = parseInt(e.target.value)
                        if (year >= 2020 && year <= 2030) {
                          setSelectedYear(year)
                        }
                      }}
                      min="2020"
                      max="2030"
                      className="w-24 px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm text-center"
                      placeholder="Ano"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <label className="block text-sm font-semibold text-gray-700">
                    📊 Mês Atual de Análise
                  </label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                    className="w-full px-3 py-2.5 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white shadow-sm"
                  >
                    {Array.from({ length: 12 }, (_, i) => (
                      <option key={i + 1} value={i + 1}>
                        {getMonthName(i + 1)}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                <div className="flex items-start gap-2">
                  <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span className="text-white text-xs font-bold">i</span>
                  </div>
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">Como funciona:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• <strong>Análise histórica:</strong> Baseada nos dados até o mês selecionado</li>
                      <li>• <strong>Recomendações:</strong> Calculadas para o mês atual escolhido</li>
                      <li>• <strong>Projeção:</strong> Mantém a data original do objetivo</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : projectionData ? (
            <div className="space-y-8">
              {/* Valores para o Mês Atual */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div className="bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl p-6 border border-emerald-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-emerald-100 rounded-xl flex items-center justify-center">
                      <TrendingUp className="h-6 w-6 text-emerald-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Valor Total Recomendado</h4>
                      <p className="text-sm text-gray-600">Para o mês atual</p>
                    </div>
                  </div>
                  <p className="text-3xl font-bold text-emerald-600 mb-2">
                    {formatCurrency(getCurrentMonthValues().optimalAccumulatedAmount)}
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                    <span className="text-emerald-700 font-medium">
                      Baseado na sua capacidade atual
                    </span>
                  </div>
                  <p className="text-xs text-emerald-600 mt-2">
                    💡 Este valor considera seus limites de poupança configurados
                  </p>
                </div>

                <div className="bg-gradient-to-r from-amber-50 to-orange-50 rounded-xl p-6 border border-amber-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-12 h-12 bg-amber-100 rounded-xl flex items-center justify-center">
                      <Target className="h-6 w-6 text-amber-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Valor Total Escolhido</h4>
                      <p className="text-sm text-gray-600">Para atingir a meta</p>
                    </div>
                  </div>
                  <p className="text-3xl font-bold text-amber-600 mb-2">
                    {formatCurrency(getCurrentMonthValues().targetAccumulatedAmount)}
                  </p>
                  <div className="flex items-center gap-2 text-sm">
                    <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                    <span className="text-amber-700 font-medium">
                      Necessário para atingir a meta na data
                    </span>
                  </div>
                  <p className="text-xs text-amber-600 mt-2">
                    🎯 Valor mensal para atingir {formatCurrency(goal?.targetAmount)} em {getMonthName(goal?.targetMonth)}/{goal?.targetYear}
                  </p>
                </div>
              </div>

              {/* Comparação Visual */}
              <div className="bg-white rounded-xl border border-gray-200 p-6 mb-8">
                <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <BarChart3 className="h-5 w-5 text-blue-600" />
                  Comparação de Valores Mensais
                </h4>

                <div className="space-y-4">
                  {/* Barra Recomendado */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-emerald-700">Valor Recomendado</span>
                      <span className="text-sm font-bold text-emerald-600">
                        {formatCurrency(getCurrentMonthValues().optimalAccumulatedAmount)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-emerald-500 to-green-500 h-3 rounded-full transition-all duration-500"
                        style={{
                          width: `${Math.min((getCurrentMonthValues().optimalAccumulatedAmount / Math.max(getCurrentMonthValues().optimalAccumulatedAmount, getCurrentMonthValues().targetAccumulatedAmount)) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>

                  {/* Barra Escolhido */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-amber-700">Valor Escolhido</span>
                      <span className="text-sm font-bold text-amber-600">
                        {formatCurrency(getCurrentMonthValues().targetAccumulatedAmount)}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div
                        className="bg-gradient-to-r from-amber-500 to-orange-500 h-3 rounded-full transition-all duration-500"
                        style={{
                          width: `${Math.min((getCurrentMonthValues().targetAccumulatedAmount / Math.max(getCurrentMonthValues().optimalAccumulatedAmount, getCurrentMonthValues().targetAccumulatedAmount)) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                  </div>

                  {/* Análise da Diferença */}
                  <div className={`p-3 rounded-lg border ${
                    getCurrentMonthValues().targetAccumulatedAmount <= getCurrentMonthValues().optimalAccumulatedAmount
                      ? 'bg-green-50 border-green-200'
                      : 'bg-yellow-50 border-yellow-200'
                  }`}>
                    <div className="flex items-center gap-2">
                      {getCurrentMonthValues().targetAccumulatedAmount <= getCurrentMonthValues().optimalAccumulatedAmount ? (
                        <CheckCircle className="h-4 w-4 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                      )}
                      <span className={`text-sm font-medium ${
                        getCurrentMonthValues().targetAccumulatedAmount <= getCurrentMonthValues().optimalAccumulatedAmount
                          ? 'text-green-700'
                          : 'text-yellow-700'
                      }`}>
                        {getCurrentMonthValues().targetAccumulatedAmount <= getCurrentMonthValues().optimalAccumulatedAmount
                          ? '✅ Meta viável com acúmulo atual'
                          : '⚠️ Meta desafiadora - acúmulo insuficiente'
                        }
                      </span>
                    </div>
                    <p className={`text-xs mt-1 ${
                      getCurrentMonthValues().targetAccumulatedAmount <= getCurrentMonthValues().optimalAccumulatedAmount
                        ? 'text-green-600'
                        : 'text-yellow-600'
                    }`}>
                      Diferença: {formatCurrency(Math.abs(getCurrentMonthValues().targetAccumulatedAmount - getCurrentMonthValues().optimalAccumulatedAmount))}
                      {getCurrentMonthValues().targetAccumulatedAmount > getCurrentMonthValues().optimalAccumulatedAmount
                        ? ' a mais que o acúmulo projetado'
                        : ' dentro do acúmulo projetado'
                      }
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      📅 Valores para {getMonthName(selectedMonth)}/{selectedYear}
                    </p>
                  </div>
                </div>
              </div>

              {/* Resumo da Projeção */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <DollarSign className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Renda Média</h4>
                      <p className="text-sm text-gray-600">Últimos 3 meses</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-blue-600">
                    {formatCurrency(projectionData.projection.avgIncome)}
                  </p>
                </div>

                <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 border border-green-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <TrendingUp className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Meta Recomendada</h4>
                      <p className="text-sm text-gray-600">Capacidade projetada</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-green-600">
                    {formatCurrency(projectionData.projection.monthlySavingsCapacity)}
                  </p>
                </div>

                <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 border border-purple-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <Clock className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Tempo Ótimo</h4>
                      <p className="text-sm text-gray-600">Baseado na capacidade</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-purple-600">
                    {projectionData.projection.monthsToGoalOptimal ?
                      `${projectionData.projection.monthsToGoalOptimal} meses` :
                      'Impossível'
                    }
                  </p>
                </div>

                <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 border border-orange-200">
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
                      <Target className="h-5 w-5 text-orange-600" />
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Meta Escolhida</h4>
                      <p className="text-sm text-gray-600">Valor mensal necessário</p>
                    </div>
                  </div>
                  <p className="text-2xl font-bold text-orange-600">
                    {formatCurrency(projectionData.projection.monthlyAmountNeeded)}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {projectionData.projection.monthsToTargetDate} meses restantes
                  </p>
                </div>

                <div className={`rounded-xl p-6 border ${
                  projectionData.projection.canAchieveTargetDate
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
                    : 'bg-gradient-to-r from-red-50 to-pink-50 border-red-200'
                }`}>
                  <div className="flex items-center gap-3 mb-3">
                    <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                      projectionData.projection.canAchieveTargetDate
                        ? 'bg-green-100'
                        : 'bg-red-100'
                    }`}>
                      {projectionData.projection.canAchieveTargetDate ? (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-600" />
                      )}
                    </div>
                    <div>
                      <h4 className="font-semibold text-gray-900">Viabilidade</h4>
                      <p className="text-sm text-gray-600">Meta escolhida</p>
                    </div>
                  </div>
                  <p className={`text-lg font-bold ${
                    projectionData.projection.canAchieveTargetDate
                      ? 'text-green-600'
                      : 'text-red-600'
                  }`}>
                    {projectionData.projection.canAchieveTargetDate ? 'Viável' : 'Desafiador'}
                  </p>
                  <p className="text-sm text-gray-600 mt-1">
                    {formatDate(projectionData.projection.targetDate)}
                  </p>
                </div>
              </div>

              {/* Gráfico de Projeção */}
              {projectionData.projection.monthlyProjection.length > 0 && (
                <div className="bg-white rounded-xl border border-gray-200 p-6">
                  <h4 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-blue-600" />
                    Projeção de Acúmulo Mensal - Comparativo
                  </h4>
                  <div className="mb-4 p-4 bg-gray-50 rounded-lg">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-green-500 rounded"></div>
                        <span><strong>Linha Verde:</strong> Projeção baseada na sua capacidade atual de poupança</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-4 h-4 bg-orange-500 rounded"></div>
                        <span><strong>Linha Laranja:</strong> Projeção para atingir a meta na data escolhida</span>
                      </div>
                    </div>
                  </div>
                  <div className="h-96">
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart data={projectionData.projection.monthlyProjection}>
                        <CartesianGrid strokeDasharray="3 3" stroke="#f1f5f9" />
                        <XAxis
                          dataKey="month"
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `Mês ${value}`}
                        />
                        <YAxis
                          tick={{ fontSize: 12 }}
                          tickFormatter={(value) => `R$ ${(value / 1000).toFixed(0)}k`}
                        />
                        <Tooltip
                          formatter={(value, name) => {
                            const labels = {
                              'optimalAccumulatedAmount': 'Acúmulo Ótimo',
                              'targetAccumulatedAmount': 'Acúmulo Meta',
                              'optimalMonthlyAmount': 'Valor Mensal Ótimo',
                              'targetMonthlyAmount': 'Valor Mensal Meta'
                            };
                            return [formatCurrency(value), labels[name] || name];
                          }}
                          labelFormatter={(value) => `Mês ${value}`}
                        />
                        <Line
                          type="monotone"
                          dataKey="optimalAccumulatedAmount"
                          stroke="#10b981"
                          strokeWidth={3}
                          dot={{ fill: '#10b981', strokeWidth: 2, r: 4 }}
                          name="Acúmulo Ótimo"
                        />
                        <Line
                          type="monotone"
                          dataKey="targetAccumulatedAmount"
                          stroke="#f97316"
                          strokeWidth={3}
                          dot={{ fill: '#f97316', strokeWidth: 2, r: 4 }}
                          name="Acúmulo Meta"
                        />
                        {/* Linha vertical para marcar a data da meta */}
                        {projectionData.projection.monthlyProjection.map((item, index) =>
                          item.isTargetMonth ? (
                            <Line
                              key={`target-line-${index}`}
                              type="monotone"
                              dataKey={() => goal.targetAmount}
                              stroke="#dc2626"
                              strokeWidth={2}
                              strokeDasharray="10 5"
                              dot={false}
                              name="Data Meta"
                            />
                          ) : null
                        )}
                      </LineChart>
                    </ResponsiveContainer>
                  </div>
                </div>
              )}

              {/* Histórico do Ano Atual */}
              <div className="bg-white rounded-xl border border-gray-200 p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Histórico do Ano {projectionData.projection.analysisYear}
                </h4>
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-200">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Mês</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Renda</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Despesas</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Investimentos</th>
                        <th className="text-right py-3 px-4 font-medium text-gray-900">Lazer</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Despesas</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Investimentos</th>
                        <th className="text-center py-3 px-4 font-medium text-gray-900">Status Lazer</th>
                      </tr>
                    </thead>
                    <tbody>
                      {projectionData.historicalData.map((data, index) => {
                        const adherence = projectionData.adherenceAnalysis[index]
                        return (
                          <tr key={`${data.year}-${data.month}`} className="border-b border-gray-100">
                            <td className="py-3 px-4">
                              {getMonthName(data.month)} {data.year}
                            </td>
                            <td className="py-3 px-4 text-right font-medium">
                              {formatCurrency(data.income)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.expenses)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.investments)}
                            </td>
                            <td className="py-3 px-4 text-right">
                              {formatCurrency(data.leisure || 0)}
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.expenseAdherence.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.expenseAdherence.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.investmentAdherence.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.investmentAdherence.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                            <td className="py-3 px-4 text-center">
                              <span className={`px-2 py-1 rounded-full text-xs ${
                                adherence.leisureAdherence?.withinLimit
                                  ? 'bg-green-100 text-green-700'
                                  : 'bg-red-100 text-red-700'
                              }`}>
                                {adherence.leisureAdherence?.withinLimit ? 'Dentro' : 'Fora'}
                              </span>
                            </td>
                          </tr>
                        )
                      })}
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Resumo da Análise */}
              <div className="bg-gray-50 rounded-xl p-6">
                <h4 className="text-lg font-semibold text-gray-900 mb-4">
                  Resumo da Análise
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Aderência aos Limites</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Despesas dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgExpenseAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Investimentos dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgInvestmentAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Lazer dentro do limite:</span>
                        <span className="font-medium">
                          {(projectionData.summary.avgLeisureAdherence * 100).toFixed(0)}%
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Projeção Futura</h5>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Despesas projetadas:</span>
                        <span className="font-medium">
                          {formatCurrency(projectionData.projection.projectedExpenses)}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span>Investimentos projetados:</span>
                        <span className="font-medium">
                          {formatCurrency(projectionData.projection.projectedInvestments)}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-12">
              <AlertTriangle className="h-16 w-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Configuração necessária</h3>
              <p className="text-gray-600 mb-4">
                Para calcular a projeção, é necessário configurar pelo menos um relatório mensal do ano atual.
              </p>
              <p className="text-sm text-gray-500">
                Vá para a aba "Relatórios" e configure os limites percentuais para o mês atual.
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GoalProjectionModal
