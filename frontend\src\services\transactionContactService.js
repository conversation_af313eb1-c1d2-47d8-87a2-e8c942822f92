import api from './api'

export const transactionContactService = {
  // Listar todos os contatos de transações
  async getContacts() {
    try {
      console.log('🔍 Chamando API: GET /contacts-tx')
      const response = await api.get('/contacts-tx')
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao buscar contatos de transações:', error)
      throw error
    }
  },

  // Buscar contato por ID
  async getContactById(id) {
    try {
      console.log(`🔍 Chamando API: GET /contacts-tx/${id}`)
      const response = await api.get(`/contacts-tx/${id}`)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao buscar contato:', error)
      throw error
    }
  },

  // Criar novo contato
  async createContact(contactData) {
    try {
      console.log('🔍 Chamando API: POST /contacts-tx', contactData)
      const response = await api.post('/contacts-tx', contactData)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao criar contato:', error)
      throw error
    }
  },

  // Atualizar contato
  async updateContact(id, contactData) {
    try {
      console.log(`🔍 Chamando API: PUT /contacts-tx/${id}`, contactData)
      const response = await api.put(`/contacts-tx/${id}`, contactData)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao atualizar contato:', error)
      throw error
    }
  },

  // Atualizar contato com foto (usando FormData)
  async updateContactWithPhoto(id, formData) {
    try {
      console.log(`🔍 Chamando API: PUT /contacts-tx/${id} (com foto)`)
      const response = await api.put(`/contacts-tx/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao atualizar contato com foto:', error)
      throw error
    }
  },

  // Criar contato com foto (usando FormData)
  async createContactWithPhoto(formData) {
    try {
      console.log('🔍 Chamando API: POST /contacts-tx (com foto)')
      const response = await api.post('/contacts-tx', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao criar contato com foto:', error)
      throw error
    }
  },

  // Deletar contato
  async deleteContact(id) {
    try {
      console.log(`🔍 Chamando API: DELETE /contacts-tx/${id}`)
      const response = await api.delete(`/contacts-tx/${id}`)
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao deletar contato:', error)
      throw error
    }
  },

  // Upload de foto do contato
  async uploadPhoto(id, photoFile) {
    try {
      console.log(`🔍 Chamando API: POST /contacts-tx/${id}/photo`)
      const formData = new FormData()
      formData.append('photo', photoFile)

      const response = await api.post(`/contacts-tx/${id}/photo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao fazer upload da foto:', error)
      throw error
    }
  },

  // Buscar estatísticas do contato
  async getContactStats(id, month, year) {
    try {
      const params = {}
      if (month) params.month = month
      if (year) params.year = year

      console.log(`🔍 Chamando API: GET /contacts-tx/${id}/stats`, params)
      const response = await api.get(`/contacts-tx/${id}/stats`, { params })
      console.log('✅ Resposta recebida:', response.data)
      return response.data
    } catch (error) {
      console.error('❌ Erro ao buscar estatísticas do contato:', error)
      throw error
    }
  }
}

export default transactionContactService
