const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');
const multer = require('multer');
const cloudinary = require('../config/cloudinary');

const router = express.Router();
const prisma = new PrismaClient();

// Configuração do multer para upload de arquivos
const upload = multer({
  storage: multer.memoryStorage(),
  limits: { fileSize: 10 * 1024 * 1024 }, // 10MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp', 'application/pdf'];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error('Apenas imagens e PDFs são permitidos'));
    }
  }
});

// Aplicar middleware de autenticação a todas as rotas
router.use(authenticateToken);

// Listar rotinas
router.get('/', async (req, res) => {
  try {
    const routines = await prisma.routine.findMany({
      where: { userId: req.user.id },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    res.json(routines);
  } catch (error) {
    console.error('Erro ao buscar rotinas:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar rotina
router.post('/', async (req, res) => {
  try {
    const { name, description, executionDay, isActive, items } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Nome é obrigatório' });
    }

    if (!executionDay || executionDay < 1 || executionDay > 31) {
      return res.status(400).json({ error: 'Dia de execução deve estar entre 1 e 31' });
    }

    const routine = await prisma.routine.create({
      data: {
        name,
        description,
        executionDay,
        isActive: isActive !== undefined ? isActive : true,
        userId: req.user.id,
        items: {
          create: items?.map(item => ({
            name: item.name,
            type: item.type,
            description: item.description,
            transactionContactId: item.transactionContactId,
            tags: item.tags && Array.isArray(item.tags) ? item.tags.join(',') : null,
            categoryId: item.categoryId,
            bankId: item.bankId
          })) || []
        }
      },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      }
    });

    res.status(201).json(routine);
  } catch (error) {
    console.error('Erro ao criar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar apenas o status da rotina (pausar/ativar)
router.patch('/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { isActive } = req.body;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    const updatedRoutine = await prisma.routine.update({
      where: { id },
      data: { isActive },
      include: {
        items: {
          include: {
            category: true,
            bank: true
          }
        }
      }
    });

    res.json(updatedRoutine);
  } catch (error) {
    console.error('Erro ao atualizar status da rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar rotina completa
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, description, executionDay, isActive, items } = req.body;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    // Atualizar rotina e seus itens
    const updatedRoutine = await prisma.$transaction(async (prisma) => {
      // Deletar itens existentes
      await prisma.routineItem.deleteMany({
        where: { routineId: id }
      });

      // Atualizar rotina
      const updated = await prisma.routine.update({
        where: { id },
        data: {
          name: name || routine.name,
          description: description !== undefined ? description : routine.description,
          executionDay: executionDay || routine.executionDay,
          isActive: isActive !== undefined ? isActive : routine.isActive,
          items: {
            create: items?.map(item => ({
              name: item.name,
              type: item.type,
              description: item.description,
              transactionContactId: item.transactionContactId,
              tags: item.tags && Array.isArray(item.tags) ? item.tags.join(',') : null,
              categoryId: item.categoryId,
              bankId: item.bankId
            })) || []
          }
        },
        include: {
          items: {
            include: {
              category: true,
              bank: true,
              transactionContact: true
            }
          }
        }
      });

      return updated;
    });

    res.json(updatedRoutine);
  } catch (error) {
    console.error('Erro ao atualizar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Deletar rotina
router.delete('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    await prisma.routine.delete({
      where: { id }
    });

    res.json({ message: 'Rotina deletada com sucesso' });
  } catch (error) {
    console.error('Erro ao deletar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Executar rotina (criar transações com valores informados)
router.post('/:id/execute', upload.any(), async (req, res) => {
  try {
    const { id } = req.params;

    // Parse itemValues se vier como string (FormData)
    let itemValues;
    try {
      itemValues = typeof req.body.itemValues === 'string'
        ? JSON.parse(req.body.itemValues)
        : req.body.itemValues;
    } catch (e) {
      return res.status(400).json({ error: 'Formato inválido para itemValues' });
    }

    console.log('📋 Item values recebidos:', itemValues);

    const routine = await prisma.routine.findFirst({
      where: { id, userId: req.user.id },
      include: {
        items: {
          include: {
            category: true,
            bank: true,
            transactionContact: true
          }
        }
      }
    });

    if (!routine) {
      return res.status(404).json({ error: 'Rotina não encontrada' });
    }

    if (!routine.isActive) {
      return res.status(400).json({ error: 'Rotina está inativa' });
    }

    if (!routine.items || routine.items.length === 0) {
      return res.status(400).json({ error: 'Rotina não possui itens para executar' });
    }

    if (!itemValues || itemValues.length === 0) {
      return res.status(400).json({ error: 'Valores dos itens são obrigatórios' });
    }

    // Criar transações para cada item da rotina
    const transactions = await prisma.$transaction(async (prisma) => {
      const createdTransactions = [];

      for (const item of routine.items) {
        const itemValue = itemValues.find(v => v.itemId === item.id);
        if (!itemValue || !itemValue.amount) {
          continue; // Pular itens sem valor
        }

        // Processar comprovante se fornecido
        let receiptUrl = null;
        const itemFile = req.files?.find(file => file.fieldname === `receipt_${item.id}`);

        console.log('🔍 Procurando arquivo para item:', item.id);
        console.log('📁 Arquivos recebidos:', req.files?.map(f => ({ fieldname: f.fieldname, originalname: f.originalname })));

        if (itemFile) {
          try {
            const result = await new Promise((resolve, reject) => {
              cloudinary.uploader.upload_stream(
                {
                  resource_type: 'auto',
                  folder: 'routine_receipts'
                },
                (error, result) => {
                  if (error) reject(error);
                  else resolve(result);
                }
              ).end(itemFile.buffer);
            });
            receiptUrl = result.secure_url;
          } catch (uploadError) {
            console.error('Erro no upload do comprovante:', uploadError);
          }
        }

        // Usar inteligência de data/hora
        const now = new Date();
        const today = new Date().toDateString();
        const transactionDate = now.toDateString() === today ? now : new Date(now.setHours(0, 0, 0, 0));

        const transaction = await prisma.transaction.create({
          data: {
            description: item.name, // Apenas o nome do item, sem o nome da rotina
            amount: Math.abs(itemValue.amount),
            type: item.type,
            date: transactionDate,
            categoryId: item.categoryId,
            bankId: item.bankId,
            transactionContactId: item.transactionContactId,
            userId: req.user.id,
            receiptUrl: receiptUrl,
            isFromRoutine: true // Flag para indicar que veio de rotina
          },
          include: {
            category: true,
            bank: true,
            transactionContact: true
          }
        });

        // Conectar tags se existirem no item da rotina
        if (item.tags) {
          const tagIds = item.tags.split(',').map(id => id.trim()).filter(id => id);
          if (tagIds.length > 0) {
            await prisma.transaction.update({
              where: { id: transaction.id },
              data: {
                tags: {
                  connect: tagIds.map(tagId => ({ id: tagId }))
                }
              }
            });
          }
        }

        // Atualizar saldo do banco
        if (item.bank) {
          const balanceChange = item.type === 'INCOME' ? itemValue.amount : -itemValue.amount;
          await prisma.bank.update({
            where: { id: item.bankId },
            data: {
              currentBalance: {
                increment: balanceChange
              }
            }
          });
        }

        createdTransactions.push(transaction);
      }

      return createdTransactions;
    });

    res.json({
      message: 'Rotina executada com sucesso',
      transactions,
      count: transactions.length
    });
  } catch (error) {
    console.error('Erro ao executar rotina:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter próximas execuções
router.get('/upcoming', async (req, res) => {
  try {
    const routines = await prisma.routine.findMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      include: {
        items: true
      }
    });

    const now = new Date();
    const upcoming = routines.map(routine => {
      const nextExecution = new Date(now.getFullYear(), now.getMonth(), routine.executionDay);
      
      if (nextExecution <= now) {
        nextExecution.setMonth(nextExecution.getMonth() + 1);
      }

      return {
        ...routine,
        nextExecution,
        daysUntilExecution: Math.ceil((nextExecution - now) / (1000 * 60 * 60 * 24))
      };
    }).sort((a, b) => a.nextExecution - b.nextExecution);

    res.json(upcoming);
  } catch (error) {
    console.error('Erro ao buscar próximas execuções:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
