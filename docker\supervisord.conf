# 🔧 SARA - Configuração do Supervisor
[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid
childlogdir=/var/log/supervisor
loglevel=info

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

# ================================
# Nginx - Servidor Web
# ================================
[program:nginx]
command=nginx -g "daemon off;"
autostart=true
autorestart=true
startretries=3
user=root
stdout_logfile=/var/log/supervisor/nginx.log
stderr_logfile=/var/log/supervisor/nginx_error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=3
stderr_logfile_backups=3

# ================================
# Backend Node.js - API
# ================================
[program:backend]
command=node server.js
directory=/app/backend
autostart=true
autorestart=true
startretries=5
user=sara
environment=NODE_ENV=production,PORT=3001
stdout_logfile=/var/log/supervisor/backend.log
stderr_logfile=/var/log/supervisor/backend_error.log
stdout_logfile_maxbytes=10MB
stderr_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile_backups=5
stopwaitsecs=10
killasgroup=true
stopasgroup=true

# ================================
# Auto Deploy - Monitoramento Git
# ================================
[program:auto-deploy]
command=/auto-deploy.sh
autostart=true
autorestart=true
startretries=3
user=root
environment=HOME="/root"
stdout_logfile=/var/log/supervisor/auto-deploy.log
stderr_logfile=/var/log/supervisor/auto-deploy_error.log
stdout_logfile_maxbytes=5MB
stderr_logfile_maxbytes=5MB
stdout_logfile_backups=3
stderr_logfile_backups=3

# ================================
# Database Backup - Backup Automático
# ================================
[program:db-backup]
command=sh -c 'while true; do sleep 86400; cp /app/backend/production.db /app/logs/backup_$(date +%%Y%%m%%d_%%H%%M%%S).db; find /app/logs -name "backup_*.db" -mtime +7 -delete; done'
autostart=true
autorestart=true
startretries=3
user=sara
stdout_logfile=/var/log/supervisor/backup.log
stderr_logfile=/var/log/supervisor/backup_error.log
stdout_logfile_maxbytes=1MB
stderr_logfile_maxbytes=1MB
stdout_logfile_backups=2
stderr_logfile_backups=2

# ================================
# Health Monitor - Monitoramento de Saúde
# ================================
[program:health-monitor]
command=sh -c 'while true; do sleep 60; curl -f http://localhost/api/health > /dev/null 2>&1 || echo "$(date): Health check failed" >> /var/log/supervisor/health.log; done'
autostart=true
autorestart=true
startretries=3
user=sara
stdout_logfile=/dev/null
stderr_logfile=/var/log/supervisor/health-monitor_error.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=2

# ================================
# Log Rotation - Rotação de Logs
# ================================
[program:log-rotation]
command=sh -c 'while true; do sleep 3600; find /var/log/supervisor -name "*.log" -size +50M -exec truncate -s 10M {} \;; done'
autostart=true
autorestart=true
startretries=3
user=root
stdout_logfile=/dev/null
stderr_logfile=/var/log/supervisor/log-rotation_error.log
stdout_logfile_maxbytes=0
stderr_logfile_maxbytes=1MB
stderr_logfile_backups=1

# ================================
# Configurações de Grupo
# ================================
[group:sara-core]
programs=nginx,backend
priority=999

[group:sara-monitoring]
programs=auto-deploy,health-monitor
priority=998

[group:sara-maintenance]
programs=db-backup,log-rotation
priority=997
