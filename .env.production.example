# 🐳 SARA - Configuração para Deploy em Produção

# ================================
# Configurações Obrigatórias
# ================================

# JWT Secret (OBRIGATÓRIO - Gere uma chave segura!)
JWT_SECRET=seu_jwt_secret_super_seguro_aqui_mude_em_producao

# Cloudinary (OBRIGATÓRIO - Configure sua conta)
CLOUDINARY_CLOUD_NAME="daek91fl7"
CLOUDINARY_API_KEY="944574467819265"
CLOUDINARY_API_SECRET="KxvwQA1k_uc79yvoTx_awlMClqg"

# ================================
# Configurações do Git
# ================================

# Repositório Git (OBRIGATÓRIO para auto-deploy)
GIT_REPO=https://github.com/daniel-shaulin/sara.git

# Branch para deploy (padrão: main)
GIT_BRANCH=augment

# ================================
# Configurações de Deploy
# ================================

# Ativar auto-deploy (true/false)
AUTO_DEPLOY=true

# Intervalo de verificação em segundos (padrão: 300 = 5 minutos)
DEPLOY_INTERVAL=300

# Webhook para notificações (opcional)
WEBHOOK_URL=https://hooks.slack.com/services/...
WEBHOOK_SECRET=seu_webhook_secret

# ================================
# Configurações de Domínio
# ================================

# Domínio principal (para SSL)
DOMAIN=sara.seudominio.com

# URL do frontend
FRONTEND_URL=https://sara.seudominio.com

# Email para Let's Encrypt
ACME_EMAIL=<EMAIL>

# ================================
# Configurações de Dados
# ================================

# Caminho para dados persistentes
DATA_PATH=./data

# Intervalo de backup em segundos (padrão: 86400 = 24 horas)
BACKUP_INTERVAL=86400

# Retenção de backups em dias (padrão: 7 dias)
BACKUP_RETENTION=7
