#!/bin/bash
# 🚀 SARA - Script de Deploy Automático

set -e

# ================================
# Configurações
# ================================
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$SCRIPT_DIR/deploy.log"

# ================================
# Cores para output
# ================================
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# ================================
# Funções Auxiliares
# ================================
log() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[$(date '+%Y-%m-%d %H:%M:%S')] ERROR:${NC} $1" | tee -a "$LOG_FILE"
}

warning() {
    echo -e "${YELLOW}[$(date '+%Y-%m-%d %H:%M:%S')] WARNING:${NC} $1" | tee -a "$LOG_FILE"
}

info() {
    echo -e "${BLUE}[$(date '+%Y-%m-%d %H:%M:%S')] INFO:${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[$(date '+%Y-%m-%d %H:%M:%S')] SUCCESS:${NC} $1" | tee -a "$LOG_FILE"
}

# ================================
# Banner
# ================================
show_banner() {
    echo -e "${PURPLE}"
    cat << "EOF"
    ███████╗ █████╗ ██████╗  █████╗ 
    ██╔════╝██╔══██╗██╔══██╗██╔══██╗
    ███████╗███████║██████╔╝███████║
    ╚════██║██╔══██║██╔══██╗██╔══██║
    ███████║██║  ██║██║  ██║██║  ██║
    ╚══════╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝
    
    Sistema de Acompanhamento de Recursos e Aplicações
    🚀 Script de Deploy Automático v1.0
EOF
    echo -e "${NC}"
}

# ================================
# Verificar Pré-requisitos
# ================================
check_prerequisites() {
    log "🔍 Verificando pré-requisitos..."
    
    # Docker
    if ! command -v docker &> /dev/null; then
        error "Docker não encontrado! Instale o Docker primeiro."
        exit 1
    fi
    
    # Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose não encontrado! Instale o Docker Compose primeiro."
        exit 1
    fi
    
    # Git
    if ! command -v git &> /dev/null; then
        error "Git não encontrado! Instale o Git primeiro."
        exit 1
    fi
    
    success "✅ Pré-requisitos verificados"
}

# ================================
# Configurar Ambiente
# ================================
setup_environment() {
    log "⚙️ Configurando ambiente..."
    
    # Criar diretórios necessários
    mkdir -p data/{database,logs,backups}
    mkdir -p docker
    
    # Verificar se .env existe
    if [ ! -f ".env" ]; then
        if [ -f ".env.production.example" ]; then
            cp .env.production.example .env
            warning "⚠️ Arquivo .env criado a partir do exemplo"
            warning "⚠️ CONFIGURE AS VARIÁVEIS OBRIGATÓRIAS antes de continuar!"
            
            echo -e "${YELLOW}"
            echo "Variáveis obrigatórias para configurar:"
            echo "- JWT_SECRET (gere uma chave segura)"
            echo "- CLOUDINARY_CLOUD_NAME"
            echo "- CLOUDINARY_API_KEY" 
            echo "- CLOUDINARY_API_SECRET"
            echo "- GIT_REPO (seu repositório)"
            echo -e "${NC}"
            
            read -p "Pressione Enter após configurar o arquivo .env..."
        else
            error "Arquivo .env.production.example não encontrado!"
            exit 1
        fi
    fi
    
    success "✅ Ambiente configurado"
}

# ================================
# Validar Configuração
# ================================
validate_config() {
    log "🔍 Validando configuração..."
    
    # Carregar variáveis
    if [ -f ".env" ]; then
        source .env
    fi
    
    # Verificar variáveis obrigatórias
    local required_vars=("JWT_SECRET" "CLOUDINARY_CLOUD_NAME" "CLOUDINARY_API_KEY" "CLOUDINARY_API_SECRET")
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ] || [ "${!var}" = "seu_jwt_secret_super_seguro_aqui_mude_em_producao" ] || [[ "${!var}" == *"seu_"* ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        error "Variáveis obrigatórias não configuradas:"
        for var in "${missing_vars[@]}"; do
            error "  - $var"
        done
        exit 1
    fi
    
    success "✅ Configuração validada"
}

# ================================
# Build e Deploy
# ================================
deploy_application() {
    log "🚀 Iniciando deploy da aplicação..."
    
    # Parar containers existentes
    if docker-compose ps | grep -q "sara-app"; then
        log "🛑 Parando containers existentes..."
        docker-compose down
    fi
    
    # Build da aplicação
    log "🏗️ Buildando aplicação..."
    docker-compose build --no-cache sara-app
    
    # Subir aplicação
    log "🚀 Subindo aplicação..."
    docker-compose up -d sara-app
    
    # Aguardar inicialização
    log "⏳ Aguardando inicialização..."
    sleep 30
    
    # Verificar saúde
    local max_attempts=12
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f http://localhost/health > /dev/null 2>&1; then
            success "✅ Aplicação iniciada com sucesso!"
            break
        fi
        
        info "Tentativa $attempt/$max_attempts - Aguardando aplicação..."
        sleep 10
        ((attempt++))
    done
    
    if [ $attempt -gt $max_attempts ]; then
        error "❌ Falha na inicialização da aplicação"
        docker-compose logs sara-app
        exit 1
    fi
}

# ================================
# Configurar SSL (Opcional)
# ================================
setup_ssl() {
    if [ -n "$DOMAIN" ] && [ "$DOMAIN" != "sara.seudominio.com" ]; then
        log "🔒 Configurando SSL para $DOMAIN..."
        
        # Verificar se domínio aponta para o servidor
        local server_ip=$(curl -s ifconfig.me)
        local domain_ip=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}')
        
        if [ "$server_ip" = "$domain_ip" ]; then
            log "🌐 Subindo Traefik para SSL automático..."
            docker-compose --profile traefik up -d
            
            # Aguardar certificado
            log "⏳ Aguardando certificado SSL..."
            sleep 60
            
            if curl -f https://$DOMAIN/health > /dev/null 2>&1; then
                success "✅ SSL configurado com sucesso!"
                info "🌐 Aplicação disponível em: https://$DOMAIN"
            else
                warning "⚠️ SSL pode não estar funcionando corretamente"
                info "🌐 Aplicação disponível em: http://$DOMAIN"
            fi
        else
            warning "⚠️ Domínio $DOMAIN não aponta para este servidor"
            warning "⚠️ Configure o DNS antes de usar SSL"
        fi
    else
        info "🌐 Aplicação disponível em: http://localhost"
    fi
}

# ================================
# Configurar Backup (Opcional)
# ================================
setup_backup() {
    if [ "$ENABLE_BACKUP" = "true" ]; then
        log "💾 Configurando backup automático..."
        docker-compose --profile backup up -d
        success "✅ Backup automático configurado"
    fi
}

# ================================
# Mostrar Informações Finais
# ================================
show_final_info() {
    echo -e "${GREEN}"
    cat << "EOF"
    
    🎉 DEPLOY CONCLUÍDO COM SUCESSO!
    
EOF
    echo -e "${NC}"
    
    echo -e "${CYAN}📋 Informações da Aplicação:${NC}"
    echo -e "   🌐 Frontend: ${GREEN}http://localhost${NC}"
    echo -e "   🔌 Backend:  ${GREEN}http://localhost:3001${NC}"
    echo -e "   👤 Login:    ${GREEN}<EMAIL>${NC}"
    echo -e "   🔑 Senha:    ${GREEN}123456${NC}"
    
    if [ -n "$DOMAIN" ] && [ "$DOMAIN" != "sara.seudominio.com" ]; then
        echo -e "   🌍 Domínio:  ${GREEN}https://$DOMAIN${NC}"
    fi
    
    echo ""
    echo -e "${CYAN}🔧 Comandos Úteis:${NC}"
    echo -e "   📊 Status:       ${YELLOW}docker-compose ps${NC}"
    echo -e "   📋 Logs:         ${YELLOW}docker-compose logs -f sara-app${NC}"
    echo -e "   🔄 Reiniciar:    ${YELLOW}docker-compose restart sara-app${NC}"
    echo -e "   🛑 Parar:        ${YELLOW}docker-compose down${NC}"
    echo -e "   💾 Backup:       ${YELLOW}docker-compose exec sara-app cp /app/backend/production.db /app/logs/backup_manual_\$(date +%Y%m%d_%H%M%S).db${NC}"
    
    echo ""
    echo -e "${CYAN}📁 Arquivos Importantes:${NC}"
    echo -e "   ⚙️ Configuração: ${YELLOW}.env${NC}"
    echo -e "   📊 Banco:        ${YELLOW}data/database/production.db${NC}"
    echo -e "   📋 Logs:         ${YELLOW}data/logs/${NC}"
    echo -e "   💾 Backups:      ${YELLOW}data/backups/${NC}"
    
    echo ""
    echo -e "${RED}⚠️ IMPORTANTE:${NC}"
    echo -e "   🔑 Altere a senha padrão após o primeiro login!"
    echo -e "   🔒 Configure um JWT_SECRET seguro em produção!"
    echo -e "   💾 Configure backups externos para dados críticos!"
    
    echo ""
}

# ================================
# Menu Principal
# ================================
main_menu() {
    while true; do
        echo -e "${CYAN}"
        echo "🚀 SARA Deploy - Escolha uma opção:"
        echo "1) Deploy Completo (Recomendado)"
        echo "2) Deploy Básico (Apenas aplicação)"
        echo "3) Deploy com SSL (Traefik)"
        echo "4) Deploy com Backup"
        echo "5) Verificar Status"
        echo "6) Ver Logs"
        echo "7) Parar Aplicação"
        echo "8) Sair"
        echo -e "${NC}"
        
        read -p "Opção: " choice
        
        case $choice in
            1)
                deploy_complete
                break
                ;;
            2)
                deploy_basic
                break
                ;;
            3)
                deploy_with_ssl
                break
                ;;
            4)
                deploy_with_backup
                break
                ;;
            5)
                show_status
                ;;
            6)
                show_logs
                ;;
            7)
                stop_application
                ;;
            8)
                exit 0
                ;;
            *)
                error "Opção inválida!"
                ;;
        esac
    done
}

# ================================
# Opções de Deploy
# ================================
deploy_complete() {
    log "🚀 Iniciando deploy completo..."
    check_prerequisites
    setup_environment
    validate_config
    deploy_application
    setup_ssl
    setup_backup
    show_final_info
}

deploy_basic() {
    log "🚀 Iniciando deploy básico..."
    check_prerequisites
    setup_environment
    validate_config
    deploy_application
    show_final_info
}

deploy_with_ssl() {
    log "🚀 Iniciando deploy com SSL..."
    check_prerequisites
    setup_environment
    validate_config
    deploy_application
    setup_ssl
    show_final_info
}

deploy_with_backup() {
    log "🚀 Iniciando deploy com backup..."
    check_prerequisites
    setup_environment
    validate_config
    deploy_application
    setup_backup
    show_final_info
}

show_status() {
    log "📊 Status da aplicação:"
    docker-compose ps
    echo ""
    docker-compose exec sara-app supervisorctl status 2>/dev/null || echo "Aplicação não está rodando"
}

show_logs() {
    log "📋 Logs da aplicação:"
    docker-compose logs --tail=50 sara-app
}

stop_application() {
    log "🛑 Parando aplicação..."
    docker-compose down
    success "✅ Aplicação parada"
}

# ================================
# Execução Principal
# ================================
main() {
    show_banner
    
    # Verificar se é execução direta ou com parâmetros
    if [ $# -eq 0 ]; then
        main_menu
    else
        case $1 in
            "deploy")
                deploy_complete
                ;;
            "basic")
                deploy_basic
                ;;
            "ssl")
                deploy_with_ssl
                ;;
            "backup")
                deploy_with_backup
                ;;
            "status")
                show_status
                ;;
            "logs")
                show_logs
                ;;
            "stop")
                stop_application
                ;;
            *)
                echo "Uso: $0 [deploy|basic|ssl|backup|status|logs|stop]"
                exit 1
                ;;
        esac
    fi
}

# ================================
# Tratamento de Sinais
# ================================
trap 'echo -e "\n${RED}Deploy interrompido pelo usuário${NC}"; exit 1' SIGINT SIGTERM

# ================================
# Executar
# ================================
main "$@"
