const express = require('express');
const { PrismaClient } = require('@prisma/client');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();
const prisma = new PrismaClient();

// Middleware de autenticação para todas as rotas
router.use(authenticateToken);

// Obter meta financeira atual do usuário
router.get('/', async (req, res) => {
  try {
    let goal = await prisma.financialGoal.findFirst({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      orderBy: { createdAt: 'desc' }
    });

    // Se não existe meta, criar uma padrão
    if (!goal) {
      goal = await prisma.financialGoal.create({
        data: {
          userId: req.user.id,
          expensePercentage: 50,
          savingsPercentage: 20,
          leisurePercentage: 30,
          isActive: true
        }
      });
    }

    res.json(goal);
  } catch (error) {
    console.error('Erro ao buscar meta financeira:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Criar ou atualizar meta financeira
router.post('/', async (req, res) => {
  try {
    const { 
      expensePercentage, 
      savingsPercentage, 
      leisurePercentage, 
      monthlyIncome 
    } = req.body;

    // Validar que as porcentagens somam 100%
    const totalPercentage = expensePercentage + savingsPercentage + leisurePercentage;
    if (Math.abs(totalPercentage - 100) > 0.01) {
      return res.status(400).json({ 
        error: 'As porcentagens devem somar 100%',
        currentTotal: totalPercentage
      });
    }

    // Validar valores
    if (expensePercentage < 0 || savingsPercentage < 0 || leisurePercentage < 0) {
      return res.status(400).json({ error: 'Porcentagens não podem ser negativas' });
    }

    if (monthlyIncome && monthlyIncome < 0) {
      return res.status(400).json({ error: 'Renda mensal não pode ser negativa' });
    }

    // Desativar meta anterior
    await prisma.financialGoal.updateMany({
      where: { 
        userId: req.user.id,
        isActive: true
      },
      data: { isActive: false }
    });

    // Criar nova meta
    const goal = await prisma.financialGoal.create({
      data: {
        userId: req.user.id,
        expensePercentage,
        savingsPercentage,
        leisurePercentage,
        monthlyIncome: monthlyIncome || null,
        isActive: true
      }
    });

    res.json(goal);
  } catch (error) {
    console.error('Erro ao criar meta financeira:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Atualizar meta financeira existente
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      expensePercentage, 
      savingsPercentage, 
      leisurePercentage, 
      monthlyIncome 
    } = req.body;

    // Verificar se a meta pertence ao usuário
    const existingGoal = await prisma.financialGoal.findFirst({
      where: { id, userId: req.user.id }
    });

    if (!existingGoal) {
      return res.status(404).json({ error: 'Meta financeira não encontrada' });
    }

    // Validar que as porcentagens somam 100%
    const totalPercentage = expensePercentage + savingsPercentage + leisurePercentage;
    if (Math.abs(totalPercentage - 100) > 0.01) {
      return res.status(400).json({ 
        error: 'As porcentagens devem somar 100%',
        currentTotal: totalPercentage
      });
    }

    // Atualizar meta
    const goal = await prisma.financialGoal.update({
      where: { id },
      data: {
        expensePercentage,
        savingsPercentage,
        leisurePercentage,
        monthlyIncome: monthlyIncome || null
      }
    });

    res.json(goal);
  } catch (error) {
    console.error('Erro ao atualizar meta financeira:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

// Obter análise de performance vs metas
router.get('/analysis', async (req, res) => {
  try {
    const { year = new Date().getFullYear(), month = new Date().getMonth() + 1 } = req.query;

    // Buscar meta ativa
    const goal = await prisma.financialGoal.findFirst({
      where: { 
        userId: req.user.id,
        isActive: true
      }
    });

    if (!goal) {
      return res.status(404).json({ error: 'Nenhuma meta financeira encontrada' });
    }

    // Buscar transações do mês
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59);

    const transactions = await prisma.transaction.findMany({
      where: {
        userId: req.user.id,
        date: {
          gte: startOfMonth,
          lte: endOfMonth
        }
      },
      include: {
        category: true
      }
    });

    // Calcular totais
    const totalIncome = transactions
      .filter(t => t.type === 'INCOME')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalExpenses = transactions
      .filter(t => t.type === 'EXPENSE')
      .reduce((sum, t) => sum + t.amount, 0);

    const totalInvestments = transactions
      .filter(t => t.type === 'INVESTMENT')
      .reduce((sum, t) => sum + t.amount, 0);

    // Categorizar despesas (assumindo que lazer são categorias específicas)
    const leisureCategories = ['Lazer', 'Entretenimento', 'Viagem', 'Restaurante'];
    const leisureExpenses = transactions
      .filter(t => t.type === 'EXPENSE' && t.category && 
        leisureCategories.some(cat => t.category.name.toLowerCase().includes(cat.toLowerCase())))
      .reduce((sum, t) => sum + t.amount, 0);

    const essentialExpenses = totalExpenses - leisureExpenses;
    const savings = totalInvestments; // Considerando investimentos como poupança

    // Usar renda da meta ou renda real
    const baseIncome = goal.monthlyIncome || totalIncome;

    // Calcular porcentagens atuais
    const currentExpensePercentage = baseIncome > 0 ? (essentialExpenses / baseIncome) * 100 : 0;
    const currentSavingsPercentage = baseIncome > 0 ? (savings / baseIncome) * 100 : 0;
    const currentLeisurePercentage = baseIncome > 0 ? (leisureExpenses / baseIncome) * 100 : 0;

    // Calcular score do esverdeômetro (0-100)
    const expenseScore = Math.max(0, 100 - Math.abs(currentExpensePercentage - goal.expensePercentage) * 2);
    const savingsScore = Math.max(0, 100 - Math.abs(currentSavingsPercentage - goal.savingsPercentage) * 2);
    const leisureScore = Math.max(0, 100 - Math.abs(currentLeisurePercentage - goal.leisurePercentage) * 2);
    
    const overallScore = (expenseScore + savingsScore + leisureScore) / 3;

    res.json({
      goal,
      current: {
        income: totalIncome,
        baseIncome,
        essentialExpenses,
        leisureExpenses,
        savings,
        expensePercentage: currentExpensePercentage,
        savingsPercentage: currentSavingsPercentage,
        leisurePercentage: currentLeisurePercentage
      },
      scores: {
        expense: expenseScore,
        savings: savingsScore,
        leisure: leisureScore,
        overall: overallScore
      },
      period: {
        year: parseInt(year),
        month: parseInt(month),
        monthName: new Date(year, month - 1).toLocaleDateString('pt-BR', { month: 'long' })
      }
    });

  } catch (error) {
    console.error('Erro ao analisar performance:', error);
    res.status(500).json({ error: 'Erro interno do servidor' });
  }
});

module.exports = router;
